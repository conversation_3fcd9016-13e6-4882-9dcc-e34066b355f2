syntax = "v1"

info(
	title: "旅游服务"
	desc: "旅游服务"
	author: "<PERSON><PERSON><PERSON>"
	email: "<EMAIL>"
	version: "v1"
)

import (
	"order/order.api"
)

//========================> order v1 <========================
//need login
@server(
	prefix: order/v1
	group: homestayOrder
	jwt: JwtAuth
)
service order {
	
	@doc "创建民宿订单"
	@handler createHomestayOrder
	post /homestayOrder/createHomestayOrder (CreateHomestayOrderReq) returns (CreateHomestayOrderResp)
	
	@doc "用户订单列表"
	@handler userHomestayOrderList
	post /homestayOrder/userHomestayOrderList (UserHomestayOrderListReq) returns (UserHomestayOrderListResp)
	
	@doc "用户订单明细"
	@handler userHomestayOrderDetail
	post /homestayOrder/userHomestayOrderDetail (UserHomestayOrderDetailReq) returns (UserHomestayOrderDetailResp)
}