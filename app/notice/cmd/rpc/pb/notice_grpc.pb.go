// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.2
// source: notice.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Notice_NoticeLotteryDraw_FullMethodName             = "/pb.notice/NoticeLotteryDraw"
	Notice_GetNoticeSubscribePreference_FullMethodName  = "/pb.notice/GetNoticeSubscribePreference"
	Notice_SaveNoticeSubscribePreference_FullMethodName = "/pb.notice/SaveNoticeSubscribePreference"
	Notice_NoticeLotteryStart_FullMethodName            = "/pb.notice/NoticeLotteryStart"
	Notice_NoticeWishSign_FullMethodName                = "/pb.notice/NoticeWishSign"
)

// NoticeClient is the client API for Notice service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NoticeClient interface {
	NoticeLotteryDraw(ctx context.Context, in *NoticeLotteryDrawReq, opts ...grpc.CallOption) (*NoticeLotteryDrawResp, error)
	GetNoticeSubscribePreference(ctx context.Context, in *GetNoticeSubscribePreferenceReq, opts ...grpc.CallOption) (*GetNoticeSubscribePreferenceResp, error)
	SaveNoticeSubscribePreference(ctx context.Context, in *SaveNoticeSubscribePreferenceReq, opts ...grpc.CallOption) (*SaveNoticeSubscribePreferenceResp, error)
	NoticeLotteryStart(ctx context.Context, in *NoticeLotteryStartReq, opts ...grpc.CallOption) (*NoticeLotteryStartResp, error)
	NoticeWishSign(ctx context.Context, in *NoticeWishSignInReq, opts ...grpc.CallOption) (*NoticeWishSignInResp, error)
}

type noticeClient struct {
	cc grpc.ClientConnInterface
}

func NewNoticeClient(cc grpc.ClientConnInterface) NoticeClient {
	return &noticeClient{cc}
}

func (c *noticeClient) NoticeLotteryDraw(ctx context.Context, in *NoticeLotteryDrawReq, opts ...grpc.CallOption) (*NoticeLotteryDrawResp, error) {
	out := new(NoticeLotteryDrawResp)
	err := c.cc.Invoke(ctx, Notice_NoticeLotteryDraw_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *noticeClient) GetNoticeSubscribePreference(ctx context.Context, in *GetNoticeSubscribePreferenceReq, opts ...grpc.CallOption) (*GetNoticeSubscribePreferenceResp, error) {
	out := new(GetNoticeSubscribePreferenceResp)
	err := c.cc.Invoke(ctx, Notice_GetNoticeSubscribePreference_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *noticeClient) SaveNoticeSubscribePreference(ctx context.Context, in *SaveNoticeSubscribePreferenceReq, opts ...grpc.CallOption) (*SaveNoticeSubscribePreferenceResp, error) {
	out := new(SaveNoticeSubscribePreferenceResp)
	err := c.cc.Invoke(ctx, Notice_SaveNoticeSubscribePreference_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *noticeClient) NoticeLotteryStart(ctx context.Context, in *NoticeLotteryStartReq, opts ...grpc.CallOption) (*NoticeLotteryStartResp, error) {
	out := new(NoticeLotteryStartResp)
	err := c.cc.Invoke(ctx, Notice_NoticeLotteryStart_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *noticeClient) NoticeWishSign(ctx context.Context, in *NoticeWishSignInReq, opts ...grpc.CallOption) (*NoticeWishSignInResp, error) {
	out := new(NoticeWishSignInResp)
	err := c.cc.Invoke(ctx, Notice_NoticeWishSign_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NoticeServer is the server API for Notice service.
// All implementations must embed UnimplementedNoticeServer
// for forward compatibility
type NoticeServer interface {
	NoticeLotteryDraw(context.Context, *NoticeLotteryDrawReq) (*NoticeLotteryDrawResp, error)
	GetNoticeSubscribePreference(context.Context, *GetNoticeSubscribePreferenceReq) (*GetNoticeSubscribePreferenceResp, error)
	SaveNoticeSubscribePreference(context.Context, *SaveNoticeSubscribePreferenceReq) (*SaveNoticeSubscribePreferenceResp, error)
	NoticeLotteryStart(context.Context, *NoticeLotteryStartReq) (*NoticeLotteryStartResp, error)
	NoticeWishSign(context.Context, *NoticeWishSignInReq) (*NoticeWishSignInResp, error)
	mustEmbedUnimplementedNoticeServer()
}

// UnimplementedNoticeServer must be embedded to have forward compatible implementations.
type UnimplementedNoticeServer struct {
}

func (UnimplementedNoticeServer) NoticeLotteryDraw(context.Context, *NoticeLotteryDrawReq) (*NoticeLotteryDrawResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NoticeLotteryDraw not implemented")
}
func (UnimplementedNoticeServer) GetNoticeSubscribePreference(context.Context, *GetNoticeSubscribePreferenceReq) (*GetNoticeSubscribePreferenceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNoticeSubscribePreference not implemented")
}
func (UnimplementedNoticeServer) SaveNoticeSubscribePreference(context.Context, *SaveNoticeSubscribePreferenceReq) (*SaveNoticeSubscribePreferenceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveNoticeSubscribePreference not implemented")
}
func (UnimplementedNoticeServer) NoticeLotteryStart(context.Context, *NoticeLotteryStartReq) (*NoticeLotteryStartResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NoticeLotteryStart not implemented")
}
func (UnimplementedNoticeServer) NoticeWishSign(context.Context, *NoticeWishSignInReq) (*NoticeWishSignInResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NoticeWishSign not implemented")
}
func (UnimplementedNoticeServer) mustEmbedUnimplementedNoticeServer() {}

// UnsafeNoticeServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NoticeServer will
// result in compilation errors.
type UnsafeNoticeServer interface {
	mustEmbedUnimplementedNoticeServer()
}

func RegisterNoticeServer(s grpc.ServiceRegistrar, srv NoticeServer) {
	s.RegisterService(&Notice_ServiceDesc, srv)
}

func _Notice_NoticeLotteryDraw_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NoticeLotteryDrawReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NoticeServer).NoticeLotteryDraw(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Notice_NoticeLotteryDraw_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NoticeServer).NoticeLotteryDraw(ctx, req.(*NoticeLotteryDrawReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notice_GetNoticeSubscribePreference_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNoticeSubscribePreferenceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NoticeServer).GetNoticeSubscribePreference(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Notice_GetNoticeSubscribePreference_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NoticeServer).GetNoticeSubscribePreference(ctx, req.(*GetNoticeSubscribePreferenceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notice_SaveNoticeSubscribePreference_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveNoticeSubscribePreferenceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NoticeServer).SaveNoticeSubscribePreference(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Notice_SaveNoticeSubscribePreference_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NoticeServer).SaveNoticeSubscribePreference(ctx, req.(*SaveNoticeSubscribePreferenceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notice_NoticeLotteryStart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NoticeLotteryStartReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NoticeServer).NoticeLotteryStart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Notice_NoticeLotteryStart_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NoticeServer).NoticeLotteryStart(ctx, req.(*NoticeLotteryStartReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Notice_NoticeWishSign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NoticeWishSignInReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NoticeServer).NoticeWishSign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Notice_NoticeWishSign_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NoticeServer).NoticeWishSign(ctx, req.(*NoticeWishSignInReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Notice_ServiceDesc is the grpc.ServiceDesc for Notice service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Notice_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.notice",
	HandlerType: (*NoticeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "NoticeLotteryDraw",
			Handler:    _Notice_NoticeLotteryDraw_Handler,
		},
		{
			MethodName: "GetNoticeSubscribePreference",
			Handler:    _Notice_GetNoticeSubscribePreference_Handler,
		},
		{
			MethodName: "SaveNoticeSubscribePreference",
			Handler:    _Notice_SaveNoticeSubscribePreference_Handler,
		},
		{
			MethodName: "NoticeLotteryStart",
			Handler:    _Notice_NoticeLotteryStart_Handler,
		},
		{
			MethodName: "NoticeWishSign",
			Handler:    _Notice_NoticeWishSign_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "notice.proto",
}
