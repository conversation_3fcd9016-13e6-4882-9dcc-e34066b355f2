// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v3.19.4
// source: checkin.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// --------------------------------checkinRecord--------------------------------
type CheckinRecord struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	Id                    int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                       //id
	UserId                int64                  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`                               //用户id
	ContinuousCheckinDays int64                  `protobuf:"varint,3,opt,name=continuousCheckinDays,proto3" json:"continuousCheckinDays,omitempty"` //用户连续签到天数
	State                 int64                  `protobuf:"varint,4,opt,name=state,proto3" json:"state,omitempty"`                                 //当天是否签到，1为已签，0为未签
	LastCheckinDate       int64                  `protobuf:"varint,5,opt,name=lastCheckinDate,proto3" json:"lastCheckinDate,omitempty"`             //上一次签到的日期
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *CheckinRecord) Reset() {
	*x = CheckinRecord{}
	mi := &file_checkin_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckinRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckinRecord) ProtoMessage() {}

func (x *CheckinRecord) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckinRecord.ProtoReflect.Descriptor instead.
func (*CheckinRecord) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{0}
}

func (x *CheckinRecord) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CheckinRecord) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *CheckinRecord) GetContinuousCheckinDays() int64 {
	if x != nil {
		return x.ContinuousCheckinDays
	}
	return 0
}

func (x *CheckinRecord) GetState() int64 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *CheckinRecord) GetLastCheckinDate() int64 {
	if x != nil {
		return x.LastCheckinDate
	}
	return 0
}

type AddCheckinRecordReq struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	UserId                int64                  `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`                               //用户id
	ContinuousCheckinDays int64                  `protobuf:"varint,2,opt,name=continuousCheckinDays,proto3" json:"continuousCheckinDays,omitempty"` //用户连续签到天数
	State                 int64                  `protobuf:"varint,3,opt,name=state,proto3" json:"state,omitempty"`                                 //当天是否签到，1为已签，0为未签
	LastCheckinDate       int64                  `protobuf:"varint,4,opt,name=lastCheckinDate,proto3" json:"lastCheckinDate,omitempty"`             //上一次签到的日期
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *AddCheckinRecordReq) Reset() {
	*x = AddCheckinRecordReq{}
	mi := &file_checkin_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCheckinRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCheckinRecordReq) ProtoMessage() {}

func (x *AddCheckinRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCheckinRecordReq.ProtoReflect.Descriptor instead.
func (*AddCheckinRecordReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{1}
}

func (x *AddCheckinRecordReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AddCheckinRecordReq) GetContinuousCheckinDays() int64 {
	if x != nil {
		return x.ContinuousCheckinDays
	}
	return 0
}

func (x *AddCheckinRecordReq) GetState() int64 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *AddCheckinRecordReq) GetLastCheckinDate() int64 {
	if x != nil {
		return x.LastCheckinDate
	}
	return 0
}

type AddCheckinRecordResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddCheckinRecordResp) Reset() {
	*x = AddCheckinRecordResp{}
	mi := &file_checkin_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCheckinRecordResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCheckinRecordResp) ProtoMessage() {}

func (x *AddCheckinRecordResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCheckinRecordResp.ProtoReflect.Descriptor instead.
func (*AddCheckinRecordResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{2}
}

type UpdateCheckinRecordReq struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	Id                    int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                       //id
	UserId                int64                  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`                               //用户id
	ContinuousCheckinDays int64                  `protobuf:"varint,3,opt,name=continuousCheckinDays,proto3" json:"continuousCheckinDays,omitempty"` //用户连续签到天数
	State                 int64                  `protobuf:"varint,4,opt,name=state,proto3" json:"state,omitempty"`                                 //当天是否签到，1为已签，0为未签
	LastCheckinDate       int64                  `protobuf:"varint,5,opt,name=lastCheckinDate,proto3" json:"lastCheckinDate,omitempty"`             //上一次签到的日期
	Integral              int64                  `protobuf:"varint,6,opt,name=integral,proto3" json:"integral,omitempty"`                           //心愿值
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *UpdateCheckinRecordReq) Reset() {
	*x = UpdateCheckinRecordReq{}
	mi := &file_checkin_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCheckinRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCheckinRecordReq) ProtoMessage() {}

func (x *UpdateCheckinRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCheckinRecordReq.ProtoReflect.Descriptor instead.
func (*UpdateCheckinRecordReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateCheckinRecordReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateCheckinRecordReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UpdateCheckinRecordReq) GetContinuousCheckinDays() int64 {
	if x != nil {
		return x.ContinuousCheckinDays
	}
	return 0
}

func (x *UpdateCheckinRecordReq) GetState() int64 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *UpdateCheckinRecordReq) GetLastCheckinDate() int64 {
	if x != nil {
		return x.LastCheckinDate
	}
	return 0
}

func (x *UpdateCheckinRecordReq) GetIntegral() int64 {
	if x != nil {
		return x.Integral
	}
	return 0
}

type UpdateCheckinRecordResp struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	ContinuousCheckinDays int64                  `protobuf:"varint,1,opt,name=continuousCheckinDays,proto3" json:"continuousCheckinDays,omitempty"` //用户连续签到天数
	State                 int64                  `protobuf:"varint,2,opt,name=state,proto3" json:"state,omitempty"`                                 //当天是否签到，1为已签，0为未签
	Integral              int64                  `protobuf:"varint,3,opt,name=integral,proto3" json:"integral,omitempty"`                           //心愿值
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *UpdateCheckinRecordResp) Reset() {
	*x = UpdateCheckinRecordResp{}
	mi := &file_checkin_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCheckinRecordResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCheckinRecordResp) ProtoMessage() {}

func (x *UpdateCheckinRecordResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCheckinRecordResp.ProtoReflect.Descriptor instead.
func (*UpdateCheckinRecordResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateCheckinRecordResp) GetContinuousCheckinDays() int64 {
	if x != nil {
		return x.ContinuousCheckinDays
	}
	return 0
}

func (x *UpdateCheckinRecordResp) GetState() int64 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *UpdateCheckinRecordResp) GetIntegral() int64 {
	if x != nil {
		return x.Integral
	}
	return 0
}

type GetCheckinRecordByUserIdReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"` //user_id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCheckinRecordByUserIdReq) Reset() {
	*x = GetCheckinRecordByUserIdReq{}
	mi := &file_checkin_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCheckinRecordByUserIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCheckinRecordByUserIdReq) ProtoMessage() {}

func (x *GetCheckinRecordByUserIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCheckinRecordByUserIdReq.ProtoReflect.Descriptor instead.
func (*GetCheckinRecordByUserIdReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{5}
}

func (x *GetCheckinRecordByUserIdReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GetCheckinRecordByUserIdResp struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	ContinuousCheckinDays int64                  `protobuf:"varint,1,opt,name=continuousCheckinDays,proto3" json:"continuousCheckinDays,omitempty"` // 用户连续签到天数
	State                 int64                  `protobuf:"varint,2,opt,name=state,proto3" json:"state,omitempty"`                                 // 当天是否签到，1为已签，0为未签
	Integral              int64                  `protobuf:"varint,3,opt,name=integral,proto3" json:"integral,omitempty"`                           // 心愿值
	SubStatus             int64                  `protobuf:"varint,4,opt,name=subStatus,proto3" json:"subStatus,omitempty"`                         // 订阅状态
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *GetCheckinRecordByUserIdResp) Reset() {
	*x = GetCheckinRecordByUserIdResp{}
	mi := &file_checkin_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCheckinRecordByUserIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCheckinRecordByUserIdResp) ProtoMessage() {}

func (x *GetCheckinRecordByUserIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCheckinRecordByUserIdResp.ProtoReflect.Descriptor instead.
func (*GetCheckinRecordByUserIdResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{6}
}

func (x *GetCheckinRecordByUserIdResp) GetContinuousCheckinDays() int64 {
	if x != nil {
		return x.ContinuousCheckinDays
	}
	return 0
}

func (x *GetCheckinRecordByUserIdResp) GetState() int64 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *GetCheckinRecordByUserIdResp) GetIntegral() int64 {
	if x != nil {
		return x.Integral
	}
	return 0
}

func (x *GetCheckinRecordByUserIdResp) GetSubStatus() int64 {
	if x != nil {
		return x.SubStatus
	}
	return 0
}

type Integral struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`             //id
	UserId        int64                  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`     //userId
	Integral      int64                  `protobuf:"varint,3,opt,name=integral,proto3" json:"integral,omitempty"` //积分
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Integral) Reset() {
	*x = Integral{}
	mi := &file_checkin_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Integral) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Integral) ProtoMessage() {}

func (x *Integral) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Integral.ProtoReflect.Descriptor instead.
func (*Integral) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{7}
}

func (x *Integral) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Integral) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *Integral) GetIntegral() int64 {
	if x != nil {
		return x.Integral
	}
	return 0
}

type AddIntegralReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`     //userId
	Integral      int64                  `protobuf:"varint,2,opt,name=integral,proto3" json:"integral,omitempty"` //积分
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddIntegralReq) Reset() {
	*x = AddIntegralReq{}
	mi := &file_checkin_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddIntegralReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddIntegralReq) ProtoMessage() {}

func (x *AddIntegralReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddIntegralReq.ProtoReflect.Descriptor instead.
func (*AddIntegralReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{8}
}

func (x *AddIntegralReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AddIntegralReq) GetIntegral() int64 {
	if x != nil {
		return x.Integral
	}
	return 0
}

type AddIntegralResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddIntegralResp) Reset() {
	*x = AddIntegralResp{}
	mi := &file_checkin_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddIntegralResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddIntegralResp) ProtoMessage() {}

func (x *AddIntegralResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddIntegralResp.ProtoReflect.Descriptor instead.
func (*AddIntegralResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{9}
}

type UpdateIntegralReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`             //id
	UserId        int64                  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`     //userId
	Integral      int64                  `protobuf:"varint,3,opt,name=integral,proto3" json:"integral,omitempty"` //积分
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateIntegralReq) Reset() {
	*x = UpdateIntegralReq{}
	mi := &file_checkin_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateIntegralReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateIntegralReq) ProtoMessage() {}

func (x *UpdateIntegralReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateIntegralReq.ProtoReflect.Descriptor instead.
func (*UpdateIntegralReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateIntegralReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateIntegralReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UpdateIntegralReq) GetIntegral() int64 {
	if x != nil {
		return x.Integral
	}
	return 0
}

type UpdateIntegralResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateIntegralResp) Reset() {
	*x = UpdateIntegralResp{}
	mi := &file_checkin_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateIntegralResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateIntegralResp) ProtoMessage() {}

func (x *UpdateIntegralResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateIntegralResp.ProtoReflect.Descriptor instead.
func (*UpdateIntegralResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{11}
}

type GetIntegralByUserIdReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` //user_id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetIntegralByUserIdReq) Reset() {
	*x = GetIntegralByUserIdReq{}
	mi := &file_checkin_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIntegralByUserIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIntegralByUserIdReq) ProtoMessage() {}

func (x *GetIntegralByUserIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIntegralByUserIdReq.ProtoReflect.Descriptor instead.
func (*GetIntegralByUserIdReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{12}
}

func (x *GetIntegralByUserIdReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GetIntegralByUserIdResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Integral      int64                  `protobuf:"varint,1,opt,name=integral,proto3" json:"integral,omitempty"` //Integral
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetIntegralByUserIdResp) Reset() {
	*x = GetIntegralByUserIdResp{}
	mi := &file_checkin_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIntegralByUserIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIntegralByUserIdResp) ProtoMessage() {}

func (x *GetIntegralByUserIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIntegralByUserIdResp.ProtoReflect.Descriptor instead.
func (*GetIntegralByUserIdResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{13}
}

func (x *GetIntegralByUserIdResp) GetIntegral() int64 {
	if x != nil {
		return x.Integral
	}
	return 0
}

// 扣减积分
type DeductIntegralReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`     //userId
	Integral      int64                  `protobuf:"varint,2,opt,name=integral,proto3" json:"integral,omitempty"` //积分
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeductIntegralReq) Reset() {
	*x = DeductIntegralReq{}
	mi := &file_checkin_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeductIntegralReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeductIntegralReq) ProtoMessage() {}

func (x *DeductIntegralReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeductIntegralReq.ProtoReflect.Descriptor instead.
func (*DeductIntegralReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{14}
}

func (x *DeductIntegralReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *DeductIntegralReq) GetIntegral() int64 {
	if x != nil {
		return x.Integral
	}
	return 0
}

type DeductIntegralResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeductIntegralResp) Reset() {
	*x = DeductIntegralResp{}
	mi := &file_checkin_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeductIntegralResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeductIntegralResp) ProtoMessage() {}

func (x *DeductIntegralResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeductIntegralResp.ProtoReflect.Descriptor instead.
func (*DeductIntegralResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{15}
}

// --------------------------------integralRecord--------------------------------
type IntegralRecord struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`             //id
	UserId        int64                  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`     //userId
	Integral      int64                  `protobuf:"varint,3,opt,name=integral,proto3" json:"integral,omitempty"` //增加或减少的积分
	Content       int64                  `protobuf:"varint,4,opt,name=content,proto3" json:"content,omitempty"`   //增加或减少积分的原因
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IntegralRecord) Reset() {
	*x = IntegralRecord{}
	mi := &file_checkin_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IntegralRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IntegralRecord) ProtoMessage() {}

func (x *IntegralRecord) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IntegralRecord.ProtoReflect.Descriptor instead.
func (*IntegralRecord) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{16}
}

func (x *IntegralRecord) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *IntegralRecord) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *IntegralRecord) GetIntegral() int64 {
	if x != nil {
		return x.Integral
	}
	return 0
}

func (x *IntegralRecord) GetContent() int64 {
	if x != nil {
		return x.Content
	}
	return 0
}

type AddIntegralRecordReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`     //userId
	Integral      int64                  `protobuf:"varint,2,opt,name=integral,proto3" json:"integral,omitempty"` //增加或减少的积分
	Content       int64                  `protobuf:"varint,3,opt,name=content,proto3" json:"content,omitempty"`   //增加或减少积分的原因
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddIntegralRecordReq) Reset() {
	*x = AddIntegralRecordReq{}
	mi := &file_checkin_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddIntegralRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddIntegralRecordReq) ProtoMessage() {}

func (x *AddIntegralRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddIntegralRecordReq.ProtoReflect.Descriptor instead.
func (*AddIntegralRecordReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{17}
}

func (x *AddIntegralRecordReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AddIntegralRecordReq) GetIntegral() int64 {
	if x != nil {
		return x.Integral
	}
	return 0
}

func (x *AddIntegralRecordReq) GetContent() int64 {
	if x != nil {
		return x.Content
	}
	return 0
}

type AddIntegralRecordResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddIntegralRecordResp) Reset() {
	*x = AddIntegralRecordResp{}
	mi := &file_checkin_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddIntegralRecordResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddIntegralRecordResp) ProtoMessage() {}

func (x *AddIntegralRecordResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddIntegralRecordResp.ProtoReflect.Descriptor instead.
func (*AddIntegralRecordResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{18}
}

type UpdateIntegralRecordReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`             //id
	UserId        int64                  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`     //userId
	Integral      int64                  `protobuf:"varint,3,opt,name=integral,proto3" json:"integral,omitempty"` //增加或减少的积分
	Content       int64                  `protobuf:"varint,4,opt,name=content,proto3" json:"content,omitempty"`   //增加或减少积分的原因
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateIntegralRecordReq) Reset() {
	*x = UpdateIntegralRecordReq{}
	mi := &file_checkin_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateIntegralRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateIntegralRecordReq) ProtoMessage() {}

func (x *UpdateIntegralRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateIntegralRecordReq.ProtoReflect.Descriptor instead.
func (*UpdateIntegralRecordReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{19}
}

func (x *UpdateIntegralRecordReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateIntegralRecordReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UpdateIntegralRecordReq) GetIntegral() int64 {
	if x != nil {
		return x.Integral
	}
	return 0
}

func (x *UpdateIntegralRecordReq) GetContent() int64 {
	if x != nil {
		return x.Content
	}
	return 0
}

type UpdateIntegralRecordResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateIntegralRecordResp) Reset() {
	*x = UpdateIntegralRecordResp{}
	mi := &file_checkin_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateIntegralRecordResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateIntegralRecordResp) ProtoMessage() {}

func (x *UpdateIntegralRecordResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateIntegralRecordResp.ProtoReflect.Descriptor instead.
func (*UpdateIntegralRecordResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{20}
}

type DelIntegralRecordReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DelIntegralRecordReq) Reset() {
	*x = DelIntegralRecordReq{}
	mi := &file_checkin_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelIntegralRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelIntegralRecordReq) ProtoMessage() {}

func (x *DelIntegralRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelIntegralRecordReq.ProtoReflect.Descriptor instead.
func (*DelIntegralRecordReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{21}
}

func (x *DelIntegralRecordReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DelIntegralRecordResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DelIntegralRecordResp) Reset() {
	*x = DelIntegralRecordResp{}
	mi := &file_checkin_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelIntegralRecordResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelIntegralRecordResp) ProtoMessage() {}

func (x *DelIntegralRecordResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelIntegralRecordResp.ProtoReflect.Descriptor instead.
func (*DelIntegralRecordResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{22}
}

type GetIntegralRecordByIdReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetIntegralRecordByIdReq) Reset() {
	*x = GetIntegralRecordByIdReq{}
	mi := &file_checkin_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIntegralRecordByIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIntegralRecordByIdReq) ProtoMessage() {}

func (x *GetIntegralRecordByIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIntegralRecordByIdReq.ProtoReflect.Descriptor instead.
func (*GetIntegralRecordByIdReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{23}
}

func (x *GetIntegralRecordByIdReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetIntegralRecordByIdResp struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IntegralRecord *IntegralRecord        `protobuf:"bytes,1,opt,name=integralRecord,proto3" json:"integralRecord,omitempty"` //integralRecord
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetIntegralRecordByIdResp) Reset() {
	*x = GetIntegralRecordByIdResp{}
	mi := &file_checkin_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIntegralRecordByIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIntegralRecordByIdResp) ProtoMessage() {}

func (x *GetIntegralRecordByIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIntegralRecordByIdResp.ProtoReflect.Descriptor instead.
func (*GetIntegralRecordByIdResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{24}
}

func (x *GetIntegralRecordByIdResp) GetIntegralRecord() *IntegralRecord {
	if x != nil {
		return x.IntegralRecord
	}
	return nil
}

type GetIntegralRecordByUserIdReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"` //id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetIntegralRecordByUserIdReq) Reset() {
	*x = GetIntegralRecordByUserIdReq{}
	mi := &file_checkin_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIntegralRecordByUserIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIntegralRecordByUserIdReq) ProtoMessage() {}

func (x *GetIntegralRecordByUserIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIntegralRecordByUserIdReq.ProtoReflect.Descriptor instead.
func (*GetIntegralRecordByUserIdReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{25}
}

func (x *GetIntegralRecordByUserIdReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GetIntegralRecordByUserIdResp struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IntegralRecord *IntegralRecord        `protobuf:"bytes,1,opt,name=integralRecord,proto3" json:"integralRecord,omitempty"` //integralRecord
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetIntegralRecordByUserIdResp) Reset() {
	*x = GetIntegralRecordByUserIdResp{}
	mi := &file_checkin_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIntegralRecordByUserIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIntegralRecordByUserIdResp) ProtoMessage() {}

func (x *GetIntegralRecordByUserIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIntegralRecordByUserIdResp.ProtoReflect.Descriptor instead.
func (*GetIntegralRecordByUserIdResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{26}
}

func (x *GetIntegralRecordByUserIdResp) GetIntegralRecord() *IntegralRecord {
	if x != nil {
		return x.IntegralRecord
	}
	return nil
}

type SearchIntegralRecordReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int64                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`         //page
	Limit         int64                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`       //limit
	Id            int64                  `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`             //id
	UserId        int64                  `protobuf:"varint,4,opt,name=userId,proto3" json:"userId,omitempty"`     //userId
	Integral      int64                  `protobuf:"varint,5,opt,name=integral,proto3" json:"integral,omitempty"` //增加或减少的积分
	Content       int64                  `protobuf:"varint,6,opt,name=content,proto3" json:"content,omitempty"`   //增加或减少积分的原因
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchIntegralRecordReq) Reset() {
	*x = SearchIntegralRecordReq{}
	mi := &file_checkin_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchIntegralRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchIntegralRecordReq) ProtoMessage() {}

func (x *SearchIntegralRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchIntegralRecordReq.ProtoReflect.Descriptor instead.
func (*SearchIntegralRecordReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{27}
}

func (x *SearchIntegralRecordReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchIntegralRecordReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *SearchIntegralRecordReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SearchIntegralRecordReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *SearchIntegralRecordReq) GetIntegral() int64 {
	if x != nil {
		return x.Integral
	}
	return 0
}

func (x *SearchIntegralRecordReq) GetContent() int64 {
	if x != nil {
		return x.Content
	}
	return 0
}

type SearchIntegralRecordResp struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IntegralRecord []*IntegralRecord      `protobuf:"bytes,1,rep,name=integralRecord,proto3" json:"integralRecord,omitempty"` //integralRecord
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SearchIntegralRecordResp) Reset() {
	*x = SearchIntegralRecordResp{}
	mi := &file_checkin_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchIntegralRecordResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchIntegralRecordResp) ProtoMessage() {}

func (x *SearchIntegralRecordResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchIntegralRecordResp.ProtoReflect.Descriptor instead.
func (*SearchIntegralRecordResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{28}
}

func (x *SearchIntegralRecordResp) GetIntegralRecord() []*IntegralRecord {
	if x != nil {
		return x.IntegralRecord
	}
	return nil
}

// --------------------------------taskProgress--------------------------------
type TaskProgress struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	Id                    int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId                int64                  `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	IsParticipatedLottery int64                  `protobuf:"varint,3,opt,name=isParticipatedLottery,proto3" json:"isParticipatedLottery,omitempty"`
	IsInitiatedLottery    int64                  `protobuf:"varint,4,opt,name=isInitiatedLottery,proto3" json:"isInitiatedLottery,omitempty"`
	IsSubCheckin          int64                  `protobuf:"varint,5,opt,name=is_sub_checkin,json=isSubCheckin,proto3" json:"is_sub_checkin,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *TaskProgress) Reset() {
	*x = TaskProgress{}
	mi := &file_checkin_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskProgress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskProgress) ProtoMessage() {}

func (x *TaskProgress) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskProgress.ProtoReflect.Descriptor instead.
func (*TaskProgress) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{29}
}

func (x *TaskProgress) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TaskProgress) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *TaskProgress) GetIsParticipatedLottery() int64 {
	if x != nil {
		return x.IsParticipatedLottery
	}
	return 0
}

func (x *TaskProgress) GetIsInitiatedLottery() int64 {
	if x != nil {
		return x.IsInitiatedLottery
	}
	return 0
}

func (x *TaskProgress) GetIsSubCheckin() int64 {
	if x != nil {
		return x.IsSubCheckin
	}
	return 0
}

type GetTaskProgressReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTaskProgressReq) Reset() {
	*x = GetTaskProgressReq{}
	mi := &file_checkin_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTaskProgressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskProgressReq) ProtoMessage() {}

func (x *GetTaskProgressReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskProgressReq.ProtoReflect.Descriptor instead.
func (*GetTaskProgressReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{30}
}

func (x *GetTaskProgressReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GetTaskProgressResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DayCount      int64                  `protobuf:"varint,1,opt,name=dayCount,proto3" json:"dayCount,omitempty"`
	WeekCount     int64                  `protobuf:"varint,2,opt,name=weekCount,proto3" json:"weekCount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTaskProgressResp) Reset() {
	*x = GetTaskProgressResp{}
	mi := &file_checkin_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTaskProgressResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskProgressResp) ProtoMessage() {}

func (x *GetTaskProgressResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskProgressResp.ProtoReflect.Descriptor instead.
func (*GetTaskProgressResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{31}
}

func (x *GetTaskProgressResp) GetDayCount() int64 {
	if x != nil {
		return x.DayCount
	}
	return 0
}

func (x *GetTaskProgressResp) GetWeekCount() int64 {
	if x != nil {
		return x.WeekCount
	}
	return 0
}

type UpdateSubReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	State         int64                  `protobuf:"varint,2,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateSubReq) Reset() {
	*x = UpdateSubReq{}
	mi := &file_checkin_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateSubReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSubReq) ProtoMessage() {}

func (x *UpdateSubReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSubReq.ProtoReflect.Descriptor instead.
func (*UpdateSubReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{32}
}

func (x *UpdateSubReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UpdateSubReq) GetState() int64 {
	if x != nil {
		return x.State
	}
	return 0
}

type UpdateSubResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateSubResp) Reset() {
	*x = UpdateSubResp{}
	mi := &file_checkin_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateSubResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSubResp) ProtoMessage() {}

func (x *UpdateSubResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSubResp.ProtoReflect.Descriptor instead.
func (*UpdateSubResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{33}
}

// --------------------------------taskRecord--------------------------------
type TaskRecord struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
	Type          int64                  `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	UserId        int64                  `protobuf:"varint,3,opt,name=userId,proto3" json:"userId,omitempty"`         //userId
	TaskId        int64                  `protobuf:"varint,4,opt,name=taskId,proto3" json:"taskId,omitempty"`         //taskId
	IsFinished    int64                  `protobuf:"varint,5,opt,name=isFinished,proto3" json:"isFinished,omitempty"` //0为未完成，1为已完成
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskRecord) Reset() {
	*x = TaskRecord{}
	mi := &file_checkin_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskRecord) ProtoMessage() {}

func (x *TaskRecord) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskRecord.ProtoReflect.Descriptor instead.
func (*TaskRecord) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{34}
}

func (x *TaskRecord) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TaskRecord) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *TaskRecord) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *TaskRecord) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *TaskRecord) GetIsFinished() int64 {
	if x != nil {
		return x.IsFinished
	}
	return 0
}

type AddTaskRecordReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`         //userId
	TaskId        int64                  `protobuf:"varint,2,opt,name=taskId,proto3" json:"taskId,omitempty"`         //taskId
	IsFinished    int64                  `protobuf:"varint,3,opt,name=isFinished,proto3" json:"isFinished,omitempty"` //0为未完成，1为已完成
	Type          int64                  `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddTaskRecordReq) Reset() {
	*x = AddTaskRecordReq{}
	mi := &file_checkin_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddTaskRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddTaskRecordReq) ProtoMessage() {}

func (x *AddTaskRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddTaskRecordReq.ProtoReflect.Descriptor instead.
func (*AddTaskRecordReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{35}
}

func (x *AddTaskRecordReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AddTaskRecordReq) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *AddTaskRecordReq) GetIsFinished() int64 {
	if x != nil {
		return x.IsFinished
	}
	return 0
}

func (x *AddTaskRecordReq) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

type AddTaskRecordResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddTaskRecordResp) Reset() {
	*x = AddTaskRecordResp{}
	mi := &file_checkin_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddTaskRecordResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddTaskRecordResp) ProtoMessage() {}

func (x *AddTaskRecordResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddTaskRecordResp.ProtoReflect.Descriptor instead.
func (*AddTaskRecordResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{36}
}

type UpdateTaskRecordReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                 //id
	UserId        int64                  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`         //userId
	TaskId        int64                  `protobuf:"varint,3,opt,name=taskId,proto3" json:"taskId,omitempty"`         //taskId
	IsFinished    int64                  `protobuf:"varint,4,opt,name=isFinished,proto3" json:"isFinished,omitempty"` //0为未完成，1为已完成
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTaskRecordReq) Reset() {
	*x = UpdateTaskRecordReq{}
	mi := &file_checkin_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTaskRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTaskRecordReq) ProtoMessage() {}

func (x *UpdateTaskRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTaskRecordReq.ProtoReflect.Descriptor instead.
func (*UpdateTaskRecordReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{37}
}

func (x *UpdateTaskRecordReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateTaskRecordReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UpdateTaskRecordReq) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *UpdateTaskRecordReq) GetIsFinished() int64 {
	if x != nil {
		return x.IsFinished
	}
	return 0
}

type UpdateTaskRecordResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTaskRecordResp) Reset() {
	*x = UpdateTaskRecordResp{}
	mi := &file_checkin_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTaskRecordResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTaskRecordResp) ProtoMessage() {}

func (x *UpdateTaskRecordResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTaskRecordResp.ProtoReflect.Descriptor instead.
func (*UpdateTaskRecordResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{38}
}

type DelTaskRecordReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DelTaskRecordReq) Reset() {
	*x = DelTaskRecordReq{}
	mi := &file_checkin_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelTaskRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelTaskRecordReq) ProtoMessage() {}

func (x *DelTaskRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelTaskRecordReq.ProtoReflect.Descriptor instead.
func (*DelTaskRecordReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{39}
}

func (x *DelTaskRecordReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DelTaskRecordResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DelTaskRecordResp) Reset() {
	*x = DelTaskRecordResp{}
	mi := &file_checkin_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelTaskRecordResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelTaskRecordResp) ProtoMessage() {}

func (x *DelTaskRecordResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelTaskRecordResp.ProtoReflect.Descriptor instead.
func (*DelTaskRecordResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{40}
}

type GetTaskRecordByIdReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTaskRecordByIdReq) Reset() {
	*x = GetTaskRecordByIdReq{}
	mi := &file_checkin_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTaskRecordByIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskRecordByIdReq) ProtoMessage() {}

func (x *GetTaskRecordByIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskRecordByIdReq.ProtoReflect.Descriptor instead.
func (*GetTaskRecordByIdReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{41}
}

func (x *GetTaskRecordByIdReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetTaskRecordByIdResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TaskRecord    *TaskRecord            `protobuf:"bytes,1,opt,name=taskRecord,proto3" json:"taskRecord,omitempty"` //taskRecord
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTaskRecordByIdResp) Reset() {
	*x = GetTaskRecordByIdResp{}
	mi := &file_checkin_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTaskRecordByIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskRecordByIdResp) ProtoMessage() {}

func (x *GetTaskRecordByIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskRecordByIdResp.ProtoReflect.Descriptor instead.
func (*GetTaskRecordByIdResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{42}
}

func (x *GetTaskRecordByIdResp) GetTaskRecord() *TaskRecord {
	if x != nil {
		return x.TaskRecord
	}
	return nil
}

type GetTaskRecordByUserIdReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTaskRecordByUserIdReq) Reset() {
	*x = GetTaskRecordByUserIdReq{}
	mi := &file_checkin_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTaskRecordByUserIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskRecordByUserIdReq) ProtoMessage() {}

func (x *GetTaskRecordByUserIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskRecordByUserIdReq.ProtoReflect.Descriptor instead.
func (*GetTaskRecordByUserIdReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{43}
}

func (x *GetTaskRecordByUserIdReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GetTaskRecordByUserIdResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TaskList      []*Tasks               `protobuf:"bytes,1,rep,name=taskList,proto3" json:"taskList,omitempty"` // 任务列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTaskRecordByUserIdResp) Reset() {
	*x = GetTaskRecordByUserIdResp{}
	mi := &file_checkin_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTaskRecordByUserIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskRecordByUserIdResp) ProtoMessage() {}

func (x *GetTaskRecordByUserIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskRecordByUserIdResp.ProtoReflect.Descriptor instead.
func (*GetTaskRecordByUserIdResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{44}
}

func (x *GetTaskRecordByUserIdResp) GetTaskList() []*Tasks {
	if x != nil {
		return x.TaskList
	}
	return nil
}

type SearchTaskRecordReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int64                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`             //page
	Limit         int64                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`           //limit
	Id            int64                  `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`                 //id
	UserId        int64                  `protobuf:"varint,4,opt,name=userId,proto3" json:"userId,omitempty"`         //userId
	TaskId        int64                  `protobuf:"varint,5,opt,name=taskId,proto3" json:"taskId,omitempty"`         //taskId
	IsFinished    int64                  `protobuf:"varint,6,opt,name=isFinished,proto3" json:"isFinished,omitempty"` //0为未完成，1为已完成
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchTaskRecordReq) Reset() {
	*x = SearchTaskRecordReq{}
	mi := &file_checkin_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchTaskRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTaskRecordReq) ProtoMessage() {}

func (x *SearchTaskRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTaskRecordReq.ProtoReflect.Descriptor instead.
func (*SearchTaskRecordReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{45}
}

func (x *SearchTaskRecordReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchTaskRecordReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *SearchTaskRecordReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SearchTaskRecordReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *SearchTaskRecordReq) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *SearchTaskRecordReq) GetIsFinished() int64 {
	if x != nil {
		return x.IsFinished
	}
	return 0
}

type SearchTaskRecordResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TaskRecord    []*TaskRecord          `protobuf:"bytes,1,rep,name=taskRecord,proto3" json:"taskRecord,omitempty"` //taskRecord
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchTaskRecordResp) Reset() {
	*x = SearchTaskRecordResp{}
	mi := &file_checkin_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchTaskRecordResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTaskRecordResp) ProtoMessage() {}

func (x *SearchTaskRecordResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTaskRecordResp.ProtoReflect.Descriptor instead.
func (*SearchTaskRecordResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{46}
}

func (x *SearchTaskRecordResp) GetTaskRecord() []*TaskRecord {
	if x != nil {
		return x.TaskRecord
	}
	return nil
}

// --------------------------------tasks--------------------------------
type Tasks struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                 //id
	Type          int64                  `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`             //1为新手，2为每日，3为每周
	Content       string                 `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`        //任务内容
	Integral      int64                  `protobuf:"varint,4,opt,name=integral,proto3" json:"integral,omitempty"`     //完成后增加的心愿值
	IsFinished    int64                  `protobuf:"varint,5,opt,name=isFinished,proto3" json:"isFinished,omitempty"` //0为未完成，1为已完成，2为已领取
	Path          string                 `protobuf:"bytes,6,opt,name=path,proto3" json:"path,omitempty"`
	Count         int64                  `protobuf:"varint,7,opt,name=count,proto3" json:"count,omitempty"`
	NeedCount     int64                  `protobuf:"varint,8,opt,name=needCount,proto3" json:"needCount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Tasks) Reset() {
	*x = Tasks{}
	mi := &file_checkin_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Tasks) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Tasks) ProtoMessage() {}

func (x *Tasks) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Tasks.ProtoReflect.Descriptor instead.
func (*Tasks) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{47}
}

func (x *Tasks) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Tasks) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *Tasks) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *Tasks) GetIntegral() int64 {
	if x != nil {
		return x.Integral
	}
	return 0
}

func (x *Tasks) GetIsFinished() int64 {
	if x != nil {
		return x.IsFinished
	}
	return 0
}

func (x *Tasks) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *Tasks) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *Tasks) GetNeedCount() int64 {
	if x != nil {
		return x.NeedCount
	}
	return 0
}

type AddTasksReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          int64                  `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`         //1为新手，2为每日，3为每周
	Content       string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`    //任务内容
	Integral      int64                  `protobuf:"varint,3,opt,name=integral,proto3" json:"integral,omitempty"` //完成后增加的心愿值
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddTasksReq) Reset() {
	*x = AddTasksReq{}
	mi := &file_checkin_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddTasksReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddTasksReq) ProtoMessage() {}

func (x *AddTasksReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddTasksReq.ProtoReflect.Descriptor instead.
func (*AddTasksReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{48}
}

func (x *AddTasksReq) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *AddTasksReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *AddTasksReq) GetIntegral() int64 {
	if x != nil {
		return x.Integral
	}
	return 0
}

type AddTasksResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddTasksResp) Reset() {
	*x = AddTasksResp{}
	mi := &file_checkin_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddTasksResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddTasksResp) ProtoMessage() {}

func (x *AddTasksResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddTasksResp.ProtoReflect.Descriptor instead.
func (*AddTasksResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{49}
}

type UpdateTasksReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`             //id
	Type          int64                  `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`         //1为新手，2为每日，3为每周
	Content       string                 `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`    //任务内容
	Integral      int64                  `protobuf:"varint,4,opt,name=integral,proto3" json:"integral,omitempty"` //完成后增加的心愿值
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTasksReq) Reset() {
	*x = UpdateTasksReq{}
	mi := &file_checkin_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTasksReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTasksReq) ProtoMessage() {}

func (x *UpdateTasksReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTasksReq.ProtoReflect.Descriptor instead.
func (*UpdateTasksReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{50}
}

func (x *UpdateTasksReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateTasksReq) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *UpdateTasksReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *UpdateTasksReq) GetIntegral() int64 {
	if x != nil {
		return x.Integral
	}
	return 0
}

type UpdateTasksResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTasksResp) Reset() {
	*x = UpdateTasksResp{}
	mi := &file_checkin_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTasksResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTasksResp) ProtoMessage() {}

func (x *UpdateTasksResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTasksResp.ProtoReflect.Descriptor instead.
func (*UpdateTasksResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{51}
}

type DelTasksReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DelTasksReq) Reset() {
	*x = DelTasksReq{}
	mi := &file_checkin_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelTasksReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelTasksReq) ProtoMessage() {}

func (x *DelTasksReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelTasksReq.ProtoReflect.Descriptor instead.
func (*DelTasksReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{52}
}

func (x *DelTasksReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DelTasksResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DelTasksResp) Reset() {
	*x = DelTasksResp{}
	mi := &file_checkin_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelTasksResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelTasksResp) ProtoMessage() {}

func (x *DelTasksResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelTasksResp.ProtoReflect.Descriptor instead.
func (*DelTasksResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{53}
}

type GetTasksByIdReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTasksByIdReq) Reset() {
	*x = GetTasksByIdReq{}
	mi := &file_checkin_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTasksByIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTasksByIdReq) ProtoMessage() {}

func (x *GetTasksByIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTasksByIdReq.ProtoReflect.Descriptor instead.
func (*GetTasksByIdReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{54}
}

func (x *GetTasksByIdReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetTasksByIdResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Tasks         *Tasks                 `protobuf:"bytes,1,opt,name=tasks,proto3" json:"tasks,omitempty"` //tasks
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTasksByIdResp) Reset() {
	*x = GetTasksByIdResp{}
	mi := &file_checkin_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTasksByIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTasksByIdResp) ProtoMessage() {}

func (x *GetTasksByIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTasksByIdResp.ProtoReflect.Descriptor instead.
func (*GetTasksByIdResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{55}
}

func (x *GetTasksByIdResp) GetTasks() *Tasks {
	if x != nil {
		return x.Tasks
	}
	return nil
}

type SearchTasksReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int64                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`         //page
	Limit         int64                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`       //limit
	Id            int64                  `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`             //id
	Type          int64                  `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`         //1为新手，2为每日，3为每周
	Content       string                 `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`    //任务内容
	Integral      int64                  `protobuf:"varint,6,opt,name=integral,proto3" json:"integral,omitempty"` //完成后增加的心愿值
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchTasksReq) Reset() {
	*x = SearchTasksReq{}
	mi := &file_checkin_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchTasksReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTasksReq) ProtoMessage() {}

func (x *SearchTasksReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTasksReq.ProtoReflect.Descriptor instead.
func (*SearchTasksReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{56}
}

func (x *SearchTasksReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchTasksReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *SearchTasksReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SearchTasksReq) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *SearchTasksReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SearchTasksReq) GetIntegral() int64 {
	if x != nil {
		return x.Integral
	}
	return 0
}

type SearchTasksResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Tasks         []*Tasks               `protobuf:"bytes,1,rep,name=tasks,proto3" json:"tasks,omitempty"` //tasks
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchTasksResp) Reset() {
	*x = SearchTasksResp{}
	mi := &file_checkin_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchTasksResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTasksResp) ProtoMessage() {}

func (x *SearchTasksResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTasksResp.ProtoReflect.Descriptor instead.
func (*SearchTasksResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{57}
}

func (x *SearchTasksResp) GetTasks() []*Tasks {
	if x != nil {
		return x.Tasks
	}
	return nil
}

// --------------------------------others--------------------------------
type NoticeWishCheckinData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`         //用户
	Reward        int64                  `protobuf:"varint,2,opt,name=reward,proto3" json:"reward,omitempty"`         //心愿值奖励
	Accumulate    int64                  `protobuf:"varint,3,opt,name=accumulate,proto3" json:"accumulate,omitempty"` //累计次数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NoticeWishCheckinData) Reset() {
	*x = NoticeWishCheckinData{}
	mi := &file_checkin_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoticeWishCheckinData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoticeWishCheckinData) ProtoMessage() {}

func (x *NoticeWishCheckinData) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoticeWishCheckinData.ProtoReflect.Descriptor instead.
func (*NoticeWishCheckinData) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{58}
}

func (x *NoticeWishCheckinData) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *NoticeWishCheckinData) GetReward() int64 {
	if x != nil {
		return x.Reward
	}
	return 0
}

func (x *NoticeWishCheckinData) GetAccumulate() int64 {
	if x != nil {
		return x.Accumulate
	}
	return 0
}

type NoticeWishCheckinReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NoticeWishCheckinReq) Reset() {
	*x = NoticeWishCheckinReq{}
	mi := &file_checkin_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoticeWishCheckinReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoticeWishCheckinReq) ProtoMessage() {}

func (x *NoticeWishCheckinReq) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoticeWishCheckinReq.ProtoReflect.Descriptor instead.
func (*NoticeWishCheckinReq) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{59}
}

type NoticeWishCheckinResp struct {
	state            protoimpl.MessageState   `protogen:"open.v1"`
	WishCheckinDatas []*NoticeWishCheckinData `protobuf:"bytes,1,rep,name=wishCheckinDatas,proto3" json:"wishCheckinDatas,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *NoticeWishCheckinResp) Reset() {
	*x = NoticeWishCheckinResp{}
	mi := &file_checkin_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoticeWishCheckinResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoticeWishCheckinResp) ProtoMessage() {}

func (x *NoticeWishCheckinResp) ProtoReflect() protoreflect.Message {
	mi := &file_checkin_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoticeWishCheckinResp.ProtoReflect.Descriptor instead.
func (*NoticeWishCheckinResp) Descriptor() ([]byte, []int) {
	return file_checkin_proto_rawDescGZIP(), []int{60}
}

func (x *NoticeWishCheckinResp) GetWishCheckinDatas() []*NoticeWishCheckinData {
	if x != nil {
		return x.WishCheckinDatas
	}
	return nil
}

var File_checkin_proto protoreflect.FileDescriptor

var file_checkin_proto_rawDesc = string([]byte{
	0x0a, 0x0d, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x02, 0x70, 0x62, 0x22, 0xad, 0x01, 0x0a, 0x0d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x34, 0x0a,
	0x15, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x69, 0x6e, 0x44, 0x61, 0x79, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x15, 0x63, 0x6f,
	0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x44,
	0x61, 0x79, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x6c, 0x61, 0x73,
	0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x44,
	0x61, 0x74, 0x65, 0x22, 0xa3, 0x01, 0x0a, 0x13, 0x41, 0x64, 0x64, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x69, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x15, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75,
	0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x44, 0x61, 0x79, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x15, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x69, 0x6e, 0x44, 0x61, 0x79, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x28, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x44, 0x61,
	0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x69, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x22, 0x16, 0x0a, 0x14, 0x41, 0x64, 0x64,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x22, 0xd2, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x69, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x15, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f,
	0x75, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x44, 0x61, 0x79, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x15, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x44, 0x61, 0x79, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x28, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x44,
	0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e,
	0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x6e,
	0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x22, 0x81, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x34, 0x0a, 0x15, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x44, 0x61, 0x79, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x15, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x69, 0x6e, 0x44, 0x61, 0x79, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x22, 0x35, 0x0a, 0x1b, 0x47, 0x65,
	0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42, 0x79,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x22, 0xa4, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x34, 0x0a, 0x15, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x44, 0x61, 0x79, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x15, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x69, 0x6e, 0x44, 0x61, 0x79, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x75,
	0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73,
	0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x4e, 0x0a, 0x08, 0x49, 0x6e, 0x74, 0x65,
	0x67, 0x72, 0x61, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x22, 0x44, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x49,
	0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x22, 0x11,
	0x0a, 0x0f, 0x41, 0x64, 0x64, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x57, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67,
	0x72, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x22, 0x14, 0x0a, 0x12, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x31, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x42,
	0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x22, 0x35, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72,
	0x61, 0x6c, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1a,
	0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x22, 0x47, 0x0a, 0x11, 0x44, 0x65,
	0x64, 0x75, 0x63, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x12,
	0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x67,
	0x72, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x67,
	0x72, 0x61, 0x6c, 0x22, 0x14, 0x0a, 0x12, 0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x49, 0x6e, 0x74,
	0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x22, 0x6e, 0x0a, 0x0e, 0x49, 0x6e, 0x74,
	0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x64, 0x0a, 0x14, 0x41, 0x64, 0x64,
	0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x74,
	0x65, 0x67, 0x72, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x6e, 0x74,
	0x65, 0x67, 0x72, 0x61, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22,
	0x17, 0x0a, 0x15, 0x41, 0x64, 0x64, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x22, 0x77, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x69,
	0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69,
	0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x22, 0x1a, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67,
	0x72, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x22, 0x26, 0x0a,
	0x14, 0x44, 0x65, 0x6c, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x17, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x49, 0x6e, 0x74, 0x65,
	0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2a,
	0x0a, 0x18, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x57, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42,
	0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3a, 0x0a, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x67,
	0x72, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x70, 0x62, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x22, 0x36, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72,
	0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x5b, 0x0a, 0x1d, 0x47,
	0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3a, 0x0a, 0x0e,
	0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x70, 0x62, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72,
	0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72,
	0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x22, 0xa1, 0x01, 0x0a, 0x17, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72,
	0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72,
	0x61, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x56, 0x0a, 0x18,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3a, 0x0a, 0x0e, 0x69, 0x6e, 0x74, 0x65,
	0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x70, 0x62, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x22, 0xc3, 0x01, 0x0a, 0x0c, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x34,
	0x0a, 0x15, 0x69, 0x73, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x74, 0x65, 0x64,
	0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x15, 0x69,
	0x73, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x74,
	0x74, 0x65, 0x72, 0x79, 0x12, 0x2e, 0x0a, 0x12, 0x69, 0x73, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61,
	0x74, 0x65, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x12, 0x69, 0x73, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x74,
	0x74, 0x65, 0x72, 0x79, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x69, 0x73,
	0x53, 0x75, 0x62, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x22, 0x2c, 0x0a, 0x12, 0x47, 0x65,
	0x74, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71,
	0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x4f, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x1a, 0x0a, 0x08, 0x64, 0x61, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x64, 0x61, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x77,
	0x65, 0x65, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x77, 0x65, 0x65, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x3c, 0x0a, 0x0c, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x75, 0x62, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0x0f, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x75, 0x62, 0x52, 0x65, 0x73, 0x70, 0x22, 0x80, 0x01, 0x0a, 0x0a, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x69,
	0x73, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x69, 0x73, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x22, 0x76, 0x0a, 0x10, 0x41,
	0x64, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12,
	0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x69, 0x73, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x69, 0x73, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x22, 0x13, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x22, 0x75, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x69, 0x73, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x69, 0x73, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x22,
	0x16, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x22, 0x22, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x13, 0x0a, 0x11, 0x44,
	0x65, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x26, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x47, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x2e, 0x0a, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x62, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x22, 0x33, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x42, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x25, 0x0a, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x70, 0x62, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x73,
	0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x9f, 0x01, 0x0a, 0x13, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52,
	0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a,
	0x69, 0x73, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x69, 0x73, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x22, 0x46, 0x0a, 0x14,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x2e, 0x0a, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x62, 0x2e, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x22, 0xc9, 0x01, 0x0a, 0x05, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x73, 0x46, 0x69,
	0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x69, 0x73,
	0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x65, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6e, 0x65, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0x57, 0x0a, 0x0b, 0x41, 0x64, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x22, 0x0e, 0x0a, 0x0c, 0x41, 0x64, 0x64,
	0x54, 0x61, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x6a, 0x0a, 0x0e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x74,
	0x65, 0x67, 0x72, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x6e, 0x74,
	0x65, 0x67, 0x72, 0x61, 0x6c, 0x22, 0x11, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x61, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x1d, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x54,
	0x61, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x0e, 0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x54, 0x61,
	0x73, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x21, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x54, 0x61,
	0x73, 0x6b, 0x73, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x33, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1f,
	0x0a, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x09, 0x2e,
	0x70, 0x62, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x52, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x22,
	0x94, 0x01, 0x0a, 0x0e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x52,
	0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e,
	0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x6e,
	0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x22, 0x32, 0x0a, 0x0f, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x54, 0x61, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1f, 0x0a, 0x05, 0x74, 0x61, 0x73,
	0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x70, 0x62, 0x2e, 0x54, 0x61,
	0x73, 0x6b, 0x73, 0x52, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x22, 0x67, 0x0a, 0x15, 0x4e, 0x6f,
	0x74, 0x69, 0x63, 0x65, 0x57, 0x69, 0x73, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x75, 0x6d, 0x75, 0x6c, 0x61, 0x74,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x75, 0x6d, 0x75, 0x6c,
	0x61, 0x74, 0x65, 0x22, 0x16, 0x0a, 0x14, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x57, 0x69, 0x73,
	0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x22, 0x5e, 0x0a, 0x15, 0x4e,
	0x6f, 0x74, 0x69, 0x63, 0x65, 0x57, 0x69, 0x73, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x45, 0x0a, 0x10, 0x77, 0x69, 0x73, 0x68, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x69, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x70, 0x62, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x57, 0x69, 0x73, 0x68, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x69, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x10, 0x77, 0x69, 0x73, 0x68, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x73, 0x32, 0xad, 0x0e, 0x0a, 0x07,
	0x63, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x12, 0x45, 0x0a, 0x10, 0x41, 0x64, 0x64, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x69, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x17, 0x2e, 0x70, 0x62,
	0x2e, 0x41, 0x64, 0x64, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x69, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4e,
	0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x1a, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x1a, 0x1b, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x69, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x5d,
	0x0a, 0x18, 0x47, 0x65, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x2e, 0x70, 0x62, 0x2e,
	0x47, 0x65, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x70, 0x62,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x36, 0x0a,
	0x0b, 0x41, 0x64, 0x64, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x12, 0x12, 0x2e, 0x70,
	0x62, 0x2e, 0x41, 0x64, 0x64, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x71,
	0x1a, 0x13, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x48, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x49, 0x6e, 0x74, 0x65,
	0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x18, 0x2e, 0x70, 0x62, 0x2e,
	0x41, 0x64, 0x64, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x49, 0x6e, 0x74,
	0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x51, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61,
	0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x1b, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x48, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61,
	0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x18, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c,
	0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x1a, 0x19, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72,
	0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x54, 0x0a, 0x15,
	0x47, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x42, 0x79, 0x49, 0x64, 0x12, 0x1c, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e,
	0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42, 0x79, 0x49, 0x64,
	0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65,
	0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x60, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61,
	0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x20, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65,
	0x71, 0x1a, 0x21, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72,
	0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x51, 0x0a, 0x14, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x49, 0x6e,
	0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x1b, 0x2e, 0x70,
	0x62, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x70, 0x62, 0x2e, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x0e, 0x44, 0x65, 0x64, 0x75, 0x63,
	0x74, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x12, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x44,
	0x65, 0x64, 0x75, 0x63, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x71,
	0x1a, 0x16, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x49, 0x6e, 0x74, 0x65,
	0x67, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3c, 0x0a, 0x0d, 0x41, 0x64, 0x64, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x14, 0x2e, 0x70, 0x62, 0x2e, 0x41,
	0x64, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a,
	0x15, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x45, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x17, 0x2e, 0x70, 0x62, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3c, 0x0a,
	0x0d, 0x44, 0x65, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x14,
	0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x48, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42, 0x79, 0x49, 0x64,
	0x12, 0x18, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x70, 0x62, 0x2e,
	0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42, 0x79, 0x49,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x54, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c,
	0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x70,
	0x62, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42,
	0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x45, 0x0a, 0x10, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12,
	0x17, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x2d, 0x0a, 0x08, 0x41, 0x64, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x0f,
	0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x1a,
	0x10, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x36, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x73,
	0x12, 0x12, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b,
	0x73, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2d, 0x0a, 0x08, 0x44, 0x65, 0x6c,
	0x54, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x0f, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c, 0x54, 0x61,
	0x73, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c, 0x54,
	0x61, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x39, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x73, 0x42, 0x79, 0x49, 0x64, 0x12, 0x13, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65,
	0x74, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e,
	0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x42, 0x79, 0x49, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x36, 0x0a, 0x0b, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x61, 0x73,
	0x6b, 0x73, 0x12, 0x12, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x61,
	0x73, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x42, 0x0a, 0x0f, 0x47,
	0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x16,
	0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x30, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x75, 0x62, 0x12, 0x10, 0x2e, 0x70,
	0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x75, 0x62, 0x52, 0x65, 0x71, 0x1a, 0x11,
	0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x75, 0x62, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x48, 0x0a, 0x11, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x57, 0x69, 0x73, 0x68, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x12, 0x18, 0x2e, 0x70, 0x62, 0x2e, 0x4e, 0x6f, 0x74, 0x69,
	0x63, 0x65, 0x57, 0x69, 0x73, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x52, 0x65, 0x71,
	0x1a, 0x19, 0x2e, 0x70, 0x62, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x57, 0x69, 0x73, 0x68,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4e, 0x0a, 0x13, 0x47,
	0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1a, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x67,
	0x72, 0x61, 0x6c, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1b,
	0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x42,
	0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x42, 0x06, 0x5a, 0x04, 0x2e,
	0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_checkin_proto_rawDescOnce sync.Once
	file_checkin_proto_rawDescData []byte
)

func file_checkin_proto_rawDescGZIP() []byte {
	file_checkin_proto_rawDescOnce.Do(func() {
		file_checkin_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_checkin_proto_rawDesc), len(file_checkin_proto_rawDesc)))
	})
	return file_checkin_proto_rawDescData
}

var file_checkin_proto_msgTypes = make([]protoimpl.MessageInfo, 61)
var file_checkin_proto_goTypes = []any{
	(*CheckinRecord)(nil),                 // 0: pb.CheckinRecord
	(*AddCheckinRecordReq)(nil),           // 1: pb.AddCheckinRecordReq
	(*AddCheckinRecordResp)(nil),          // 2: pb.AddCheckinRecordResp
	(*UpdateCheckinRecordReq)(nil),        // 3: pb.UpdateCheckinRecordReq
	(*UpdateCheckinRecordResp)(nil),       // 4: pb.UpdateCheckinRecordResp
	(*GetCheckinRecordByUserIdReq)(nil),   // 5: pb.GetCheckinRecordByUserIdReq
	(*GetCheckinRecordByUserIdResp)(nil),  // 6: pb.GetCheckinRecordByUserIdResp
	(*Integral)(nil),                      // 7: pb.Integral
	(*AddIntegralReq)(nil),                // 8: pb.AddIntegralReq
	(*AddIntegralResp)(nil),               // 9: pb.AddIntegralResp
	(*UpdateIntegralReq)(nil),             // 10: pb.UpdateIntegralReq
	(*UpdateIntegralResp)(nil),            // 11: pb.UpdateIntegralResp
	(*GetIntegralByUserIdReq)(nil),        // 12: pb.GetIntegralByUserIdReq
	(*GetIntegralByUserIdResp)(nil),       // 13: pb.GetIntegralByUserIdResp
	(*DeductIntegralReq)(nil),             // 14: pb.DeductIntegralReq
	(*DeductIntegralResp)(nil),            // 15: pb.DeductIntegralResp
	(*IntegralRecord)(nil),                // 16: pb.IntegralRecord
	(*AddIntegralRecordReq)(nil),          // 17: pb.AddIntegralRecordReq
	(*AddIntegralRecordResp)(nil),         // 18: pb.AddIntegralRecordResp
	(*UpdateIntegralRecordReq)(nil),       // 19: pb.UpdateIntegralRecordReq
	(*UpdateIntegralRecordResp)(nil),      // 20: pb.UpdateIntegralRecordResp
	(*DelIntegralRecordReq)(nil),          // 21: pb.DelIntegralRecordReq
	(*DelIntegralRecordResp)(nil),         // 22: pb.DelIntegralRecordResp
	(*GetIntegralRecordByIdReq)(nil),      // 23: pb.GetIntegralRecordByIdReq
	(*GetIntegralRecordByIdResp)(nil),     // 24: pb.GetIntegralRecordByIdResp
	(*GetIntegralRecordByUserIdReq)(nil),  // 25: pb.GetIntegralRecordByUserIdReq
	(*GetIntegralRecordByUserIdResp)(nil), // 26: pb.GetIntegralRecordByUserIdResp
	(*SearchIntegralRecordReq)(nil),       // 27: pb.SearchIntegralRecordReq
	(*SearchIntegralRecordResp)(nil),      // 28: pb.SearchIntegralRecordResp
	(*TaskProgress)(nil),                  // 29: pb.TaskProgress
	(*GetTaskProgressReq)(nil),            // 30: pb.GetTaskProgressReq
	(*GetTaskProgressResp)(nil),           // 31: pb.GetTaskProgressResp
	(*UpdateSubReq)(nil),                  // 32: pb.UpdateSubReq
	(*UpdateSubResp)(nil),                 // 33: pb.UpdateSubResp
	(*TaskRecord)(nil),                    // 34: pb.TaskRecord
	(*AddTaskRecordReq)(nil),              // 35: pb.AddTaskRecordReq
	(*AddTaskRecordResp)(nil),             // 36: pb.AddTaskRecordResp
	(*UpdateTaskRecordReq)(nil),           // 37: pb.UpdateTaskRecordReq
	(*UpdateTaskRecordResp)(nil),          // 38: pb.UpdateTaskRecordResp
	(*DelTaskRecordReq)(nil),              // 39: pb.DelTaskRecordReq
	(*DelTaskRecordResp)(nil),             // 40: pb.DelTaskRecordResp
	(*GetTaskRecordByIdReq)(nil),          // 41: pb.GetTaskRecordByIdReq
	(*GetTaskRecordByIdResp)(nil),         // 42: pb.GetTaskRecordByIdResp
	(*GetTaskRecordByUserIdReq)(nil),      // 43: pb.GetTaskRecordByUserIdReq
	(*GetTaskRecordByUserIdResp)(nil),     // 44: pb.GetTaskRecordByUserIdResp
	(*SearchTaskRecordReq)(nil),           // 45: pb.SearchTaskRecordReq
	(*SearchTaskRecordResp)(nil),          // 46: pb.SearchTaskRecordResp
	(*Tasks)(nil),                         // 47: pb.Tasks
	(*AddTasksReq)(nil),                   // 48: pb.AddTasksReq
	(*AddTasksResp)(nil),                  // 49: pb.AddTasksResp
	(*UpdateTasksReq)(nil),                // 50: pb.UpdateTasksReq
	(*UpdateTasksResp)(nil),               // 51: pb.UpdateTasksResp
	(*DelTasksReq)(nil),                   // 52: pb.DelTasksReq
	(*DelTasksResp)(nil),                  // 53: pb.DelTasksResp
	(*GetTasksByIdReq)(nil),               // 54: pb.GetTasksByIdReq
	(*GetTasksByIdResp)(nil),              // 55: pb.GetTasksByIdResp
	(*SearchTasksReq)(nil),                // 56: pb.SearchTasksReq
	(*SearchTasksResp)(nil),               // 57: pb.SearchTasksResp
	(*NoticeWishCheckinData)(nil),         // 58: pb.NoticeWishCheckinData
	(*NoticeWishCheckinReq)(nil),          // 59: pb.NoticeWishCheckinReq
	(*NoticeWishCheckinResp)(nil),         // 60: pb.NoticeWishCheckinResp
}
var file_checkin_proto_depIdxs = []int32{
	16, // 0: pb.GetIntegralRecordByIdResp.integralRecord:type_name -> pb.IntegralRecord
	16, // 1: pb.GetIntegralRecordByUserIdResp.integralRecord:type_name -> pb.IntegralRecord
	16, // 2: pb.SearchIntegralRecordResp.integralRecord:type_name -> pb.IntegralRecord
	34, // 3: pb.GetTaskRecordByIdResp.taskRecord:type_name -> pb.TaskRecord
	47, // 4: pb.GetTaskRecordByUserIdResp.taskList:type_name -> pb.Tasks
	34, // 5: pb.SearchTaskRecordResp.taskRecord:type_name -> pb.TaskRecord
	47, // 6: pb.GetTasksByIdResp.tasks:type_name -> pb.Tasks
	47, // 7: pb.SearchTasksResp.tasks:type_name -> pb.Tasks
	58, // 8: pb.NoticeWishCheckinResp.wishCheckinDatas:type_name -> pb.NoticeWishCheckinData
	1,  // 9: pb.checkin.AddCheckinRecord:input_type -> pb.AddCheckinRecordReq
	3,  // 10: pb.checkin.UpdateCheckinRecord:input_type -> pb.UpdateCheckinRecordReq
	5,  // 11: pb.checkin.GetCheckinRecordByUserId:input_type -> pb.GetCheckinRecordByUserIdReq
	8,  // 12: pb.checkin.AddIntegral:input_type -> pb.AddIntegralReq
	17, // 13: pb.checkin.AddIntegralRecord:input_type -> pb.AddIntegralRecordReq
	19, // 14: pb.checkin.UpdateIntegralRecord:input_type -> pb.UpdateIntegralRecordReq
	21, // 15: pb.checkin.DelIntegralRecord:input_type -> pb.DelIntegralRecordReq
	23, // 16: pb.checkin.GetIntegralRecordById:input_type -> pb.GetIntegralRecordByIdReq
	25, // 17: pb.checkin.GetIntegralRecordByUserId:input_type -> pb.GetIntegralRecordByUserIdReq
	27, // 18: pb.checkin.SearchIntegralRecord:input_type -> pb.SearchIntegralRecordReq
	14, // 19: pb.checkin.DeductIntegral:input_type -> pb.DeductIntegralReq
	35, // 20: pb.checkin.AddTaskRecord:input_type -> pb.AddTaskRecordReq
	37, // 21: pb.checkin.UpdateTaskRecord:input_type -> pb.UpdateTaskRecordReq
	39, // 22: pb.checkin.DelTaskRecord:input_type -> pb.DelTaskRecordReq
	41, // 23: pb.checkin.GetTaskRecordById:input_type -> pb.GetTaskRecordByIdReq
	43, // 24: pb.checkin.GetTaskRecordByUserId:input_type -> pb.GetTaskRecordByUserIdReq
	45, // 25: pb.checkin.SearchTaskRecord:input_type -> pb.SearchTaskRecordReq
	48, // 26: pb.checkin.AddTasks:input_type -> pb.AddTasksReq
	50, // 27: pb.checkin.UpdateTasks:input_type -> pb.UpdateTasksReq
	52, // 28: pb.checkin.DelTasks:input_type -> pb.DelTasksReq
	54, // 29: pb.checkin.GetTasksById:input_type -> pb.GetTasksByIdReq
	56, // 30: pb.checkin.SearchTasks:input_type -> pb.SearchTasksReq
	30, // 31: pb.checkin.GetTaskProgress:input_type -> pb.GetTaskProgressReq
	32, // 32: pb.checkin.UpdateSub:input_type -> pb.UpdateSubReq
	59, // 33: pb.checkin.NoticeWishCheckin:input_type -> pb.NoticeWishCheckinReq
	12, // 34: pb.checkin.GetIntegralByUserId:input_type -> pb.GetIntegralByUserIdReq
	2,  // 35: pb.checkin.AddCheckinRecord:output_type -> pb.AddCheckinRecordResp
	4,  // 36: pb.checkin.UpdateCheckinRecord:output_type -> pb.UpdateCheckinRecordResp
	6,  // 37: pb.checkin.GetCheckinRecordByUserId:output_type -> pb.GetCheckinRecordByUserIdResp
	9,  // 38: pb.checkin.AddIntegral:output_type -> pb.AddIntegralResp
	18, // 39: pb.checkin.AddIntegralRecord:output_type -> pb.AddIntegralRecordResp
	20, // 40: pb.checkin.UpdateIntegralRecord:output_type -> pb.UpdateIntegralRecordResp
	22, // 41: pb.checkin.DelIntegralRecord:output_type -> pb.DelIntegralRecordResp
	24, // 42: pb.checkin.GetIntegralRecordById:output_type -> pb.GetIntegralRecordByIdResp
	26, // 43: pb.checkin.GetIntegralRecordByUserId:output_type -> pb.GetIntegralRecordByUserIdResp
	28, // 44: pb.checkin.SearchIntegralRecord:output_type -> pb.SearchIntegralRecordResp
	15, // 45: pb.checkin.DeductIntegral:output_type -> pb.DeductIntegralResp
	36, // 46: pb.checkin.AddTaskRecord:output_type -> pb.AddTaskRecordResp
	38, // 47: pb.checkin.UpdateTaskRecord:output_type -> pb.UpdateTaskRecordResp
	40, // 48: pb.checkin.DelTaskRecord:output_type -> pb.DelTaskRecordResp
	42, // 49: pb.checkin.GetTaskRecordById:output_type -> pb.GetTaskRecordByIdResp
	44, // 50: pb.checkin.GetTaskRecordByUserId:output_type -> pb.GetTaskRecordByUserIdResp
	46, // 51: pb.checkin.SearchTaskRecord:output_type -> pb.SearchTaskRecordResp
	49, // 52: pb.checkin.AddTasks:output_type -> pb.AddTasksResp
	51, // 53: pb.checkin.UpdateTasks:output_type -> pb.UpdateTasksResp
	53, // 54: pb.checkin.DelTasks:output_type -> pb.DelTasksResp
	55, // 55: pb.checkin.GetTasksById:output_type -> pb.GetTasksByIdResp
	57, // 56: pb.checkin.SearchTasks:output_type -> pb.SearchTasksResp
	31, // 57: pb.checkin.GetTaskProgress:output_type -> pb.GetTaskProgressResp
	33, // 58: pb.checkin.UpdateSub:output_type -> pb.UpdateSubResp
	60, // 59: pb.checkin.NoticeWishCheckin:output_type -> pb.NoticeWishCheckinResp
	13, // 60: pb.checkin.GetIntegralByUserId:output_type -> pb.GetIntegralByUserIdResp
	35, // [35:61] is the sub-list for method output_type
	9,  // [9:35] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_checkin_proto_init() }
func file_checkin_proto_init() {
	if File_checkin_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_checkin_proto_rawDesc), len(file_checkin_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   61,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_checkin_proto_goTypes,
		DependencyIndexes: file_checkin_proto_depIdxs,
		MessageInfos:      file_checkin_proto_msgTypes,
	}.Build()
	File_checkin_proto = out.File
	file_checkin_proto_goTypes = nil
	file_checkin_proto_depIdxs = nil
}
