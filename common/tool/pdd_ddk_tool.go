package tool

import (
	"crypto/md5"
	"encoding/hex"
	"io"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"
)

const (
	clientId     = "dcdb539289de4b66a16e4ac4d66353bb"
	ClientSecret = "4ded09b0a14eb72196f790a93c06eafcfb75ceb8"
	pid          = "42316735_301157319"
	endpoint     = "http://gw-api.pinduoduo.com/api/router"
)

// GenerateSign generates the sign value for the given parameters and client secret.
func GenerateSign(params map[string]string, clientSecret string) string {
	// Sort the parameters by key in ASCII order
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// Concatenate the parameters in the format key=value
	var sb strings.Builder
	for _, k := range keys {
		sb.WriteString(k)
		sb.WriteString(params[k])
	}

	// Prepend and append client_secret
	signStr := clientSecret + sb.String() + clientSecret

	// Generate MD5 hash
	hash := md5.Sum([]byte(signStr))
	sign := strings.ToUpper(hex.EncodeToString(hash[:]))

	return sign
}

// SendRequestToDDJB sends a POST request with the given parameters and returns the response body as a string.
func SendRequestToDDJB(params map[string]string, method string) ([]byte, error) {
	params["client_id"] = clientId
	params["data_type"] = "JSON"
	params["timestamp"] = strconv.FormatInt(time.Now().Unix(), 10)

	sign := GenerateSign(params, ClientSecret)

	// Construct request parameters
	data := url.Values{}
	for key, value := range params {
		//logx.Errorf("Testt-5 key: %s, value: %s", key, value)
		data.Add(key, value)
	}
	data.Add("sign", sign)

	// Construct HTTP request
	request, err := http.NewRequest(method, endpoint, strings.NewReader(data.Encode()))
	if err != nil {
		return nil, err
	}

	// Set request headers
	request.Header.Set("Content-Type", "application/x-www-form-urlencoded; charset=utf-8")

	// Log the request details
	//logx.Infof("Request URL: %s", request.URL.String())
	//logx.Infof("Request Method: %s", request.Method)
	//logx.Infof("Request Headers: %v", request.Header)
	//logx.Infof("Request Body: %s", data.Encode())

	// Send request
	resp, err := http.DefaultClient.Do(request)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	return body, nil
}
