syntax = "v1"

info (
	title:   "type title here"
	desc:    "type desc here"
	author:  "type author here"
	email:   "type email here"
	version: "type version here"
)

import (
	"goods/goods.api"
)

//@server (
//	prefix: shop/v1
//	group:  shop
//	jwt:    JwtAuth
//)
//service shop {
//	@doc "查询商品信息"
//	@handler getGoodsById
//	post /goodsInfo/getGoodsById (GoodsInfoReq) returns (GoodsInfoResp)
//}

// @server (
//	prefix: shop/v1
//	group:  shop
//	jwt:    JwtAuth
//)
//service shop {
//	@doc "查询所有商品信息"
//	@handler getGoodsList
//	post /goodsInfo/getGoodsList (GoodsListReq) returns (GoodsListResp)
//}

//@server (
//	prefix: shop/v1
//	group:  shop
//	jwt:    JwtAuth
//)
//service shop {
//	@doc "同步拼多多数据"
//	@handler syncPddGoods
//	post /goodsInfo/syncPddGoods
//}

@server (
	prefix: shop/v1
	group:  shop
	jwt:    JwtAuth
)
service shop {
	@doc "查询商品列表"
	@handler getGoodsList
	post /getGoodsList (GoodsListReq) returns (string)
}

// 商品详情接口
@server (
	prefix: shop/v1
	group:  shop
	jwt:    JwtAuth
)
service shop {
	@doc "查询商品详情"
	@handler getGoodsDetail
	post /getGoodsDetail (GoodsDetailReq) returns (string)
}

// 商品搜索接口
@server (
	prefix: shop/v1
	group:  shop
	jwt:    JwtAuth
)
service shop {
	@doc "商品搜索"
	@handler searchGoods
	post /searchGoods (GoodsSearchReq) returns (string)
}

// 心愿兑换接口
@server (
	prefix: shop/v1
	group:  shop
	jwt:    JwtAuth
)
service shop {
	@doc "心愿兑换"
	@handler wishExchange
	post /wishExchange (WishExchangeReq) returns (WishExchangeResp)
}

// 心愿兑换列表接口
@server (
	prefix: shop/v1
	group:  shop
	jwt:    JwtAuth
)
service shop {
	@doc "心愿兑换列表"
	@handler wishExchangeList
	post /wishExchangeList (WishExchangeListReq) returns (string)
}

// 订单列表查询接口
@server (
	prefix: shop/v1
	group:  shop
	jwt:    JwtAuth
)
service shop {
	@doc "订单列表查询"
	@handler orderList
	post /orderList (OrderListReq) returns (string)
}

// 查询备案情况并返回pdd备案url
@server (
	prefix: shop/v1
	group:  shop
	jwt:    JwtAuth
)
service shop {
	@doc "查询备案情况并返回pdd备案url"
	@handler getPddAuth
	post /getPddAuth (PddAuthReq) returns (PddAuthResp)
}

// 多多进宝推广链接生成
@server (
	prefix: shop/v1
	group:  shop
	jwt:    JwtAuth
)
service shop {
	@doc "多多进宝推广链接生成"
	@handler genPddPromotionUrl
	post /genPddPromotionUrl (GenPddPromotionUrlReq) returns (string)
}