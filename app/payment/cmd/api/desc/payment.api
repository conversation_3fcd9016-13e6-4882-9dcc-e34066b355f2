syntax = "v1"

info(
	title: "支付服务"
	desc: "支付服务"
	author: "<PERSON><PERSON><PERSON>"
	email: "<EMAIL>"
	version: "v1"
)

import(
	"thirdPayment/thirdPayment.api"
)

//==============================> payment v1 <========================================
//no need login
@server(
	prefix: payment/v1
	group: thirdPayment
)
service payment {
	
	@doc "third payment：wechat pay callback"
	@handler thirdPaymentWxPayCallback
	post /thirdPayment/thirdPaymentWxPayCallback (ThirdPaymentWxPayCallbackReq) returns (ThirdPaymentWxPayCallbackResp)
}

//need login
@server(
	prefix: payment/v1
	group: thirdPayment
	jwt: JwtAuth
)
service payment {
	@doc "third payment：wechat pay"
	@handler thirdPaymentwxPay
	post /thirdPayment/thirdPaymentWxPay (ThirdPaymentWxPayReq) returns (ThirdPaymentWxPayResp)
}