### 一、项目简介

整个项目使用了go-zero开发的微服务，基本包含了go-zero以及相关go-zero作者开发的一些中间件，所用到的技术栈基本是go-zero项目组的自研组件，基本是go-zero全家桶了

本项目开发环境推荐docker-compose，使用直链方式，放弃服务注册发现中间件（etcd、nacos、consul等）带来的麻烦

测试、线上部署使用k8s（也不需要etcd、nacos、consul等）有详细教程（搭建+部署），可以进go-zero社区群沟通，非常easy

项目目录结构如下：
- app：所有业务代码包含api、rpc以及mq（消息队列、延迟队列、定时任务）
- common：通用组件 error、middleware、interceptor、tool、ctxdata等
- data：该项目包含该目录依赖所有中间件(mysql、es、redis、grafana等)产生的数据，此目录下的所有内容应该在git忽略文件中，不需要提交。
- deploy：
    - filebeat: docker部署filebeat配置
    - go-stash：go-stash配置
    - nginx: nginx网关配置
    - prometheus ： prometheus配置
    - script：
        - gencode：生成api、rpc，以及创建kafka语句，复制粘贴使用
        - mysql：生成model的sh工具
    - goctl: 该项目goctl的template，goctl生成自定义代码模版，template用法可参考go-zero文档，复制到家目录下.goctl即可
- doc : 该项目系列文档
- modd.conf :  modd热加载配置文件，不要怕～它用起来很简单，关于modd更多用法可以去这里了解 ： https://github.com/cortesi/modd ， 本项目镜像只是将golang-1.17.7-alpine作为基础镜像安装了modd在内部，如果你想把goctl、protoc、golint等加进去，不用我的镜像直接制作一个镜像也一样的哈

### 二、用到技术栈

##### Tips : 如果你不熟悉这里面很多技术栈也不要怕，只要你会mysql、redis可以先启动这两个中间件在启动项目先跑起来项目，其他可以慢慢学。

- k8s

- go-zero

- nginx网关

- filebeat

- kafka

- go-stash

- elasticsearch

- kibana

- prometheus

- grafana

- jaeger

- go-queue

- asynq

- asynqmon

- dtm

- docker

- docker-compose

- mysql

- redis

- modd

- jenkins

- gitlab

- harbor

### 三、项目架构图

![gozerolooklook](./images/1/gozerolooklook.png)

### 四、业务架构图
![gozerolooklook](./images/1/go-zero-looklook-service.png)

### 五、项目环境搭建

##### ⚠️搭建过程中如果遇到问题，可以看 "九、常见错误"



本项目采用modd热加载功即时修改代码及时生效，并且不需要每次都要重启，改了代码自动就在容器中重新加载了，本地不需要启动服务，本地安装的sdk就是写代码自动提示使用的，实际运行是以来容器中 lyumikael/go-modd-env:v1.0.0的golang环境。所以使用goland、vscode都一样



【注意】由于本项目本来中间件比较多，在非linux上启动docker可能会消耗内存较多，建议将物理机分配给docker的内存调到8G



#### 1、clone代码&更新依赖

```shell
*********************:60fadd729187b7df39056384/training_camp/lottery-backend.git
go mod tidy
```



#### 2、启动项目所依赖的环境

```shell
$ docker network create go-zero-looklook_looklook_net
$ docker-compose -f docker-compose-env.yml up -d
```



#### 3、导入数据

###### 3.1创建kafka topic

系统内使用了3个topic，默认是不允许程序自动创建topic的，进入kafka的容器创建3个topic

进入容器

```shell
$ docker exec -it kafka /bin/sh
$ cd /opt/kafka/bin/
```

创建3个topic

```shell
$ ./kafka-topics.sh --create --zookeeper zookeeper:2181 --replication-factor 1 -partitions 1 --topic looklook-log
$ ./kafka-topics.sh --create --zookeeper zookeeper:2181 --replication-factor 1 -partitions 1 --topic payment-update-paystatus-topic
```

looklook-log ： 日志收集使用的

payment-update-paystatus-topic ： 支付成功通知所有订阅者



###### 3.2导入mysql数据

本地工具连接mysql的话要先进入容器，给root设置下远程连接权限

```shell
$ docker exec -it mysql mysql -uroot -p
##输入密码：PXDN93VRKUm8TeE7
$ use mysql;
$ update user set host='%' where user='root';
$ FLUSH PRIVILEGES;
```

把deploy/sql/下面所有的sql文件导入到你自己的数据库中，比如：

创建数据库looklook_order && 导入deploy/sql/looklook_order.sql数据

创建数据库looklook_payment && 导入deploy/sql/looklook_payment.sql数据

创建数据库looklook_travel && 导入deploy/sql/looklook_travel.sql数据

创建数据库looklook_usercenter && 导入looklook_usercenter.sql数据

创建数据库lottery && 导入lottery.sql数据

还有其他的sql文件也按这种方式导入即可。

#### 4、查看服务环境

Elastic search: http://127.0.0.1:9200/ （⚠️：这个启动时间有点长）

jaeger: http://127.0.0.1:16686/search  (⚠️：如果失败了，依赖es，因为es启动时间长这个有可能超时，等es启动玩restart一下)

go-stash :  如果发现kibana点击下一步，就是日志没有收集到，恰巧你的kafka又拿到了数据的话，请restart一下go-stash稍等一分钟即可  (⚠️：如果你是mac m1或者 linux arm，请更改一下docker-compose-env.yml中go-stash镜像 kevinwan/go-stash:1.0-arm64 ，默认是linux amd的)

asynq （延迟任务、定时任务、消息队列）: http://127.0.0.1:8980/

kibana  : http://127.0.0.1:5601/

Prometheus: http://127.0.0.1:9090/

Grafana: http://127.0.0.1:3001/  ， 默认账号、密码都是admin

Mysql :  自行客户端工具(Navicat、Sequel Pro)查看

- host : 127.0.0.1

- port : 33069  

- username : root

- pwd : PXDN93VRKUm8TeE7 

Redis :  自行工具（redisManager）查看 

- host : 127.0.0.1

- port : 36379

- pwd : G62m50oigInC30sf

Kafka:  （发布、订阅｜pub、sub）自行客户端工具查看

- host : 127.0.0.1

- port : 9092



#### 5、启动服务

##### 5.1 拉取运行环境镜像

因为本项目是用docker+热加载，即改即生效 

前台app下所有api+rpc服务统一使用modd + golang

直接docker-compose去启动可以，但是考虑依赖可能会比较大，会影响启动项目，所以最好先把这个镜像拉取下来再去启动项目

```shell
$ docker pull lyumikael/gomodd:v1.20.3 #这个是app下所有的api+rpc启动服务使用的，如果你是 "mac m1" : lyumikael/go-modd-env:v1.0.0
```

【注】后续如果app下新增业务，要记得在项目根目录下的modd.conf复制添加一份就可以了

​			关于modd更多用法可以去这里了解 ： https://github.com/cortesi/modd ， 本项目镜像只是将golang-1.17.7-alpine作为基础镜像安装了modd在内部，

如果你想把goctl、protoc、golint等加进去，不用我的镜像直接制作一个镜像也一样的哈



##### 5.2 启动项目

```shell
$ docker-compose up -d 
```

【注】依赖的是项目根目录下的docker-compose.yml配置



#### 6、查看项目运行情况

访问 http://127.0.0.1:9090/ ， 点击上面菜单“Status”，在点击Targets ,蓝色的就是启动成了，红色就是没启动成功

【注】如果是第一次拉取项目，每个项目容器第一次构建拉取依赖，这个看网络情况，可能会稍微比较慢有的服务，这个很正常，如果碰到项目启动不起来的情况，比如order-api ，手动在order-api代码中随便写点啥保存一下触发重新编译看看日志就可以了

```shell
$ docker-compose logs -f 
```

可以看到prometheus也显示成功了，同理把其他的也排查一次，启动成功就可以了

<img src="./images/1/image-20220120103641110.png" alt="image-20220120103641110.png" style="zoom:33%;" />

#### 7、访问项目

由于我们使用nginx做的网关，nginx网关配置在docker-compose中，也是配置在docker-compose中，nginx对外暴露端口是8888，所以我们通过8888端口访问

```shell
$ curl  -X POST "http://127.0.0.1:8888/usercenter/v1/user/register" -H "Content-Type: application/json" -d "{\"mobile\":\"18888888888\",\"password\":\"123456\"}" 

返回:
{"code":200,"msg":"OK","data":{"accessToken":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NzM5NjY0MjUsImlhdCI6MTY0MjQzMDQyNSwiand0VXNlcklkIjo1fQ.E5-yMF0OvNpBcfr0WyDxuTq1SRWGC3yZb9_Xpxtzlyw","accessExpire":1673966425,"refreshAfter":1658198425}}
```

【注】 如果是访问nginx失败，访问成功可以忽略，可能是nginx依赖后端服务，之前因为后端服务没启动起来，nginx这里没启动起来，重启一次nginx即可,项目根目录下重启

```shell
$ docker-compose restart nginx
```

### 六、日志收集

将项目日志收集到es（filebeat收集日志->kafka -> go-stash消费kafka日志->输出到es中,kibana查看es数据）

访问kibana http://127.0.0.1:5601/ ， 创建日志索引

点击左上角菜单(三个横线那个东东)，找到Analytics - > 点击discover 

<img src="./images/1/image-20220120105829870.png" alt="image-20220120105829870" style="zoom:33%;" />

然后在当前页面，Create index pattern->输入looklook-*  -> Next Step ->选择@timestamp->Create index pattern

然后点击左上角菜单，找到Analytics->点击discover ，日志都显示了 （如果不显示，就去排查filebeat、go-stash，使用docker logs -f filebeat查看）

![image-20220120105947733](./images/1/image-20220120105947733.png)

⚠️常见收集失败原因

- kafka中没有创建log的topic ： looklook-log

  解决：去kafka创建 looklook-log，重启filebeat、go-stash

- go-stash后启动的

  解决：重启一次go-stash

- go-stash镜像使用错了 

  看看go-stash的log ，如果出现core dumped ， 就说明镜像使用错了。

  解答:

  如果你是mac m1或者 linux arm，请更改一下docker-compose-env.yml中go-stash镜像 kevinwan/go-stash:1.0-arm64 ，默认是linux amd的

- docker版本问题

  解答:

  这个我没有实际遇到，但是有同学使用docker版本是1.13遇到了，filebeat配置文件中配置收集docker的路径低版本docker可能位置不一样导致收集不到docker内部日志，最好升级一下docker18.03.1以上目前都没问题，17没有实际测试 ， 我这边用的docker版本是 Version: 20.10.8

- 内部kafka问题

  解答:

  1）docker logs 按照顺序检查kafka、filbeat、go-stash、es的容器日志，确认服务都没问题

  2）先docker logs -f filebeat查看filebeat是否正确连接到了kafka

  3）进入kafka容器内，执行消费kafka-log消息，看看是否filebeat的消息已经发送到了kafka

  ```shell
  $ docker exec -it kafka /bin/sh
  $ cd /opt/kafka/bin
  $ ./kafka-console-consumer.sh --bootstrap-server kafka:9092 --topic looklook-log 
  ```

  【注】如果能消费到消息，说明filebeat与kafka没问题，就去排查go-stash、es

  ​		   如果不能消费

  ​		  1）就应该是filebeat与kafka之间连接的问题，要去看下kafka的配置信息Listen是否修改了

  ​		  2）在kafka容器内部命令行使用consumer.sh消费looklook-log，另外一个终端命令行用producer.sh给looklook-log发送消息，如果consumer收不到，说明kafka出问题了，docker logs -f kafka看看什么问题

​	

### 七、本项目镜像介绍

所有服务启动成功，应该是如下这些，自行对比

![image-20220117221816047](./images/1/image-20220117221816047.png)

- nginx : 网关 （nginx->api->rpc）
- wurstmeister/kafka ： 业务使用的kafka
- wurstmeister/zookeeper ： kafka依赖的zookeeper
- redis：业务使用的redis
- mysql: 业务使用的数据库
- prom/prometheus：监控业务
- grafana/grafana ：prometheus的ui很难看，用来显示prometheus收集来的数据
- elastic/filebeat ： 收集日志到kafka
- go-stash : 消费kafka中日志，脱敏、过滤然后输出到es
- docker.elastic.co/elasticsearch/elasticsearch ： 存储收集的日志
- docker.elastic.co/kibana/kibana ： 显示elasticsearch
- jaegertracing/jaeger-query 、jaegertracing/jaeger-collector、jaegertracing/jaeger-agent：链路追踪
- go-stash : filebeat收集日志到kafka后，go-stash去消费kafka进行数据脱敏、过滤日志中内容，最后输出到es中

### 八、项目开发建议

- app下放所有业务服务代码

- common放所有服务的公共基础库

- data项目依赖中间件产生的数据，实际开发中应该在git中忽略此目录以及此目录下产生的数据

- 生成api、rpc代码：

一般我们在生成api，rpc代码时候手动去敲goctl的命令比较长，也记不住，所以我们直接去deploy/script/gencode/gen.sh中复制代码即可。比如我在usercenter服务中新增加了一个业务，修改密码，写完api文件之后，进入到usercenter/cmd/api/desc目录下，直接复制deploy/script/gencode/gen.sh中的生成api命令运行即可

```shell
$ goctl api go -api main.api -dir ../  -style=goZero
```

生成rpc也一样，在写完proto文件后，直接粘复制deploy/script/gencode/gen.sh中的生成rpc命令运行即可

goctl >= 1.3 进入"服务/cmd/rpc/pb"目录下，执行下面命令

```shell
$ goctl rpc protoc lottery.proto --go_out=../ --go-grpc_out=../  --zrpc_out=../
$ sed -i "" 's/,omitempty//g' *.pb.go
```

goctl < 1.3 进入"服务/cmd"目录下，执行下面命令

```shell
$  goctl rpc proto -src rpc/pb/*.proto -dir ./rpc -style=goZero
$  sed -i "" 's/,omitempty//g'  ./rpc/pb/*.pb.go
```

【注】建议在生成rpc文件时候，在多执行一次下面那个命令，把protobuf生成的omitempty给删除掉，不然字段为nil就不返回了

- 生成kafka代码：

  因为本项目使用了go-queue的kq做消息队列，kq又依赖的kafka，实际就是使用了kafka做消息队列，但是kq默认是需要我们提前把topic建好的，不许默认自动生成，所以命令也准备好了，直接复制deploy/script/gencode/gen.sh中的创建kafka的topic代码即可

  ```shell
   kafka-topics.sh --create --zookeeper zookeeper:2181 --replication-factor 1 -partitions 1 --topic {topic}
  ```

- 生成model代码，直接运行deploy/script/mysql/genModel.sh  参数

- api项目中的.api文件我们做了拆分，统一放到每个api的desc文件夹下，因为如果所有内容都写在api中可能不便于查看，所以做了拆分，把所有方法写到一个api中，其他的实体以及req、rep统一放到一个文件夹单独定义比较清晰

- 生成model、错误处理时候使用了template重新定义，该项目用到的自定义的goctl的模版在项目deploy/goctl下



### 九、搭建环境常见错误

```dock
1、创建阶段，起docker-compose-env.yml容器
Grafana 报错You may have issues with file permissions, more information here: http://docs.grafana.org/installation/docker/#migrate-to-v51-or-later
mkdir: can't create directory '/var/lib/grafana/plugins': Permission denied
因权限问题导致，可在docker-compose-env.yml中grafana部分加入user: root

2、filebeat容器启动报错Exiting: error loading config file: config file ("filebeat.yml") must be owned by the user identifier (uid=0) or root
因文件所有者不同导致（我在普通用户下clone的项目），filebeat的配置文件所有者必须为root，需修改sudo chown root deploy/filebeat/conf/filebeat.yml 

3、elasticsearch容器启动报错ElasticsearchException[failed to bind service]; nested: AccessDeniedException[/usr/share/elasticsearch/data/nodes];
Likely root cause: java.nio.file.AccessDeniedException: /usr/share/elasticsearch/data/nodes
报错原因es没有权限操作挂载目录，无法绑定节点，解决方法，修改权限sudo chmod 777 data/elasticsearch/data （不知道es是哪个用户启动的，所以硬改了777）

4、jaeger依赖于elasticsearch，且没有失败自动重启
```

### 十、后续

由于项目中由于涉及到的技术栈稍微有点多，将逐步分章节一点点添加