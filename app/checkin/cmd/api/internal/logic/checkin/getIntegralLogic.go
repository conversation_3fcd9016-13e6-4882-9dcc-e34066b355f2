package checkin

import (
	"context"
	"looklook/app/checkin/cmd/rpc/checkin"
	"looklook/common/ctxdata"

	"github.com/zeromicro/go-zero/core/logx"
	"looklook/app/checkin/cmd/api/internal/svc"
)

type GetIntegralLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetIntegralLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetIntegralLogic {
	return &GetIntegralLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetIntegralLogic) GetIntegral() (resp int64, err error) {
	// todo: add your logic here and delete this line

	userId := ctxdata.GetUidFromCtx(l.ctx)

	res, err := l.svcCtx.CheckinRpc.GetIntegralByUserId(l.ctx, &checkin.GetIntegralByUserIdReq{
		UserId: userId,
	})
	if err != nil {
		return 0, err
	}

	return res.Integral, nil
}
