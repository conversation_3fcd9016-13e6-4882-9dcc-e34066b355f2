package svc

import (
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/zrpc"
	"looklook/app/checkin/cmd/rpc/checkin"
	"looklook/app/shop/cmd/rpc/internal/config"
	"looklook/app/shop/model"
)

type ServiceContext struct {
	Config      config.Config
	RedisClient *redis.Redis

	GoodsModel            model.GoodsModel
	Order                 model.OrderModel
	WishRedemptionRecords model.WishRedemptionRecordsModel
	CheckinRpc            checkin.Checkin
}

func NewServiceContext(c config.Config) *ServiceContext {
	sqlConn := sqlx.NewMysql(c.DB.DataSource)
	return &ServiceContext{
		Config: c,
		RedisClient: redis.New(c.Redis.Host, func(r *redis.Redis) {
			r.Type = c.Redis.Type
			r.Pass = c.Redis.Pass
		}),
		GoodsModel:            model.NewGoodsModel(sqlConn, c.<PERSON><PERSON>),
		Order:                 model.NewOrderModel(sqlConn, c.Cache),
		WishRedemptionRecords: model.NewWishRedemptionRecordsModel(sqlConn, c.Cache),
		CheckinRpc:            checkin.NewCheckin(zrpc.MustNewClient(c.CheckinRpcConf)),
	}
}
