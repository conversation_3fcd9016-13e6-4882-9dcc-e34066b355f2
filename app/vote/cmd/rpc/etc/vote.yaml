Name: vote-rpc
ListenOn: 0.0.0.0:2012
Mode: dev

#监控
Prometheus:
  Host: 0.0.0.0
  Port: 4028
  Path: /metrics

#jwtAuth
JwtAuth:
  AccessSecret: ae0536f9-6450-4606-8e13-5a19ed505da0
  AccessExpire: 31536000

#Redis
Redis:
  Host: redis:6379
  Type: node
  Pass: G62m50oigInC30sf
  Key: vote-rpc

Log:
  ServiceName: vote-rpc
  Level: error
  # 打印堆栈信息 方便查询错误
  Encoding: plain

#DB
DB:
  DataSource: root:PXDN93VRKUm8TeE7@tcp(mysql:3306)/lottery?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai
Cache:
  - Host: redis:6379
    Pass: G62m50oigInC30sf