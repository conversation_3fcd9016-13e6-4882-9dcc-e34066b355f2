syntax = "v1"

info (
	title:   "用户中心服务"
	desc:    "用户中心服务"
	author:  "王中阳"
	email:   "<EMAIL>"
	version: "v1"
)

import (
	"user/user.api"
	"address/address.api"
	"userSponsor/userSponsor.api"
	"userContact/userContact.api"
	"userWinDynamicComment/userWinDynamicComment.api"
	"userDynamic/userDynamic.api"
)

//============================> usercenter v1 <============================
//no need login
@server (
	prefix: usercenter/v1
	group:  user
)
service usercenter {
	@doc "注册"
	@handler register
	post /user/register (RegisterReq) returns (RegisterResp)

	@doc "登录"
	@handler login
	post /user/login (LoginReq) returns (LoginResp)

	@doc "小程序注册登录"
	@handler wxMiniAuth
	post /user/wxMiniAuth (WXMiniAuthReq) returns (WXMiniAuthResp)
}

//need login
@server (
	prefix: usercenter/v1
	group:  user
	jwt:    JwtAuth
)
service usercenter {
	@doc "获得用户数据"
	@handler detail
	post /user/detail (UserInfoReq) returns (UserInfoResp)

	@doc "修改用户基本信息"
	@handler update
	put /user/update (UserUpdateReq) returns (UserUpdateResp)

	@doc "设置user为admin"
	@handler setAdmin
	post /user/setAdmin (SetAdminReq) returns (SetAdminResp)
}

//need login
@server (
	prefix: usercenter/v1
	group:  address
	jwt:    JwtAuth
)
service usercenter {
	@doc "收货地址列表"
	@handler addressList
	post /user/addressList (AddressListReq) returns (AddressListResp)

	@doc "添加收货地址"
	@handler addAddress
	post /user/addAddress (AddAddressReq) returns (AddAddressResp)

	@doc "识别并转换收货地址"
	@handler convertAddress
	post /user/convertAddress (ConvertAddressReq) returns (ConvertAddressResp)
}

//need login
@server (
	prefix: usercenter/v1
	group:  userSponsor
	jwt:    JwtAuth
)
service usercenter {
	@doc "添加 抽奖发起人（赞助商）"
	@handler addSponsor
	post /userSponsor/addSponsor (CreateSponsorReq) returns (CreateSponsorResp)

	@doc "我的赞助商列表（赞助商）"
	@handler sponsorList
	post /userSponsor/sponsorList (SponsorListReq) returns (SponsorListResp)

	@doc "修改抽奖发起人（赞助商）"
	@handler upDateSponsor
	post /userContact/upDateSponsor (UpdateSponsorReq) returns (UpdateSponsorResp)

	@doc "删除（赞助商）"
	@handler sponsorDel
	post /userContact/sponsorDel (sponsorDelReq) returns (sponsorDelResp)
}

//no need login
@server (
	prefix: usercenter/v1
	group:  userSponsor
)
service usercenter {
	@doc "抽奖发起人（赞助商）详情"
	@handler sponsorDetail
	post /userSponsor/sponsorDetail (SponosorDetailReq) returns (SponosorDetailResp)
}

//need login
@server (
	prefix: usercenter/v1
	group:  userContact
	jwt:    JwtAuth
)
service usercenter {
	@doc "添加抽奖发起人联系方式"
	@handler addContact
	post /userContact/addContact (CreateContactReq) returns (CreateContactResp)

	@doc "我的联系方式列表"
	@handler contactList
	post /userContact/contactList (ContactListReq) returns (ContactListResp)

	@doc "批量删除联系方式"
	@handler ContactDel
	post /userContact/ContactDel (ContactDelReq) returns (ContactDelResp)

	@doc "修改抽奖发起人联系方式"
	@handler upDateContact
	post /userContact/upDateContact (UpDateContactReq) returns (UpDateContactResp)
}

//need login
@server (
	prefix: usercenter/v1
	group:  userDynamic
	jwt:    JwtAuth
)
service usercenter {
	@doc "创建用户动态"
	@handler createDynamic
	post /user/createDynamic (CreateDynamicReq) returns (CreateDynamicResp)
}

//need login
@server (
	prefix: usercenter/v1
	group:  userDynamic
	jwt:    JwtAuth
)
service usercenter {
	@doc "删除用户动态"
	@handler deleteDynamic
	post /user/deleteDynamic (DeleteDynamicReq) returns (DeleteDynamicResp)
}

//need login
@server (
	prefix: usercenter/v1
	group:  userWonDynamicComment
	jwt:    JwtAuth
)
service usercenter {
	@doc "累计奖品发布动态用户晒单列表"
	@handler UserWonDynamicCommentList
	post /user/userWonDynamicCommentList (UserWonDynamicCommentReq) returns (UserWonDynamicCommentResp)
}

