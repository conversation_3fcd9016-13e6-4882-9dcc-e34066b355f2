package shop

import (
	"context"
	"encoding/json"
	"looklook/app/shop/cmd/rpc/shop"
	"looklook/common/ctxdata"

	"looklook/app/shop/cmd/api/internal/svc"
	"looklook/app/shop/cmd/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type OrderListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewOrderListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *OrderListLogic {
	return &OrderListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *OrderListLogic) OrderList(req *types.OrderListReq) (resp string, err error) {
	// todo: add your logic here and delete this line
	// 1、uid获取
	uid := ctxdata.GetUidFromCtx(l.ctx)
	// 2、调用rpc接口获取订单列表
	orderList, err := l.svcCtx.ShopRpc.GetOrderList(l.ctx, &shop.OrderListReq{
		UserId:   uid,
		Page:     1,
		PageSize: 100,
	})
	if err != nil {
		return "", err
	}

	// 3、返回订单列表
	res, _ := json.Marshal(orderList.List)

	return string(res), nil
}
