package logic

import (
	"context"
	"looklook/common/tool"
	"strconv"
	"time"

	"looklook/app/shop/cmd/rpc/internal/svc"
	"looklook/app/shop/cmd/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetOrderPullLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetOrderPullLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetOrderPullLogic {
	return &GetOrderPullLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetOrderPullLogic) GetOrderPull(in *pb.OrderPullReq) (*pb.OrderPullResp, error) {
	// todo: add your logic here and delete this line
	// 上游定时任务调用该rpc调用，拉取订单信息
	// 调用多多进宝的订单拉取接口，获取订单信息
	// http调用 pdd.ddk.order.list.increment.get
	// 将拉取到的订单信息落库

	params := map[string]string{
		"start_update_time": strconv.FormatInt(time.Now().Add(-time.Hour*24).Unix(), 10),
		"end_update_time":   strconv.FormatInt(time.Now().Unix(), 10),
		"type":              "pdd.ddk.order.list.increment.get",
		"client_secret":     tool.ClientSecret,
	}

	body, err := tool.SendRequestToDDJB(params, "POST")
	if err != nil {
		return nil, err
	}

	logx.Errorf("Testt-5 Response body: %s", string(body))

	// todo: 解析响应体，将订单信息落库
	return &pb.OrderPullResp{}, nil
}
