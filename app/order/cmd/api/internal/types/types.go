// Code generated by goctl. DO NOT EDIT.
package types

type CreateHomestayOrderReq struct {
	HomestayId    int64  `json:"homestayId"`
	IsFood        bool   `json:"isFood"`
	LiveStartTime int64  `json:"liveStartTime"`
	LiveEndTime   int64  `json:"liveEndTime"`
	LivePeopleNum int64  `json:"livePeopleNum"`
	Remark        string `json:"remark"`
}

type CreateHomestayOrderResp struct {
	OrderSn string `json:"orderSn"`
}

type UserHomestayOrderListView struct {
	Sn              string  `json:"sn"`              //单号
	Title           string  `json:"title"`           //标题
	SubTitle        string  `json:"subTitle"`        //副标题
	HomestayId      int64   `json:"homestayId"`      //民宿id
	Cover           string  `json:"cover"`           //封面
	OrderTotalPrice float64 `json:"orderTotalPrice"` //订单总价格
	CreateTime      int64   `json:"createTime"`      //下单时间
	TradeState      int64   `json:"tradeState"`      //-1: 已取消 0:待支付 1:未使用 2:已使用  3:已退款 4:已过期
	LiveStartDate   int64   `json:"liveStartDate"`   //入驻开始日期
	LiveEndDate     int64   `json:"liveEndDate"`     //入驻结束日期
	TradeCode       string  `json:"tradeCode"`       //交易码
}

type UserHomestayOrderListReq struct {
	LastId     int64 `json:"lastId"`
	PageSize   int64 `json:"pageSize"`
	TradeState int64 `json:"tradeState"` //-99:全部 0:代支付订单 1:有效订单
}

type UserHomestayOrderListResp struct {
	List []UserHomestayOrderListView `json:"list"`
}

type UserHomestayOrderDetailReq struct {
	Sn string `json:"sn"`
}

type UserHomestayOrderDetailResp struct {
	Sn                  string  `json:"sn"`                  //单号
	UserId              int64   `json:"userId"`              //用户id
	HomestayId          int64   `json:"homestayId"`          //民宿id
	Title               string  `json:"title"`               //标题
	SubTitle            string  `json:"subTitle"`            //副标题
	Cover               string  `json:"cover"`               //封面
	Info                string  `json:"info"`                //介绍
	FoodInfo            string  `json:"foodInfo"`            //餐食标准
	FoodPrice           float64 `json:"foodPrice"`           //餐食价格(分)
	HomestayPrice       float64 `json:"homestayPrice"`       //民宿价格(分)
	MarketHomestayPrice float64 `json:"marketHomestayPrice"` //民宿市场价格(分)
	HomestayBusinessId  float64 `json:"homestayBusinessId"`  //店铺id
	HomestayUserId      float64 `json:"homestayUserId"`      //店铺房东id
	OrderTotalPrice     float64 `json:"orderTotalPrice"`     //订单总价格
	CreateTime          int64   `json:"createTime"`          //下单时间
	TradeState          int64   `json:"tradeState"`          //-1: 已取消 0:待支付 1:未使用 2:已使用  3:已退款 4:已过期
	LiveStartDate       int64   `json:"liveStartDate"`       //入驻开始日期
	LiveEndDate         int64   `json:"liveEndDate"`         //入驻结束日期
	TradeCode           string  `json:"tradeCode"`           //交易码
	FoodTotalPrice      float64 `json:"foodTotalPrice"`      //餐食总价格(分)
	HomestayTotalPrice  float64 `json:"homestayTotalPrice"`  //民宿总价格(分)
	Remark              string  `json:"remark"`              //备注
	LivePeopleNum       int64   `json:"livePeopleNum"`       //实际入住人数
	NeedFood            int64   `json:"needFood"`            //0:不需要餐食 1:需要参数
	PayTime             int64   `json:"payTime"`             //支付时间
	PayType             string  `json:"payType"`             //支付类型
}
