package shop

import (
	"context"
	"errors"
	"fmt"
	"looklook/common/ctxdata"
	"looklook/common/tool"

	"looklook/app/shop/cmd/api/internal/svc"
	"looklook/app/shop/cmd/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GenPddPromotionUrlLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGenPddPromotionUrlLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GenPddPromotionUrlLogic {
	return &GenPddPromotionUrlLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GenPddPromotionUrlLogic) GenPddPromotionUrl(req *types.GenPddPromotionUrlReq) (resp string, err error) {
	// todo: add your logic here and delete this line

	if req.GoodsSign == "" {
		return "", errors.New("商品标识不能为空")
	}

	userId := ctxdata.GetUidFromCtx(l.ctx)

	// 1、根据goodsSign 以及pdd接口获取商品信息
	params := map[string]string{
		"type":              "pdd.ddk.goods.promotion.url.generate",
		"client_secret":     tool.ClientSecret,
		"goods_sign_list":   fmt.Sprintf("[\"%s\"]", req.GoodsSign),
		"p_id":              "42316735_301157319",
		"custom_parameters": fmt.Sprintf("{\"uid\":%d}", userId),
	}

	body, err := tool.SendRequestToDDJB(params, "POST")
	if err != nil {
		return "", err
	}

	return string(body), nil
}
