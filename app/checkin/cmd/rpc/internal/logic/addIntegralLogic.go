package logic

import (
	"context"

	"looklook/app/checkin/cmd/rpc/internal/svc"
	"looklook/app/checkin/cmd/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddIntegralLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewAddIntegralLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddIntegralLogic {
	return &AddIntegralLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// -----------------------integralRecord-----------------------
func (l *AddIntegralLogic) AddIntegral(in *pb.AddIntegralReq) (*pb.AddIntegralResp, error) {
	// todo: add your logic here and delete this line

	return &pb.AddIntegralResp{}, nil
}
