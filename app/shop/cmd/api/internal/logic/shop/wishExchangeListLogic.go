package shop

import (
	"context"
	"encoding/json"
	"looklook/app/shop/cmd/rpc/shop"
	"looklook/common/ctxdata"

	"looklook/app/shop/cmd/api/internal/svc"
	"looklook/app/shop/cmd/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type WishExchangeListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewWishExchangeListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WishExchangeListLogic {
	return &WishExchangeListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WishExchangeListLogic) WishExchangeList(req *types.WishExchangeListReq) (resp string, err error) {
	// todo: add your logic here and delete this line
	// 1、uid获取
	uid := ctxdata.GetUidFromCtx(l.ctx)
	// 2、调用rpc接口获取订单列表
	wishList, err := l.svcCtx.ShopRpc.GetWishExchangeList(l.ctx, &shop.GoodsWishListReq{
		UserId:   uid,
		Page:     1,
		PageSize: 100,
	})
	if err != nil {
		return "", err
	}

	// 3、返回订单列表
	res, _ := json.Marshal(wishList.List)
	return string(res), nil
}
