version: '3'

######## app下api+rpc ,  Before starting this project, start the environment that the project depends on docker-compose-env.yml #######

services:

  #前端网关nginx-gateay (只代理looklook，admin-api不在这里做代理)
  # Front-end gateway nginx-gateway (Only agent looklook，admin-api Do not be an agent here)
  nginx-gateway:
    image: nginx:1.21.5
    container_name: nginx-gateway
    restart: always
    privileged: true
    environment:
      - TZ=Asia/Shanghai
    ports:
      - 8888:8081
    volumes:
      - ./deploy/nginx/conf.d:/etc/nginx/conf.d
      - ./data/nginx/log:/var/log/nginx
    networks:
      - looklook_net
    depends_on:
      - looklook

  #前端api + 业务rpc - Front-end API + business RPC
  looklook:
    # docker-hub : https://hub.docker.com/r/lyumikael/gomodd
    # dockerfile: https://github.com/Mikaelemmmm/gomodd , If you are macOs m1\m2 use dockerfile yourself to build the image
    image: lyumika<PERSON>/gomodd:v1.20.3
    container_name: looklook
    environment:
      # 时区上海 - Timezone Shanghai
      TZ: Asia/Shanghai
      GOPROXY: https://goproxy.cn,direct
    working_dir: /go/looklook
    volumes:
      - .:/go/looklook
    privileged: true
    restart: always
    networks:
      - looklook_net
    ports:
      - 2006:2006
      - 2004:2004
      - 2005:2005
      - 2009:2009

networks:
  looklook_net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

