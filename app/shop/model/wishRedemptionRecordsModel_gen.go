// Code generated by goctl. DO NOT EDIT.

package model

import (
	"context"
	"database/sql"
	"fmt"
	"looklook/deploy/script/mysql/genModel"
	"strings"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
	"looklook/common/globalkey"
)

var (
	wishRedemptionRecordsFieldNames          = builder.RawFieldNames(&WishRedemptionRecords{})
	wishRedemptionRecordsRows                = strings.Join(wishRedemptionRecordsFieldNames, ",")
	wishRedemptionRecordsRowsExpectAutoSet   = strings.Join(stringx.Remove(wishRedemptionRecordsFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), ",")
	wishRedemptionRecordsRowsWithPlaceHolder = strings.Join(stringx.Remove(wishRedemptionRecordsFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), "=?,") + "=?"

	cacheShopWishRedemptionRecordsIdPrefix = "cache:shop:wishRedemptionRecords:id:"
)

type (
	wishRedemptionRecordsModel interface {
		Insert(ctx context.Context, data *WishRedemptionRecords) (sql.Result, error)
		TransInsert(ctx context.Context, session sqlx.Session, data *WishRedemptionRecords) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*WishRedemptionRecords, error)
		Update(ctx context.Context, data *WishRedemptionRecords) error
		List(ctx context.Context, page, limit int64) ([]*WishRedemptionRecords, error)
		TransUpdate(ctx context.Context, session sqlx.Session, data *WishRedemptionRecords) error
		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		SelectBuilder() squirrel.SelectBuilder
		FindSum(ctx context.Context, sumBuilder squirrel.SelectBuilder, field string) (float64, error)
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder, field string) (int64, error)
		FindAll(ctx context.Context, rowBuilder squirrel.SelectBuilder, orderBy string) ([]*WishRedemptionRecords, error)
		FindPageListByPage(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*WishRedemptionRecords, error)
		FindPageListByPageWithTotal(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*WishRedemptionRecords, int64, error)
		FindPageListByIdDESC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*WishRedemptionRecords, error)
		FindPageListByIdASC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*WishRedemptionRecords, error)
		Delete(ctx context.Context, id int64) error
	}

	defaultWishRedemptionRecordsModel struct {
		sqlc.CachedConn
		table string
	}

	WishRedemptionRecords struct {
		Id        int64          `db:"id"`
		UserId    sql.NullInt64  `db:"user_id"`
		GoodUrl   sql.NullString `db:"good_url"`
		Integral  sql.NullInt64  `db:"integral"`
		GoodsSign sql.NullString `db:"goods_sign"`
		Extra     sql.NullString `db:"extra"`
	}
)

func newWishRedemptionRecordsModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultWishRedemptionRecordsModel {
	return &defaultWishRedemptionRecordsModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`wish_redemption_records`",
	}
}

func (m *defaultWishRedemptionRecordsModel) Delete(ctx context.Context, id int64) error {
	shopWishRedemptionRecordsIdKey := fmt.Sprintf("%s%v", cacheShopWishRedemptionRecordsIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		return conn.ExecCtx(ctx, query, id)
	}, shopWishRedemptionRecordsIdKey)
	return err
}

func (m *defaultWishRedemptionRecordsModel) FindOne(ctx context.Context, id int64) (*WishRedemptionRecords, error) {
	shopWishRedemptionRecordsIdKey := fmt.Sprintf("%s%v", cacheShopWishRedemptionRecordsIdPrefix, id)
	var resp WishRedemptionRecords
	err := m.QueryRowCtx(ctx, &resp, shopWishRedemptionRecordsIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", wishRedemptionRecordsRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, genModel.ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultWishRedemptionRecordsModel) Insert(ctx context.Context, data *WishRedemptionRecords) (sql.Result, error) {
	shopWishRedemptionRecordsIdKey := fmt.Sprintf("%s%v", cacheShopWishRedemptionRecordsIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, wishRedemptionRecordsRowsExpectAutoSet)
		return conn.ExecCtx(ctx, query, data.UserId, data.GoodUrl, data.Integral, data.Extra)
	}, shopWishRedemptionRecordsIdKey)
	return ret, err
}

func (m *defaultWishRedemptionRecordsModel) TransInsert(ctx context.Context, session sqlx.Session, data *WishRedemptionRecords) (sql.Result, error) {
	shopWishRedemptionRecordsIdKey := fmt.Sprintf("%s%v", cacheShopWishRedemptionRecordsIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, wishRedemptionRecordsRowsExpectAutoSet)
		return session.ExecCtx(ctx, query, data.UserId, data.GoodUrl, data.Integral, data.Extra)
	}, shopWishRedemptionRecordsIdKey)
	return ret, err
}
func (m *defaultWishRedemptionRecordsModel) Update(ctx context.Context, data *WishRedemptionRecords) error {
	shopWishRedemptionRecordsIdKey := fmt.Sprintf("%s%v", cacheShopWishRedemptionRecordsIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, wishRedemptionRecordsRowsWithPlaceHolder)
		return conn.ExecCtx(ctx, query, data.UserId, data.GoodUrl, data.Integral, data.Extra, data.Id)
	}, shopWishRedemptionRecordsIdKey)
	return err
}

func (m *defaultWishRedemptionRecordsModel) TransUpdate(ctx context.Context, session sqlx.Session, data *WishRedemptionRecords) error {
	shopWishRedemptionRecordsIdKey := fmt.Sprintf("%s%v", cacheShopWishRedemptionRecordsIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, wishRedemptionRecordsRowsWithPlaceHolder)
		return session.ExecCtx(ctx, query, data.UserId, data.GoodUrl, data.Integral, data.Extra, data.Id)
	}, shopWishRedemptionRecordsIdKey)
	return err
}

func (m *defaultWishRedemptionRecordsModel) List(ctx context.Context, page, limit int64) ([]*WishRedemptionRecords, error) {
	query := fmt.Sprintf("select %s from %s limit ?,?", wishRedemptionRecordsRows, m.table)
	var resp []*WishRedemptionRecords
	//err := m.conn.QueryRowsCtx(ctx, &resp, query, (page-1)*limit, limit)
	err := m.QueryRowsNoCacheCtx(ctx, &resp, query, (page-1)*limit, limit)
	return resp, err
}

func (m *defaultWishRedemptionRecordsModel) Trans(ctx context.Context, fn func(ctx context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, func(ctx context.Context, session sqlx.Session) error {
		return fn(ctx, session)
	})
}

func (m *defaultWishRedemptionRecordsModel) FindSum(ctx context.Context, builder squirrel.SelectBuilder, field string) (float64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindSum Least One Field"), "FindSum Least One Field")
	}

	builder = builder.Columns("IFNULL(SUM(" + field + "),0)")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp float64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultWishRedemptionRecordsModel) FindCount(ctx context.Context, builder squirrel.SelectBuilder, field string) (int64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindCount Least One Field"), "FindCount Least One Field")
	}

	builder = builder.Columns("COUNT(" + field + ")")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultWishRedemptionRecordsModel) FindAll(ctx context.Context, builder squirrel.SelectBuilder, orderBy string) ([]*WishRedemptionRecords, error) {

	builder = builder.Columns(wishRedemptionRecordsRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*WishRedemptionRecords
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultWishRedemptionRecordsModel) FindPageListByPage(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*WishRedemptionRecords, error) {

	builder = builder.Columns(wishRedemptionRecordsRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*WishRedemptionRecords
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultWishRedemptionRecordsModel) FindPageListByPageWithTotal(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*WishRedemptionRecords, int64, error) {

	total, err := m.FindCount(ctx, builder, "id")
	if err != nil {
		return nil, 0, err
	}

	builder = builder.Columns(wishRedemptionRecordsRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, total, err
	}

	var resp []*WishRedemptionRecords
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, total, nil
	default:
		return nil, total, err
	}
}

func (m *defaultWishRedemptionRecordsModel) FindPageListByIdDESC(ctx context.Context, builder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*WishRedemptionRecords, error) {

	builder = builder.Columns(wishRedemptionRecordsRows)

	if preMinId > 0 {
		builder = builder.Where(" id < ? ", preMinId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id DESC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*WishRedemptionRecords
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultWishRedemptionRecordsModel) FindPageListByIdASC(ctx context.Context, builder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*WishRedemptionRecords, error) {

	builder = builder.Columns(wishRedemptionRecordsRows)

	if preMaxId > 0 {
		builder = builder.Where(" id > ? ", preMaxId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id ASC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*WishRedemptionRecords
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultWishRedemptionRecordsModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select().From(m.table)
}

func (m *defaultWishRedemptionRecordsModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheShopWishRedemptionRecordsIdPrefix, primary)
}

func (m *defaultWishRedemptionRecordsModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", wishRedemptionRecordsRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultWishRedemptionRecordsModel) tableName() string {
	return m.table
}
