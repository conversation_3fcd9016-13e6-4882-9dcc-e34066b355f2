### 三、鉴权服务

本项目地址 :  https://github.com/Mikaelemmmm/go-zero-looklook



#### 1、鉴权服务

![image-20220118120646779](./images/3/go-zero-jwt.jpg)





#### 1.1 go-zero jwt

上一节我们提到了，在desc/api文件中我们定义了go-zero自带的jwt中间件，生成代码后我们可以看到定义了jwt的api服务的路由，这里我们以usercenter服务举例，我们可以看下 go-zero-looklook/app/usercenter/cmd/api/internal/handler/routes.go 代码如下

```go
// Code generated by goctl. DO NOT EDIT.
package handler

	........

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/user/detail",
				Handler: user.DetailHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/user/wxMiniAuth",
				Handler: user.WxMiniAuthHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.JwtAuth.AccessSecret),
		rest.WithPrefix("/usercenter/v1"),
	)
}

```

我们可以看到在路由中给我们使用了rest.WithJwt , 就是针对一组路由使用了jwt鉴权，具体实现可以去看一下rest.WithJwt源码，go-zero jwt还实现了自动刷新token功能，这个留给大家自己去查看源码发现一下。





#### 2、安装goctl 与 protoc、protoc-gen-go

【注】这个跟鉴权没什么关系，只是后面写代码要用到，在这里最好给安装了

1、安装goctl

```shell
# for Go 1.15 and earlier
GO111MODULE=on go get -u github.com/zeromicro/go-zero/tools/goctl@latest

# for Go 1.16 and later
go install github.com/zeromicro/go-zero/tools/goctl@latest
```

验证是否安装成功

```shell
$ goctl --version
```

Goctl自定义模版template： 将项目目录下的data/goctl文件夹内容copy到家目录的.goctl中，goctl在生成代码时候会优先根据这个模版下内容生成

```shell
$ cp -r data/goctl ~/.goctl
```



2、安装protoc

链接：https://github.com/protocolbuffers/protobuf/releases

直接找到对应平台的protoc，我是mac intel芯片，所以直接找到 protoc-3.19.3-osx-x86_64.zip ，解压出来后进入该目录下的bin目录中，将protoc直接copy到你的gopath/bin目录下即可。

验证是否安装成功

```shell
$ protoc --version
```



3、安装protoc-gen-go

```shell
$ go install google.golang.org/protobuf/cmd/protoc-gen-go@latest 
```

查看$GOPATH/bin下是否有protoc-gen-go即可

【注】：如果后续在使用goctl生成代码时候，遇到以下问题

```shell
protoc  --proto_path=/Users/<USER>/Developer/goenv/go-zero-looklook/app/usercenter/cmd/rpc/pb usercenter.proto --go_out=plugins=grpc:/Users/<USER>/Developer/goenv/go-zero-looklook/app/usercenter/cmd/rpc --go_opt=Musercenter.proto=././pb
goctl: generation error: unsupported plugin protoc-gen-go which installed from the following source:
google.golang.org/protobuf/cmd/protoc-gen-go, 
github.com/protocolbuffers/protobuf-go/cmd/protoc-gen-go;

Please replace it by the following command, we recommend to use version before v1.3.5:
go get -u github.com/golang/protobuf/protoc-gen-go
goctl version: 1.3.0 darwin/amd64
```

直接执行 

```shell
$ go get -u github.com/golang/protobuf/protoc-gen-go
```



4、安装protoc-gen-go-grpc

```shell
$ go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
```





### 3、总结

总的来说，identity还算是比较简单的，整个流程如下：

用户发起请求资源 -> nginx网关->匹配到对应服务模块 -> auth模块->identity-api ->identity-rpc -> 用户请求的资源









