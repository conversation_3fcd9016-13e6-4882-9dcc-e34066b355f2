// Code generated by goctl. DO NOT EDIT!
// Source: order.proto

package server

import (
	"context"

	"looklook/app/order/cmd/rpc/internal/logic"
	"looklook/app/order/cmd/rpc/internal/svc"
	"looklook/app/order/cmd/rpc/pb"
)

type OrderServer struct {
	svcCtx *svc.ServiceContext
}

func NewOrderServer(svcCtx *svc.ServiceContext) *OrderServer {
	return &OrderServer{
		svcCtx: svcCtx,
	}
}

// 民宿下订单
func (s *OrderServer) CreateHomestayOrder(ctx context.Context, in *pb.CreateHomestayOrderReq) (*pb.CreateHomestayOrderResp, error) {
	l := logic.NewCreateHomestayOrderLogic(ctx, s.svcCtx)
	return l.CreateHomestayOrder(in)
}

// 民宿订单详情
func (s *OrderServer) HomestayOrderDetail(ctx context.Context, in *pb.HomestayOrderDetailReq) (*pb.HomestayOrderDetailResp, error) {
	l := logic.NewHomestayOrderDetailLogic(ctx, s.svcCtx)
	return l.HomestayOrderDetail(in)
}

// 更新民宿订单状态
func (s *OrderServer) UpdateHomestayOrderTradeState(ctx context.Context, in *pb.UpdateHomestayOrderTradeStateReq) (*pb.UpdateHomestayOrderTradeStateResp, error) {
	l := logic.NewUpdateHomestayOrderTradeStateLogic(ctx, s.svcCtx)
	return l.UpdateHomestayOrderTradeState(in)
}

// 用户民宿订单
func (s *OrderServer) UserHomestayOrderList(ctx context.Context, in *pb.UserHomestayOrderListReq) (*pb.UserHomestayOrderListResp, error) {
	l := logic.NewUserHomestayOrderListLogic(ctx, s.svcCtx)
	return l.UserHomestayOrderList(in)
}
