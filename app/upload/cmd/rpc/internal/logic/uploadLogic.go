package logic

import (
	"context"
	"encoding/base64"
	"fmt"
	"github.com/jinzhu/copier"
	"github.com/pkg/errors"
	"github.com/qiniu/go-sdk/v7/storage"
	"looklook/app/upload/cmd/rpc/internal/svc"
	"looklook/app/upload/cmd/rpc/pb"
	"looklook/app/upload/model"
	"looklook/common/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type UploadLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUploadLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UploadLogic {
	return &UploadLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *UploadLogic) Upload(in *pb.FileUploadReq) (*pb.FileUploadResp, error) {
	uploadFile := new(model.UploadFile)
	_ = copier.Copy(uploadFile, in)
	//bucket, err := l.svcCtx.OssClient.Bucket(l.svcCtx.Config.Oss.BucketName)
	//if err != nil {
	//	return nil, errors.Wrapf(xerr.NewErrCode(xerr.OSS_ERROR), "upload file with oss client err: %+v , err: %v", in, err)
	//}
	// 创建一个 io.Reader
	//reader := bytes.NewReader(in.FileData)
	fileName := in.FileName + in.Ext
	//err = bucket.PutObject(fileName, reader)
	//if err != nil {
	//	return nil, errors.Wrapf(xerr.NewErrCode(xerr.OSS_ERROR), "put object with oss client err: %+v , err: %v", in, err)
	//}

	// 上传文件

	dst := make([]byte, base64.StdEncoding.EncodedLen(len(in.FileData)))
	base64.StdEncoding.Encode(dst, in.GetFileData())

	err := l.svcCtx.Base64Uploader.Put(context.Background(), &storage.PutRet{}, l.svcCtx.UploadToken, fileName, dst, nil)
	if err != nil {
		fmt.Println("上传失败：", err)
		return nil, errors.Wrapf(xerr.NewErrCode(xerr.OSS_ERROR), "put object with oss client err: %+v , err: %v", in, err)
	}
	url := l.svcCtx.Config.Oss.FilePathPrefix + fileName
	uploadFile.Url = url
	_, err = l.svcCtx.FileUploadModel.Insert(l.ctx, uploadFile)
	if err != nil {
		return nil, errors.Wrapf(xerr.NewErrCode(xerr.OSS_ERROR), "save file info with err: %+v , err: %v", in, err)
	}
	return &pb.FileUploadResp{
		Url: url,
	}, nil
}
