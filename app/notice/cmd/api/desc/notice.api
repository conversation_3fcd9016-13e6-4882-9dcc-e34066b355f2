syntax = "v1"

info (
	title:   "消息通知服务"
	desc:    "消息通知服务"
	author:  "Max"
	email:   "<EMAIL>"
	version: "v1"
)

import (
	"event/event.api"
)

//============================> notice v1 <============================
//no need login
@server (
	prefix: notice/v1
	group:  event
)
service notice {
	@doc "验证小程序回调消息"
	@handler verifyEvent
	get /event (VerifyEventReq) returns (VerifyEventResp)

	@doc "接收小程序回调消息"
	@handler receiveEvent
	post /event (ReceiveEventReq) returns (ReceiveEventResp)
}

