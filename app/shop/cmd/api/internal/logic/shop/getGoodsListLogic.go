package shop

import (
	"context"
	"encoding/json"
	"looklook/app/shop/cmd/rpc/shop"

	"looklook/app/shop/cmd/api/internal/svc"
	"looklook/app/shop/cmd/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetGoodsListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetGoodsListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetGoodsListLogic {
	return &GetGoodsListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetGoodsListLogic) GetGoodsList(req *types.GoodsListReq) (resp string, err error) {
	if req.Size == 0 {
		req.Size = 10
	}
	//根据页码查询商品信息列表
	list, err := l.svcCtx.ShopRpc.GetGoodsList(l.ctx, &shop.GoodsListReq{
		Size:   req.Size,   //传入的Size
		LastId: req.LastId, //传入的LastId
	})
	if err != nil {
		return "", err
	}

	// 3、返回订单列表
	res, _ := json.Marshal(list.List)
	return string(res), nil
}
