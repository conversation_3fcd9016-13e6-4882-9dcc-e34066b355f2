# 抽奖系统后端服务架构说明

请阅读项目根目录下的 /doc/chinese/ 下的完整教程，从 01-开发环境搭建 开始阅读学习。

## 服务概览

本项目是一个基于微服务架构的抽奖系统后端，使用 Go-Zero 框架开发，包含多个独立的微服务，每个服务负责不同的业务功能。这不仅仅是一个简单的抽奖系统，而是一个融合了电商、旅游等多种业务的综合平台，构建了一个完整的用户生态系统。以下是对 `/app` 目录下各个服务的详细分析：

## 服务详情

### 1. 用户中心服务 (usercenter)

**功能描述**：负责用户账户管理、认证和个人信息处理的核心服务。

**主要功能**：
- 用户注册、登录和认证（包括微信小程序认证）
- 用户个人信息管理和更新
- 用户地址管理（添加、列表查询、地址识别转换）
- 用户联系方式管理
- 用户动态管理（创建、删除动态）
- 赞助商信息管理（添加、修改、删除、查询）
- 用户晒单管理

**API 路径前缀**：`/usercenter/v1`

### 2. 抽奖服务 (lottery)

**功能描述**：系统的核心服务，负责抽奖活动的全生命周期管理。

**主要功能**：
- 创建和更新抽奖活动
- 抽奖活动列表查询和详情查看
- 用户参与抽奖记录管理
- 抽奖结果查询和中奖名单管理
- 打卡任务记录创建和管理
- 抽奖机会类型管理

**API 路径前缀**：`/lottery/v1`

### 3. 订单服务 (order)

**功能描述**：处理系统中的订单相关业务逻辑。

**主要功能**：
- 创建民宿订单
- 用户订单列表查询
- 订单详情查看

**API 路径前缀**：`/order/v1`

### 4. 支付服务 (payment)

**功能描述**：处理支付相关的业务逻辑，与第三方支付平台对接。

**主要功能**：
- 微信支付接口对接
- 支付回调处理

**API 路径前缀**：`/payment/v1`

### 5. 商店服务 (shop)

**功能描述**：管理商品和商店相关的业务逻辑。

**主要功能**：
- 商品列表查询和详情查看
- 商品搜索
- 心愿单兑换功能
- 订单列表查询
- 拼多多授权和推广链接生成

**API 路径前缀**：`/shop/v1`

### 6. 签到服务 (checkin)

**功能描述**：管理用户签到和任务相关的业务逻辑。

**主要功能**：
- 用户签到功能
- 任务列表查询
- 奖励领取
- 订阅更新
- 积分查询

**API 路径前缀**：`/checkin/v1`

### 7. 评论服务 (comment)

**功能描述**：处理系统中的评论相关业务逻辑。

**主要功能**：
- 添加、删除、更新评论
- 评论点赞
- 评论列表查询和详情查看

**API 路径前缀**：`/comment/v1`

### 8. 消息队列服务 (mqueue)

**功能描述**：处理系统中的异步任务和定时任务。

**主要功能**：
- 结算记录处理
- 延迟关闭订单
- 支付成功通知用户
- 微信小程序订阅消息发送
- 抽奖开奖定时任务
- 心愿签到定时任务

### 9. 通知服务 (notice)

**功能描述**：处理系统中的通知和事件。

**主要功能**：
- 事件验证
- 事件接收和处理

**API 路径前缀**：`/notice/v1`

### 10. 上传服务 (upload)

**功能描述**：处理文件上传相关的业务逻辑。

**主要功能**：
- 文件上传

**API 路径前缀**：`/upload/v1`

### 11. 旅行服务 (travel)

**功能描述**：处理民宿和旅行相关的业务逻辑。

**主要功能**：
- 民宿列表查询和详情查看
- 商家列表查询
- 推荐列表查询
- 民宿商家信息管理
- 民宿评论管理

**API 路径前缀**：`/travel/v1`

### 12. 投票服务 (vote)

**功能描述**：处理系统中的投票相关业务逻辑。

**主要功能**：
- 创建和更新投票
- 启用投票
- 查看投票详情
- 创建投票记录
- 查看用户投票记录和投票详情

**API 路径前缀**：`/vote/v1`

## 系统架构

该项目采用微服务架构，每个服务都有独立的 API 和 RPC 接口，服务之间通过 RPC 进行通信。系统使用 JWT 进行身份验证，并实现了分布式事务、消息队列、延迟队列等高级功能。

## 技术栈

- 框架：Go-Zero
- 数据库：MySQL
- 缓存：Redis
- 消息队列：Asynq
- 认证：JWT
- 网关：Nginx

## 系统架构图

### 服务架构图

以下是系统的服务架构图，展示了各个微服务之间的关系：

```
+------------------+       +------------------+       +------------------+
|                  |       |                  |       |                  |
|  用户中心服务    |------>|    抽奖服务      |------>|   通知服务       |
|  (usercenter)    |       |   (lottery)      |       |   (notice)       |
|                  |       |                  |       |                  |
+------------------+       +------------------+       +------------------+
        |                          |                          ^
        |                          |                          |
        v                          v                          |
+------------------+       +------------------+       +------------------+
|                  |       |                  |       |                  |
|   评论服务       |<------|   消息队列服务   |------>|   上传服务       |
|   (comment)      |       |   (mqueue)       |       |   (upload)       |
|                  |       |                  |       |                  |
+------------------+       +------------------+       +------------------+
        |                          |                          ^
        |                          |                          |
        v                          v                          |
+------------------+       +------------------+       +------------------+
|                  |       |                  |       |                  |
|   投票服务       |       |    商店服务      |<------|   签到服务       |
|   (vote)         |       |    (shop)        |       |   (checkin)      |
|                  |       |                  |       |                  |
+------------------+       +------------------+       +------------------+
                                   |
                                   |
                                   v
+------------------+       +------------------+       +------------------+
|                  |       |                  |       |                  |
|   旅行服务       |<------|   订单服务       |------>|   支付服务       |
|   (travel)       |       |   (order)        |       |   (payment)      |
|                  |       |                  |       |                  |
+------------------+       +------------------+       +------------------+
```

### 业务流程图

以下是系统的业务流程图，展示了主要的业务流程和数据流向：

```
+-------------+     登录/注册     +-------------+
|             |  -------------->  |             |
|    用户     |                   | 用户中心服务 |
|             |  <--------------  |             |
+-------------+     用户信息      +-------------+
      |                                |
      |                                |
      v                                v
+-------------+     参与抽奖     +-------------+     创建/管理    +-------------+
|             |  -------------->  |             |  ------------>  |             |
|  抽奖活动   |                   |  抽奖服务   |                 |  赞助商管理  |
|             |  <--------------  |             |  <------------  |             |
+-------------+     抽奖结果     +-------------+     提供奖品    +-------------+
      |                                |
      |                                |
      v                                v
+-------------+     中奖通知     +-------------+     定时开奖    +-------------+
|             |  <--------------  |             |  <------------  |             |
|  通知中心   |                   |  消息队列   |                 |  定时任务   |
|             |  -------------->  |             |  ------------>  |             |
+-------------+     事件分发     +-------------+     任务执行    +-------------+
      |                                |
      |                                |
      v                                v
+-------------+     奖品兑换     +-------------+     生成订单    +-------------+
|             |  -------------->  |             |  ------------>  |             |
|   商店      |                   |  订单服务   |                 |  支付服务   |
|             |  <--------------  |             |  <------------  |             |
+-------------+     商品信息     +-------------+     支付结果    +-------------+
      |                                |
      |                                |
      v                                v
+-------------+     用户评论     +-------------+     用户投票    +-------------+
|             |  -------------->  |             |  ------------>  |             |
|  评论服务   |                   |  社交互动   |                 |  投票服务   |
|             |  <--------------  |             |  <------------  |             |
+-------------+     评论列表     +-------------+     投票结果    +-------------+
      |                                |
      |                                |
      v                                v
+-------------+     签到积分     +-------------+     文件上传    +-------------+
|             |  -------------->  |             |  ------------>  |             |
|  签到服务   |                   |  积分系统   |                 |  上传服务   |
|             |  <--------------  |             |  <------------  |             |
+-------------+     积分奖励     +-------------+     文件URL     +-------------+
```

## 服务之间的关联

各个服务之间并非独立存在，而是组成了多个完整的业务流程，形成一个有机的整体。以下是主要的服务关联流程：

### 1. 抽奖核心流程

- **用户中心(usercenter)** → **抽奖服务(lottery)** → **通知服务(notice)**
- 用户登录后参与抽奖活动，中奖后通过通知服务接收消息
- 消息队列服务(mqueue)负责定时开奖和发送通知

### 2. 奖品兑换流程

- **抽奖服务(lottery)** → **商店服务(shop)** → **订单服务(order)** → **支付服务(payment)**
- 用户中奖后可以在商店中兑换实物奖品，生成订单并可能需要支付额外费用
- 心愿单功能允许用户使用积分或抽奖获得的虚拟货币兑换商品

### 3. 用户互动流程

- **用户中心(usercenter)** → **评论服务(comment)** → **投票服务(vote)**
- 用户可以对抽奖活动进行评论、点赞，参与投票活动
- 用户可以发布动态展示中奖成果，增强社交互动

### 4. 旅游奖品流程

- **抽奖服务(lottery)** → **旅行服务(travel)** → **订单服务(order)**
- 抽奖活动的奖品可能包括民宿住宿券、旅游体验等
- 用户中奖后可以通过旅行服务查看和使用这些奖品

### 5. 签到积分流程

- **签到服务(checkin)** → **商店服务(shop)**
- 用户通过每日签到获取积分
- 积分可以在商店中兑换商品或参与抽奖

## 整体业务模式

综合分析，这个系统是一个以抽奖为核心，但融合了电商、旅游等多种业务的综合平台：

### 1. 抽奖引流模式

- 通过有吸引力的抽奖活动吸引用户
- 用户参与抽奖后，可能被引导到商店或旅游服务中消费
- 商家通过赞助抽奖活动获得流量曝光

### 2. 积分生态系统

- 用户通过签到、参与活动获得积分
- 积分可以在商店中兑换商品或参与更高级别的抽奖
- 心愿单功能增强用户留存和转化

### 3. 商家合作模式

- 商家可以作为赞助商提供奖品
- 平台通过抽奖活动为商家引流，形成双赢
- 拼多多推广功能实现电商平台合作变现

### 4. 社交互动功能

- 用户可以发布动态、评论、投票
- 中奖用户可以发布晒单，展示奖品
- 增强用户粘性和平台活跃度

### 5. 消息队列与异步任务

- 通过消息队列服务实现服务间的解耦
- 处理定时开奖、延迟关闭订单等关键业务
- 确保系统在高并发情况下的稳定性

这种设计使得平台不仅仅是一个单一功能的抽奖系统，而是一个完整的用户生态，能够实现用户获取、活跃、转化和变现的全流程。各个看似不相关的服务实际上是围绕这一商业模式紧密协作的。

## 数据库实体关系图

以下是系统的数据库实体关系图，展示了各个实体之间的关系：

```
+---------------+       +---------------+       +---------------+
|     用户      |------>|    抽奖活动   |------>|     奖品      |
|    (user)     |       |   (lottery)   |       |    (prize)    |
+---------------+       +---------------+       +---------------+
      |                        |                      |
      |                        |                      |
      v                        v                      v
+---------------+       +---------------+       +---------------+
|   用户地址    |       | 抽奖参与记录  |       |   奖品领取    |
| (user_address) |       | (lottery_    |       | (prize_claim) |
|               |       | participation)|       |               |
+---------------+       +---------------+       +---------------+
      |                        |                      |
      |                        |                      |
      v                        v                      v
+---------------+       +---------------+       +---------------+
|  用户联系方式  |       |    签到记录   |       |    订单      |
| (user_contact) |       | (checkin_    |       |   (order)    |
|               |       |  record)      |       |               |
+---------------+       +---------------+       +---------------+
      |                        |                      |
      |                        |                      |
      v                        v                      v
+---------------+       +---------------+       +---------------+
|  用户动态     |       |    积分系统   |       |    支付      |
| (user_dynamic) |       |  (integral)   |       |  (payment)   |
+---------------+       +---------------+       +---------------+
```

### 主要实体说明

#### 用户相关实体
1. **用户(user)**
   - 存储用户基本信息，如手机号、密码、昵称、头像等
   - 包含用户统计数据：奖品总数、粉丝数、抽奖记录等

2. **用户地址(user_address)**
   - 存储用户的收货地址信息
   - 用于奖品寄送和商品订单配送

3. **用户联系方式(user_contact)**
   - 存储用户的联系方式信息
   - 用于奖品领取和系统通知

4. **用户动态(user_dynamic)**
   - 存储用户发布的动态内容
   - 包括晒单、中奖分享等社交内容

#### 抽奖相关实体
1. **抽奖活动(lottery)**
   - 存储抽奖活动的基本信息
   - 包括活动名称、介绍、开奖时间、开奖方式等

2. **奖品(prize)**
   - 存储抽奖活动的奖品信息
   - 包括奖品名称、等级、数量、发放方式等

3. **抽奖参与记录(lottery_participation)**
   - 记录用户参与抽奖的情况
   - 包括是否中奖、中奖奖品等信息

#### 商店相关实体
1. **商品(goods)**
   - 存储商店中的商品信息
   - 包括商品描述、价格、所需积分等

2. **订单(order)**
   - 存储用户的订单信息
   - 包括订单状态、商品信息、价格等

#### 签到与积分相关实体
1. **签到记录(checkin_record)**
   - 记录用户的签到情况
   - 包括连续签到天数、签到状态等

2. **积分(integral)**
   - 存储用户的积分信息
   - 用于商品兑换和抽奖参与

3. **任务(tasks)**
   - 存储系统中的任务信息
   - 包括任务类型、内容、奖励积分等

### 业务流程中的实体关系

1. **抽奖业务流程**
   - 用户(user) → 发起抽奖活动(lottery) → 设置奖品(prize) → 其他用户参与(lottery_participation) → 系统开奖 → 中奖用户领取奖品

2. **签到积分业务流程**
   - 用户(user) → 每日签到(checkin_record) → 获取积分(integral) → 使用积分参与抽奖或兑换商品

3. **商品兑换业务流程**
   - 用户(user) → 浏览商品(goods) → 使用积分或中奖兑换 → 生成订单(order) → 支付(payment) → 填写收货地址(user_address)

## 部署环境

详细的部署环境搭建和服务发布到 K8s 的教程，请参考 `/doc/chinese/` 目录下的相关文档。