// Code generated by goctl. DO NOT EDIT.

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	prizeFieldNames          = builder.RawFieldNames(&Prize{})
	prizeRows                = strings.Join(prizeFieldNames, ",")
	prizeRowsExpectAutoSet   = strings.Join(stringx.Remove(prizeFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), ",")
	prizeRowsWithPlaceHolder = strings.Join(stringx.Remove(prizeFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), "=?,") + "=?"

	cacheLotteryPrizeIdPrefix = "cache:lottery:prize:id:"
)

type (
	prizeModel interface {
		Insert(ctx context.Context, data *Prize) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Prize, error)
		Update(ctx context.Context, data *Prize) error
		Delete(ctx context.Context, id int64) error
	}

	defaultPrizeModel struct {
		sqlc.CachedConn
		table string
	}

	Prize struct {
		Id         int64     `db:"id"`
		LotteryId  int64     `db:"lottery_id"` // 抽奖ID
		Type       int64     `db:"type"`       // 奖品类型：1奖品 2优惠券 3兑换码 4商城 5微信红包封面 6红包
		Name       string    `db:"name"`       // 奖品名称
		Level      int64     `db:"level"`      // 几等奖 默认1
		Thumb      string    `db:"thumb"`      // 奖品图
		Count      int64     `db:"count"`      // 奖品份数
		GrantType  int64     `db:"grant_type"` // 奖品发放方式：1快递邮寄 2让中奖者联系我 3中奖者填写信息 4跳转到其他小程序
		CreateTime time.Time `db:"create_time"`
		UpdateTime time.Time `db:"update_time"`
	}
)

func newPrizeModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultPrizeModel {
	return &defaultPrizeModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`prize`",
	}
}

func (m *defaultPrizeModel) Delete(ctx context.Context, id int64) error {
	lotteryPrizeIdKey := fmt.Sprintf("%s%v", cacheLotteryPrizeIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		return conn.ExecCtx(ctx, query, id)
	}, lotteryPrizeIdKey)
	return err
}

func (m *defaultPrizeModel) FindOne(ctx context.Context, id int64) (*Prize, error) {
	lotteryPrizeIdKey := fmt.Sprintf("%s%v", cacheLotteryPrizeIdPrefix, id)
	var resp Prize
	err := m.QueryRowCtx(ctx, &resp, lotteryPrizeIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", prizeRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPrizeModel) Insert(ctx context.Context, data *Prize) (sql.Result, error) {
	lotteryPrizeIdKey := fmt.Sprintf("%s%v", cacheLotteryPrizeIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?)", m.table, prizeRowsExpectAutoSet)
		return conn.ExecCtx(ctx, query, data.LotteryId, data.Type, data.Name, data.Level, data.Thumb, data.Count, data.GrantType)
	}, lotteryPrizeIdKey)
	return ret, err
}

func (m *defaultPrizeModel) Update(ctx context.Context, data *Prize) error {
	lotteryPrizeIdKey := fmt.Sprintf("%s%v", cacheLotteryPrizeIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, prizeRowsWithPlaceHolder)
		return conn.ExecCtx(ctx, query, data.LotteryId, data.Type, data.Name, data.Level, data.Thumb, data.Count, data.GrantType, data.Id)
	}, lotteryPrizeIdKey)
	return err
}

func (m *defaultPrizeModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheLotteryPrizeIdPrefix, primary)
}

func (m *defaultPrizeModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", prizeRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultPrizeModel) tableName() string {
	return m.table
}
