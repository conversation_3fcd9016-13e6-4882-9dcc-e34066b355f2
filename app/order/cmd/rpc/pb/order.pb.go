// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.1
// source: order.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HomestayOrder struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                  int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Sn                  string `protobuf:"bytes,2,opt,name=sn,proto3" json:"sn"`
	UserId              int64  `protobuf:"varint,3,opt,name=userId,proto3" json:"userId"`
	HomestayId          int64  `protobuf:"varint,4,opt,name=homestayId,proto3" json:"homestayId"`
	Title               string `protobuf:"bytes,5,opt,name=title,proto3" json:"title"`
	SubTitle            string `protobuf:"bytes,6,opt,name=subTitle,proto3" json:"subTitle"`
	Cover               string `protobuf:"bytes,7,opt,name=cover,proto3" json:"cover"`
	Info                string `protobuf:"bytes,8,opt,name=info,proto3" json:"info"`
	PeopleNum           int64  `protobuf:"varint,9,opt,name=peopleNum,proto3" json:"peopleNum"`
	RowType             int64  `protobuf:"varint,10,opt,name=rowType,proto3" json:"rowType"`
	FoodInfo            string `protobuf:"bytes,11,opt,name=foodInfo,proto3" json:"foodInfo"`
	FoodPrice           int64  `protobuf:"varint,12,opt,name=foodPrice,proto3" json:"foodPrice"`
	HomestayPrice       int64  `protobuf:"varint,13,opt,name=homestayPrice,proto3" json:"homestayPrice"`
	MarketHomestayPrice int64  `protobuf:"varint,14,opt,name=marketHomestayPrice,proto3" json:"marketHomestayPrice"`
	HomestayBusinessId  int64  `protobuf:"varint,15,opt,name=homestayBusinessId,proto3" json:"homestayBusinessId"`
	HomestayUserId      int64  `protobuf:"varint,16,opt,name=homestayUserId,proto3" json:"homestayUserId"`
	LiveStartDate       int64  `protobuf:"varint,17,opt,name=liveStartDate,proto3" json:"liveStartDate"`
	LiveEndDate         int64  `protobuf:"varint,18,opt,name=liveEndDate,proto3" json:"liveEndDate"`
	LivePeopleNum       int64  `protobuf:"varint,19,opt,name=livePeopleNum,proto3" json:"livePeopleNum"`
	TradeState          int64  `protobuf:"varint,20,opt,name=tradeState,proto3" json:"tradeState"`
	TradeCode           string `protobuf:"bytes,21,opt,name=tradeCode,proto3" json:"tradeCode"`
	Remark              string `protobuf:"bytes,22,opt,name=remark,proto3" json:"remark"`
	OrderTotalPrice     int64  `protobuf:"varint,23,opt,name=orderTotalPrice,proto3" json:"orderTotalPrice"`
	FoodTotalPrice      int64  `protobuf:"varint,24,opt,name=foodTotalPrice,proto3" json:"foodTotalPrice"`
	HomestayTotalPrice  int64  `protobuf:"varint,25,opt,name=homestayTotalPrice,proto3" json:"homestayTotalPrice"`
	CreateTime          int64  `protobuf:"varint,26,opt,name=createTime,proto3" json:"createTime"`
}

func (x *HomestayOrder) Reset() {
	*x = HomestayOrder{}
	if protoimpl.UnsafeEnabled {
		mi := &file_order_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HomestayOrder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HomestayOrder) ProtoMessage() {}

func (x *HomestayOrder) ProtoReflect() protoreflect.Message {
	mi := &file_order_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HomestayOrder.ProtoReflect.Descriptor instead.
func (*HomestayOrder) Descriptor() ([]byte, []int) {
	return file_order_proto_rawDescGZIP(), []int{0}
}

func (x *HomestayOrder) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *HomestayOrder) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *HomestayOrder) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *HomestayOrder) GetHomestayId() int64 {
	if x != nil {
		return x.HomestayId
	}
	return 0
}

func (x *HomestayOrder) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *HomestayOrder) GetSubTitle() string {
	if x != nil {
		return x.SubTitle
	}
	return ""
}

func (x *HomestayOrder) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *HomestayOrder) GetInfo() string {
	if x != nil {
		return x.Info
	}
	return ""
}

func (x *HomestayOrder) GetPeopleNum() int64 {
	if x != nil {
		return x.PeopleNum
	}
	return 0
}

func (x *HomestayOrder) GetRowType() int64 {
	if x != nil {
		return x.RowType
	}
	return 0
}

func (x *HomestayOrder) GetFoodInfo() string {
	if x != nil {
		return x.FoodInfo
	}
	return ""
}

func (x *HomestayOrder) GetFoodPrice() int64 {
	if x != nil {
		return x.FoodPrice
	}
	return 0
}

func (x *HomestayOrder) GetHomestayPrice() int64 {
	if x != nil {
		return x.HomestayPrice
	}
	return 0
}

func (x *HomestayOrder) GetMarketHomestayPrice() int64 {
	if x != nil {
		return x.MarketHomestayPrice
	}
	return 0
}

func (x *HomestayOrder) GetHomestayBusinessId() int64 {
	if x != nil {
		return x.HomestayBusinessId
	}
	return 0
}

func (x *HomestayOrder) GetHomestayUserId() int64 {
	if x != nil {
		return x.HomestayUserId
	}
	return 0
}

func (x *HomestayOrder) GetLiveStartDate() int64 {
	if x != nil {
		return x.LiveStartDate
	}
	return 0
}

func (x *HomestayOrder) GetLiveEndDate() int64 {
	if x != nil {
		return x.LiveEndDate
	}
	return 0
}

func (x *HomestayOrder) GetLivePeopleNum() int64 {
	if x != nil {
		return x.LivePeopleNum
	}
	return 0
}

func (x *HomestayOrder) GetTradeState() int64 {
	if x != nil {
		return x.TradeState
	}
	return 0
}

func (x *HomestayOrder) GetTradeCode() string {
	if x != nil {
		return x.TradeCode
	}
	return ""
}

func (x *HomestayOrder) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *HomestayOrder) GetOrderTotalPrice() int64 {
	if x != nil {
		return x.OrderTotalPrice
	}
	return 0
}

func (x *HomestayOrder) GetFoodTotalPrice() int64 {
	if x != nil {
		return x.FoodTotalPrice
	}
	return 0
}

func (x *HomestayOrder) GetHomestayTotalPrice() int64 {
	if x != nil {
		return x.HomestayTotalPrice
	}
	return 0
}

func (x *HomestayOrder) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

type CreateHomestayOrderReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HomestayId    int64  `protobuf:"varint,1,opt,name=homestayId,proto3" json:"homestayId"`
	IsFood        bool   `protobuf:"varint,2,opt,name=isFood,proto3" json:"isFood"`
	LiveStartTime int64  `protobuf:"varint,3,opt,name=liveStartTime,proto3" json:"liveStartTime"`
	LiveEndTime   int64  `protobuf:"varint,4,opt,name=liveEndTime,proto3" json:"liveEndTime"`
	UserId        int64  `protobuf:"varint,5,opt,name=userId,proto3" json:"userId"`
	LivePeopleNum int64  `protobuf:"varint,6,opt,name=livePeopleNum,proto3" json:"livePeopleNum"`
	Remark        string `protobuf:"bytes,7,opt,name=remark,proto3" json:"remark"`
}

func (x *CreateHomestayOrderReq) Reset() {
	*x = CreateHomestayOrderReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_order_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateHomestayOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateHomestayOrderReq) ProtoMessage() {}

func (x *CreateHomestayOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_order_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateHomestayOrderReq.ProtoReflect.Descriptor instead.
func (*CreateHomestayOrderReq) Descriptor() ([]byte, []int) {
	return file_order_proto_rawDescGZIP(), []int{1}
}

func (x *CreateHomestayOrderReq) GetHomestayId() int64 {
	if x != nil {
		return x.HomestayId
	}
	return 0
}

func (x *CreateHomestayOrderReq) GetIsFood() bool {
	if x != nil {
		return x.IsFood
	}
	return false
}

func (x *CreateHomestayOrderReq) GetLiveStartTime() int64 {
	if x != nil {
		return x.LiveStartTime
	}
	return 0
}

func (x *CreateHomestayOrderReq) GetLiveEndTime() int64 {
	if x != nil {
		return x.LiveEndTime
	}
	return 0
}

func (x *CreateHomestayOrderReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *CreateHomestayOrderReq) GetLivePeopleNum() int64 {
	if x != nil {
		return x.LivePeopleNum
	}
	return 0
}

func (x *CreateHomestayOrderReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

type CreateHomestayOrderResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sn string `protobuf:"bytes,1,opt,name=sn,proto3" json:"sn"`
}

func (x *CreateHomestayOrderResp) Reset() {
	*x = CreateHomestayOrderResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_order_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateHomestayOrderResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateHomestayOrderResp) ProtoMessage() {}

func (x *CreateHomestayOrderResp) ProtoReflect() protoreflect.Message {
	mi := &file_order_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateHomestayOrderResp.ProtoReflect.Descriptor instead.
func (*CreateHomestayOrderResp) Descriptor() ([]byte, []int) {
	return file_order_proto_rawDescGZIP(), []int{2}
}

func (x *CreateHomestayOrderResp) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

type HomestayOrderDetailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sn string `protobuf:"bytes,1,opt,name=sn,proto3" json:"sn"`
}

func (x *HomestayOrderDetailReq) Reset() {
	*x = HomestayOrderDetailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_order_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HomestayOrderDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HomestayOrderDetailReq) ProtoMessage() {}

func (x *HomestayOrderDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_order_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HomestayOrderDetailReq.ProtoReflect.Descriptor instead.
func (*HomestayOrderDetailReq) Descriptor() ([]byte, []int) {
	return file_order_proto_rawDescGZIP(), []int{3}
}

func (x *HomestayOrderDetailReq) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

type HomestayOrderDetailResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HomestayOrder *HomestayOrder `protobuf:"bytes,1,opt,name=homestayOrder,proto3" json:"homestayOrder"`
}

func (x *HomestayOrderDetailResp) Reset() {
	*x = HomestayOrderDetailResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_order_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HomestayOrderDetailResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HomestayOrderDetailResp) ProtoMessage() {}

func (x *HomestayOrderDetailResp) ProtoReflect() protoreflect.Message {
	mi := &file_order_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HomestayOrderDetailResp.ProtoReflect.Descriptor instead.
func (*HomestayOrderDetailResp) Descriptor() ([]byte, []int) {
	return file_order_proto_rawDescGZIP(), []int{4}
}

func (x *HomestayOrderDetailResp) GetHomestayOrder() *HomestayOrder {
	if x != nil {
		return x.HomestayOrder
	}
	return nil
}

type UpdateHomestayOrderTradeStateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sn         string `protobuf:"bytes,1,opt,name=sn,proto3" json:"sn"`
	TradeState int64  `protobuf:"varint,2,opt,name=tradeState,proto3" json:"tradeState"` //-1: 已取消 0:待支付 1:未使用 2:已使用  3:已过关闭
}

func (x *UpdateHomestayOrderTradeStateReq) Reset() {
	*x = UpdateHomestayOrderTradeStateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_order_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateHomestayOrderTradeStateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateHomestayOrderTradeStateReq) ProtoMessage() {}

func (x *UpdateHomestayOrderTradeStateReq) ProtoReflect() protoreflect.Message {
	mi := &file_order_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateHomestayOrderTradeStateReq.ProtoReflect.Descriptor instead.
func (*UpdateHomestayOrderTradeStateReq) Descriptor() ([]byte, []int) {
	return file_order_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateHomestayOrderTradeStateReq) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *UpdateHomestayOrderTradeStateReq) GetTradeState() int64 {
	if x != nil {
		return x.TradeState
	}
	return 0
}

type UpdateHomestayOrderTradeStateResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	UserId          int64  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId"`
	Sn              string `protobuf:"bytes,3,opt,name=sn,proto3" json:"sn"`
	TradeCode       string `protobuf:"bytes,4,opt,name=tradeCode,proto3" json:"tradeCode"`
	LiveStartDate   int64  `protobuf:"varint,5,opt,name=liveStartDate,proto3" json:"liveStartDate"`
	LiveEndDate     int64  `protobuf:"varint,6,opt,name=liveEndDate,proto3" json:"liveEndDate"`
	OrderTotalPrice int64  `protobuf:"varint,7,opt,name=orderTotalPrice,proto3" json:"orderTotalPrice"`
	Title           string `protobuf:"bytes,8,opt,name=title,proto3" json:"title"`
}

func (x *UpdateHomestayOrderTradeStateResp) Reset() {
	*x = UpdateHomestayOrderTradeStateResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_order_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateHomestayOrderTradeStateResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateHomestayOrderTradeStateResp) ProtoMessage() {}

func (x *UpdateHomestayOrderTradeStateResp) ProtoReflect() protoreflect.Message {
	mi := &file_order_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateHomestayOrderTradeStateResp.ProtoReflect.Descriptor instead.
func (*UpdateHomestayOrderTradeStateResp) Descriptor() ([]byte, []int) {
	return file_order_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateHomestayOrderTradeStateResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateHomestayOrderTradeStateResp) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UpdateHomestayOrderTradeStateResp) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *UpdateHomestayOrderTradeStateResp) GetTradeCode() string {
	if x != nil {
		return x.TradeCode
	}
	return ""
}

func (x *UpdateHomestayOrderTradeStateResp) GetLiveStartDate() int64 {
	if x != nil {
		return x.LiveStartDate
	}
	return 0
}

func (x *UpdateHomestayOrderTradeStateResp) GetLiveEndDate() int64 {
	if x != nil {
		return x.LiveEndDate
	}
	return 0
}

func (x *UpdateHomestayOrderTradeStateResp) GetOrderTotalPrice() int64 {
	if x != nil {
		return x.OrderTotalPrice
	}
	return 0
}

func (x *UpdateHomestayOrderTradeStateResp) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

type UserHomestayOrderListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LastId      int64 `protobuf:"varint,1,opt,name=lastId,proto3" json:"lastId"`
	PageSize    int64 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize"`
	UserId      int64 `protobuf:"varint,3,opt,name=userId,proto3" json:"userId"`
	TraderState int64 `protobuf:"varint,4,opt,name=traderState,proto3" json:"traderState"`
}

func (x *UserHomestayOrderListReq) Reset() {
	*x = UserHomestayOrderListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_order_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserHomestayOrderListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserHomestayOrderListReq) ProtoMessage() {}

func (x *UserHomestayOrderListReq) ProtoReflect() protoreflect.Message {
	mi := &file_order_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserHomestayOrderListReq.ProtoReflect.Descriptor instead.
func (*UserHomestayOrderListReq) Descriptor() ([]byte, []int) {
	return file_order_proto_rawDescGZIP(), []int{7}
}

func (x *UserHomestayOrderListReq) GetLastId() int64 {
	if x != nil {
		return x.LastId
	}
	return 0
}

func (x *UserHomestayOrderListReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *UserHomestayOrderListReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserHomestayOrderListReq) GetTraderState() int64 {
	if x != nil {
		return x.TraderState
	}
	return 0
}

type UserHomestayOrderListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*HomestayOrder `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
}

func (x *UserHomestayOrderListResp) Reset() {
	*x = UserHomestayOrderListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_order_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserHomestayOrderListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserHomestayOrderListResp) ProtoMessage() {}

func (x *UserHomestayOrderListResp) ProtoReflect() protoreflect.Message {
	mi := &file_order_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserHomestayOrderListResp.ProtoReflect.Descriptor instead.
func (*UserHomestayOrderListResp) Descriptor() ([]byte, []int) {
	return file_order_proto_rawDescGZIP(), []int{8}
}

func (x *UserHomestayOrderListResp) GetList() []*HomestayOrder {
	if x != nil {
		return x.List
	}
	return nil
}

var File_order_proto protoreflect.FileDescriptor

var file_order_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x02, 0x70,
	0x62, 0x22, 0xcb, 0x06, 0x0a, 0x0d, 0x48, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x79, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x73, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x68,
	0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x79, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x68, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x79, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x75, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x65, 0x6f, 0x70, 0x6c,
	0x65, 0x4e, 0x75, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x65, 0x6f, 0x70,
	0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x72, 0x6f, 0x77, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x66, 0x6f, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x66, 0x6f, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x66,
	0x6f, 0x6f, 0x64, 0x50, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x66, 0x6f, 0x6f, 0x64, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x68, 0x6f, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x79, 0x50, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0d, 0x68, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x79, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12,
	0x30, 0x0a, 0x13, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x48, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x79, 0x50, 0x72, 0x69, 0x63, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x6d, 0x61,
	0x72, 0x6b, 0x65, 0x74, 0x48, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x79, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x12, 0x2e, 0x0a, 0x12, 0x68, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x79, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x68,
	0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x79, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x12, 0x26, 0x0a, 0x0e, 0x68, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x79, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x68, 0x6f, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x6c, 0x69, 0x76,
	0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x6c, 0x69, 0x76, 0x65, 0x45, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x6c, 0x69, 0x76, 0x65, 0x45, 0x6e, 0x64, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x24, 0x0a, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x50, 0x65, 0x6f, 0x70, 0x6c, 0x65, 0x4e,
	0x75, 0x6d, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x50, 0x65,
	0x6f, 0x70, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x72, 0x61,
	0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x72, 0x61, 0x64, 0x65,
	0x43, 0x6f, 0x64, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x72, 0x61, 0x64,
	0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x28, 0x0a,
	0x0f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x6f, 0x74,
	0x61, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x66, 0x6f, 0x6f, 0x64, 0x54,
	0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0e, 0x66, 0x6f, 0x6f, 0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12,
	0x2e, 0x0a, 0x12, 0x68, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x79, 0x54, 0x6f, 0x74, 0x61, 0x6c,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x68, 0x6f, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x79, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x1a, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22,
	0xee, 0x01, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x48, 0x6f, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x1e, 0x0a, 0x0a, 0x68, 0x6f,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x68, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x79, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x73,
	0x46, 0x6f, 0x6f, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x46, 0x6f,
	0x6f, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x6c, 0x69, 0x76, 0x65,
	0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x6c,
	0x69, 0x76, 0x65, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x50, 0x65, 0x6f, 0x70, 0x6c, 0x65,
	0x4e, 0x75, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x50,
	0x65, 0x6f, 0x70, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b,
	0x22, 0x29, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x48, 0x6f, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x73,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x22, 0x28, 0x0a, 0x16, 0x48,
	0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x73, 0x6e, 0x22, 0x52, 0x0a, 0x17, 0x48, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x37, 0x0a, 0x0d, 0x68, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x62, 0x2e, 0x48, 0x6f, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x68, 0x6f, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x22, 0x52, 0x0a, 0x20, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x48, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a,
	0x02, 0x73, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x1e, 0x0a,
	0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x81, 0x02,
	0x0a, 0x21, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x48, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x79,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x73,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x73, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x74,
	0x72, 0x61, 0x64, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x74, 0x72, 0x61, 0x64, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x6c, 0x69, 0x76,
	0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x6c, 0x69, 0x76, 0x65, 0x45, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x6c, 0x69, 0x76, 0x65, 0x45, 0x6e, 0x64, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x28, 0x0a, 0x0f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x22, 0x88, 0x01, 0x0a, 0x18, 0x55, 0x73, 0x65, 0x72, 0x48, 0x6f, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x16,
	0x0a, 0x06, 0x6c, 0x61, 0x73, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x6c, 0x61, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x72,
	0x61, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0b, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0x42, 0x0a, 0x19,
	0x55, 0x73, 0x65, 0x72, 0x48, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x25, 0x0a, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x62, 0x2e, 0x48, 0x6f, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x32, 0xeb, 0x02, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x4e, 0x0a, 0x13, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x48, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x12, 0x1a, 0x2e, 0x70, 0x62, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x48, 0x6f, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e,
	0x70, 0x62, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x48, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4e, 0x0a, 0x13, 0x68, 0x6f,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0x1a, 0x2e, 0x70, 0x62, 0x2e, 0x48, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x79, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e,
	0x70, 0x62, 0x2e, 0x48, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x6c, 0x0a, 0x1d, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x48, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x54, 0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x24, 0x2e, 0x70, 0x62,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x48, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x79, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x54, 0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x25, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x48, 0x6f, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x72, 0x61, 0x64, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x54, 0x0a, 0x15, 0x75, 0x73, 0x65, 0x72,
	0x48, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x1c, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x48, 0x6f, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x1d, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x48, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x79, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x42, 0x06,
	0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_order_proto_rawDescOnce sync.Once
	file_order_proto_rawDescData = file_order_proto_rawDesc
)

func file_order_proto_rawDescGZIP() []byte {
	file_order_proto_rawDescOnce.Do(func() {
		file_order_proto_rawDescData = protoimpl.X.CompressGZIP(file_order_proto_rawDescData)
	})
	return file_order_proto_rawDescData
}

var file_order_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_order_proto_goTypes = []interface{}{
	(*HomestayOrder)(nil),                     // 0: pb.HomestayOrder
	(*CreateHomestayOrderReq)(nil),            // 1: pb.CreateHomestayOrderReq
	(*CreateHomestayOrderResp)(nil),           // 2: pb.CreateHomestayOrderResp
	(*HomestayOrderDetailReq)(nil),            // 3: pb.HomestayOrderDetailReq
	(*HomestayOrderDetailResp)(nil),           // 4: pb.HomestayOrderDetailResp
	(*UpdateHomestayOrderTradeStateReq)(nil),  // 5: pb.UpdateHomestayOrderTradeStateReq
	(*UpdateHomestayOrderTradeStateResp)(nil), // 6: pb.UpdateHomestayOrderTradeStateResp
	(*UserHomestayOrderListReq)(nil),          // 7: pb.UserHomestayOrderListReq
	(*UserHomestayOrderListResp)(nil),         // 8: pb.UserHomestayOrderListResp
}
var file_order_proto_depIdxs = []int32{
	0, // 0: pb.HomestayOrderDetailResp.homestayOrder:type_name -> pb.HomestayOrder
	0, // 1: pb.UserHomestayOrderListResp.list:type_name -> pb.HomestayOrder
	1, // 2: pb.order.createHomestayOrder:input_type -> pb.CreateHomestayOrderReq
	3, // 3: pb.order.homestayOrderDetail:input_type -> pb.HomestayOrderDetailReq
	5, // 4: pb.order.updateHomestayOrderTradeState:input_type -> pb.UpdateHomestayOrderTradeStateReq
	7, // 5: pb.order.userHomestayOrderList:input_type -> pb.UserHomestayOrderListReq
	2, // 6: pb.order.createHomestayOrder:output_type -> pb.CreateHomestayOrderResp
	4, // 7: pb.order.homestayOrderDetail:output_type -> pb.HomestayOrderDetailResp
	6, // 8: pb.order.updateHomestayOrderTradeState:output_type -> pb.UpdateHomestayOrderTradeStateResp
	8, // 9: pb.order.userHomestayOrderList:output_type -> pb.UserHomestayOrderListResp
	6, // [6:10] is the sub-list for method output_type
	2, // [2:6] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_order_proto_init() }
func file_order_proto_init() {
	if File_order_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_order_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HomestayOrder); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_order_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateHomestayOrderReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_order_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateHomestayOrderResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_order_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HomestayOrderDetailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_order_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HomestayOrderDetailResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_order_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateHomestayOrderTradeStateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_order_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateHomestayOrderTradeStateResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_order_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserHomestayOrderListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_order_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserHomestayOrderListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_order_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_order_proto_goTypes,
		DependencyIndexes: file_order_proto_depIdxs,
		MessageInfos:      file_order_proto_msgTypes,
	}.Build()
	File_order_proto = out.File
	file_order_proto_rawDesc = nil
	file_order_proto_goTypes = nil
	file_order_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// OrderClient is the client API for Order service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type OrderClient interface {
	//民宿下订单
	CreateHomestayOrder(ctx context.Context, in *CreateHomestayOrderReq, opts ...grpc.CallOption) (*CreateHomestayOrderResp, error)
	//民宿订单详情
	HomestayOrderDetail(ctx context.Context, in *HomestayOrderDetailReq, opts ...grpc.CallOption) (*HomestayOrderDetailResp, error)
	//更新民宿订单状态
	UpdateHomestayOrderTradeState(ctx context.Context, in *UpdateHomestayOrderTradeStateReq, opts ...grpc.CallOption) (*UpdateHomestayOrderTradeStateResp, error)
	//用户民宿订单
	UserHomestayOrderList(ctx context.Context, in *UserHomestayOrderListReq, opts ...grpc.CallOption) (*UserHomestayOrderListResp, error)
}

type orderClient struct {
	cc grpc.ClientConnInterface
}

func NewOrderClient(cc grpc.ClientConnInterface) OrderClient {
	return &orderClient{cc}
}

func (c *orderClient) CreateHomestayOrder(ctx context.Context, in *CreateHomestayOrderReq, opts ...grpc.CallOption) (*CreateHomestayOrderResp, error) {
	out := new(CreateHomestayOrderResp)
	err := c.cc.Invoke(ctx, "/pb.order/createHomestayOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderClient) HomestayOrderDetail(ctx context.Context, in *HomestayOrderDetailReq, opts ...grpc.CallOption) (*HomestayOrderDetailResp, error) {
	out := new(HomestayOrderDetailResp)
	err := c.cc.Invoke(ctx, "/pb.order/homestayOrderDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderClient) UpdateHomestayOrderTradeState(ctx context.Context, in *UpdateHomestayOrderTradeStateReq, opts ...grpc.CallOption) (*UpdateHomestayOrderTradeStateResp, error) {
	out := new(UpdateHomestayOrderTradeStateResp)
	err := c.cc.Invoke(ctx, "/pb.order/updateHomestayOrderTradeState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orderClient) UserHomestayOrderList(ctx context.Context, in *UserHomestayOrderListReq, opts ...grpc.CallOption) (*UserHomestayOrderListResp, error) {
	out := new(UserHomestayOrderListResp)
	err := c.cc.Invoke(ctx, "/pb.order/userHomestayOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OrderServer is the server API for Order service.
type OrderServer interface {
	//民宿下订单
	CreateHomestayOrder(context.Context, *CreateHomestayOrderReq) (*CreateHomestayOrderResp, error)
	//民宿订单详情
	HomestayOrderDetail(context.Context, *HomestayOrderDetailReq) (*HomestayOrderDetailResp, error)
	//更新民宿订单状态
	UpdateHomestayOrderTradeState(context.Context, *UpdateHomestayOrderTradeStateReq) (*UpdateHomestayOrderTradeStateResp, error)
	//用户民宿订单
	UserHomestayOrderList(context.Context, *UserHomestayOrderListReq) (*UserHomestayOrderListResp, error)
}

// UnimplementedOrderServer can be embedded to have forward compatible implementations.
type UnimplementedOrderServer struct {
}

func (*UnimplementedOrderServer) CreateHomestayOrder(context.Context, *CreateHomestayOrderReq) (*CreateHomestayOrderResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateHomestayOrder not implemented")
}
func (*UnimplementedOrderServer) HomestayOrderDetail(context.Context, *HomestayOrderDetailReq) (*HomestayOrderDetailResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HomestayOrderDetail not implemented")
}
func (*UnimplementedOrderServer) UpdateHomestayOrderTradeState(context.Context, *UpdateHomestayOrderTradeStateReq) (*UpdateHomestayOrderTradeStateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateHomestayOrderTradeState not implemented")
}
func (*UnimplementedOrderServer) UserHomestayOrderList(context.Context, *UserHomestayOrderListReq) (*UserHomestayOrderListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserHomestayOrderList not implemented")
}

func RegisterOrderServer(s *grpc.Server, srv OrderServer) {
	s.RegisterService(&_Order_serviceDesc, srv)
}

func _Order_CreateHomestayOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateHomestayOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServer).CreateHomestayOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.order/CreateHomestayOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServer).CreateHomestayOrder(ctx, req.(*CreateHomestayOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_HomestayOrderDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HomestayOrderDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServer).HomestayOrderDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.order/HomestayOrderDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServer).HomestayOrderDetail(ctx, req.(*HomestayOrderDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_UpdateHomestayOrderTradeState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateHomestayOrderTradeStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServer).UpdateHomestayOrderTradeState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.order/UpdateHomestayOrderTradeState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServer).UpdateHomestayOrderTradeState(ctx, req.(*UpdateHomestayOrderTradeStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Order_UserHomestayOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserHomestayOrderListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrderServer).UserHomestayOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.order/UserHomestayOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrderServer).UserHomestayOrderList(ctx, req.(*UserHomestayOrderListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Order_serviceDesc = grpc.ServiceDesc{
	ServiceName: "pb.order",
	HandlerType: (*OrderServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createHomestayOrder",
			Handler:    _Order_CreateHomestayOrder_Handler,
		},
		{
			MethodName: "homestayOrderDetail",
			Handler:    _Order_HomestayOrderDetail_Handler,
		},
		{
			MethodName: "updateHomestayOrderTradeState",
			Handler:    _Order_UpdateHomestayOrderTradeState_Handler,
		},
		{
			MethodName: "userHomestayOrderList",
			Handler:    _Order_UserHomestayOrderList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "order.proto",
}
