### 15、发布服务到k8s



#### 1、概述

上一节，我们已经把gitlab、jenkins、harbor、k8s都已经搭建好了，这一节我们来编写jenkins的pipline将我们的服务通过jenkins完整的发布到k8s中。



#### 2、部署中间件

将mysql、redis、es等部署到k8s之外 ， 模拟用作线上独立环境（至于线上你想把某些中间件部署到k8s内部这个自行处理，本次重点是如何将go-zero开发的微服务部署到k8s集群内部），这里我就直接使用项目下的docker-compose-env.yaml了，把所有依赖的第三方中间件环境直接安装在srv-data.com(*************)这台服务器，前提是这台服务器已经安装好docker、docker-compose。

登陆到 *************

```shell
$ mkdir data && cd data && vim docker-compose.yml
$ docker-compose up -d
$ docker-compose ps #查看确认
```





#### 3、独立配置

将每个服务的配置都独立出来，统一放在一个git仓库，这样只给一个人线上仓库的权限，如果线上配置有变直接修改这个仓库的文件，在jenkins做cd的时候，会先拉取代码在拉取对应服务的配置自动构建，具体可以看后面的pipline。

【问】为什么不用配置中心？

1）修改db、redis等需要重启服务，但是有一些配置又不需要重启服务，运维有要去记，记混了比较容易造成线上事故

2）方便回滚。我们发新版本到线上，并且又改了新版本配置。这时候线上用户反馈有问题，线上需要快速回滚的话，如果我们使用将文件构建到镜像中，直接使用k8s一行命令就可以将上一个版本代码加配置直接回滚回来。如果使用了配置中心，回滚了代码，还要将上个版本的配置去陪中心改回来很麻烦，

独立线上仓库目录结构如下（这个结构是跟pipline中写法相关的）

<img src="./images/15/image-20220213134628497.png" alt="image-20220213134628497" style="zoom:50%;" />

仓库地址 ： https://github.com/Mikaelemmmm/go-zero-looklook-pro-conf , 直接下载就好

【注】1、修改配置中的中间件，数据库、redis等都要改成*************这台机器，我们把这台机器当成线上环境的中间件。

​			2、另外一个就是我们的服务发现，线上我们部署在k8s中，go-zero直接支持k8s服务发现，所以不需要etcd等，我们在配置zrpc client的时候，要改成target，k8s的配置方式。





#### 4、编写jenkins的pipline

##### 2.1 配置参数

访问 http://*************:8989/ 打开jenkins，进入jenkins首页，点击左侧菜单`新建Item`

我们先创建identity`授权服务的流水线

![image-20220209195418130](./images/15/image-20220209195418130.png)

然后点击“General” , 选择“This project is parameterized” ， "添加参数"，“Choice Parameter”，如下图

![image-20220209195724082](./images/15/image-20220209195724082.png)

然后编写内容如下

![image-20220209195853856](./images/15/image-20220209195853856.png)

直接保存。

##### 2.2 编写pipline

向下滑动找到`Pipeline script`,填写脚本内容

```pipline
pipeline {
  agent any
  parameters {
      gitParameter name: 'branch',
      type: 'PT_BRANCH',
      branchFilter: 'origin/(.*)',
      defaultValue: 'master',
      selectedValue: 'DEFAULT',
      sortMode: 'ASCENDING_SMART',
      description: '选择需要构建的分支'
  }

  stages {
      stage('服务信息')    {
          steps {
              sh 'echo 分支：$branch'
              sh 'echo 构建服务类型：${JOB_NAME}-$type'
          }
      }


      stage('拉取代码') {
          steps {
              checkout([$class: 'GitSCM',
              branches: [[name: '$branch']],
              doGenerateSubmoduleConfigurations: false,
              extensions: [],
              submoduleCfg: [],
              userRemoteConfigs: [[credentialsId: 'gitlab-cert', url: 'ssh://git@*************:2222/root/go-zero-looklook.git']]])
          }
      }
      stage('获取commit_id') {
          steps {
              echo '获取commit_id'
              git credentialsId: 'gitlab-cert', url: 'ssh://git@*************:2222/root/go-zero-looklook.git'
              script {
                  env.commit_id = sh(returnStdout: true, script: 'git rev-parse --short HEAD').trim()
              }
          }
      }
      stage('拉取配置文件') {
              steps {
                  checkout([$class: 'GitSCM',
                  branches: [[name: '$branch']],
                  doGenerateSubmoduleConfigurations: false,
                  extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: 'conf']],
                  submoduleCfg: [],
                  userRemoteConfigs: [[credentialsId: 'gitlab-cert', url: 'ssh://git@*************:2222/root/go-zero-looklook-pro-conf.git']]])
              }
        }

      stage('goctl版本检测') {
          steps{
              sh '/usr/local/bin/goctl -v'
          }
      }

      stage('Dockerfile Build') {
          steps{
                 sh 'yes | cp  -rf conf/${JOB_NAME}/${type}/${JOB_NAME}.yaml  app/${JOB_NAME}/cmd/${type}/etc'   //线上配置文件
                 sh 'cd app/${JOB_NAME}/cmd/${type} && /usr/local/bin/goctl docker -go ${JOB_NAME}.go && ls -l'
                 script{
                     env.image = sh(returnStdout: true, script: 'echo ${JOB_NAME}-${type}:${commit_id}').trim()
                 }
                 sh 'echo 镜像名称：${image} && cp app/${JOB_NAME}/cmd/${type}/Dockerfile ./  && ls -l && docker build  -t ${image} .'
          }
      }

      stage('上传到镜像仓库') {
          steps{
          	  //docker login 这里要注意，会把账号密码输出到jenkins页面，可以通过port.sh类似方式处理，官网文档有这里我就不详细写了
              sh 'docker login --username=${docker_username} --password=${docker_pwd} http://${docker_repo}'
              sh 'docker tag  ${image} ${docker_repo}/go-zero-looklook/${image}'
              sh 'docker push ${docker_repo}/go-zero-looklook/${image}'
          }
      }

      stage('部署到k8s') {
          steps{
              script{
                  env.deployYaml = sh(returnStdout: true, script: 'echo ${JOB_NAME}-${type}-deploy.yaml').trim()
                  env.port=sh(returnStdout: true, script: '/root/port.sh ${JOB_NAME}-${type}').trim()
              }

              sh 'echo ${port}'

              sh 'rm -f ${deployYaml}'
              sh '/usr/local/bin/goctl kube deploy -secret docker-login -replicas 2 -nodePort 3${port} -requestCpu 200 -requestMem 50 -limitCpu 300 -limitMem 100 -name ${JOB_NAME}-${type} -namespace go-zero-looklook -image ${docker_repo}/${image} -o ${deployYaml} -port ${port} -serviceAccount find-endpoints '
              sh '/usr/local/bin/kubectl apply -f ${deployYaml}'
          }
      }

       stage('Clean') {
           steps{
               sh 'docker rmi -f ${image}'
               sh 'docker rmi -f ${docker_repo}/${image}'
               cleanWs notFailBuild: true
           }
       }
  }
}
```

【注】：！！！非常重要！！！

1、构建优化：pipline中使用"/usr/local/bin/goctl kube xxx"生k8s yaml的时候，我们是使用k8s方式部署不需要etcd，但是这种方式部署需要为生成的k8s yaml中指定serviceAccount。 原理可以看这篇文章下方go-zero的k8s服务发现讲解 ：https://mp.weixin.qq.com/s/-WaWJaM_ePEQOf7ExNJe7w

我这边已经指定好了serviceAccount ： find-endpoints 在模版下kube文件夹中模版中：

https://github.com/Mikaelemmmm/go-zero-looklook/tree/main/deploy/goctl

所以你需要在你的k8s创建find-endpoints这个serviceAccount并绑定相应权限，yaml文件我已经准备好了，你只需要执行

kubectl apply -f auth.yaml 即可 ，auth.yaml文件如下：

````yaml
#创建账号
apiVersion: v1
kind: ServiceAccount
metadata:
  namespace: go-zero-looklook
  name: find-endpoints

---
#创建角色对应操作
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: discov-endpoints
rules:
- apiGroups: [""]
  resources: ["endpoints"]
  verbs: ["get","list","watch"]

---
#给账号绑定角色
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: find-endpoints-discov-endpoints
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: discov-endpoints
subjects:
- kind: ServiceAccount
  name: find-endpoints
  namespace: go-zero-looklook
````

【注】如果你的goctl>=1.3.3 ，pipline生成k8s yaml的文件可以不需要使用模版方式支持serviceAccount 可以在生成时候指定serviceAccount，也就是说pipline中可以直接指定-serviceAcount 直接就在生成k8s的yaml中添加serviceAccount : find-endpoints，如下命令

```shell
/usr/local/bin/goctl kube deploy -secret docker-login -replicas 2 -nodePort 3${port} -requestCpu 200 -requestMem 50 -limitCpu 300 -limitMem 100 -name ${JOB_NAME}-${type} -namespace go-zero-looklook -image ${docker_repo}/${image} -o ${deployYaml} -port ${port} --serviceAccount find-endpoints
```

但是如果你的goctl <=1.3.2，你只能手动去修改你的模版下kube文件夹下的2个模版，自己在模版中添加serviceAccountName : find-endpoints （参考模版链接：https://github.com/Mikaelemmmm/go-zero-looklook/tree/v1.0.2/deploy/goctl） ，然后使用--home指定你改好的模版来生成k8s yaml，命令如下，你在pipline中要把上面这个命令改成如下命令

```sh
/usr/local/bin/goctl kube deploy -secret docker-login -replicas 2 -nodePort 3${port} -requestCpu 200 -requestMem 50 -limitCpu 300 -limitMem 100 -name ${JOB_NAME}-${type} -namespace go-zero-looklook -image ${docker_repo}/${image} -o ${deployYaml} -port ${port} --home /root/template
```

因为1.3.3开始goctl支持--serviceAccount参数了，如果goctl<=1.3.2还是要通过pipline中指定的模版实现



2、${credentialsId}要替换为你的具体凭据值，即【添加凭据】模块中的一串字符串，我们之前配置的是gitlab-cert所以这里就填写gitlab-cert，如果你不是这个自己要更换，${gitUrl}需要替换为你代码的git仓库地址，其他的${xxx}形式的变量无需修改，保持原样即可。



3、这里跟官方文档有一点点不一样，由于我项目文件夹目录不同，goctl生成的dockerfile文件我手动做了点调整，在一个我不是在构建时候生成的dockerfile，是在创建项目时候就把dockerfile一起放在目录下，这样构建镜像时候不需要goctl了



#### 5、配置k8s拉取私有仓库镜像

k8s在默认情况下，只能拉取harbor镜像仓库的公有镜像，如果拉取私有仓库镜像，则是会报 `ErrImagePull` 和 `ImagePullBackOff` 的错误

1、先在jenkins发布机器登陆harbor

```shell
$ docker login *************:8077
$ Username: admin
$ Password:
Login Succeeded
```

2、在k8s中生成登陆harbor配置文件

```shell
#查看上一步登陆harbor生成的凭证
$ cat /root/.docker/config.json
{
	"auths": {
		"*************:8077": {
			"auth": "************************"
		}
}
```

3、对秘钥文件进行base64加密

```shell
$ cat /root/.docker/config.json  | base64 -w 0

********************************************************************************************************************
```

4、创建docker-secret.yaml

```yml
apiVersion: v1
kind: Secret
metadata:
  name: docker-login
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ********************************************************************************************************************
```

```shell
$ kubectl create -f docker-secret.yaml -n go-zero-looklook

secret "docker-login" created
```





#### 6、构建

我们进入首页，点击idenity进入详情页

![image-20220209201812134](./images/15/image-20220209201812134.png)



然后可以看到，上面我们配置好的identity服务，如下图 ，点击“Build with Parameters”， 然后选择rpc,点击“开始构建”

![image-20220209201927466](./images/15/image-20220209201927466.png)



【注】第一次构建在拉代码时候都会失败，应该是初始化啥东西，再点一次就好了。





部署成功

![image-20220211142524599](./images/15/image-20220211142524599.png)

![image-20220211142613065](./images/15/image-20220211142613065.png)

![image-20220211142729231](./images/15/image-20220211142729231.png)





同样道理，在去构建identity-api，再去配置usercenter服务 构建usercenter-rpc、构建usercenter-api，接着配置其他服务、构建即可，本次我们先只构建identity-api、identity-rpc、usercenter-rpc、usercenter-api给大家演示。





#### 6、添加网关

因为我们的api服务通过goctl发布在k8s中都会暴露nodeport端口，索引我们看下k8s中go-zero-looklook命名空间下的service的nodeport端口服务，然后将nodeport配置在nginx即可。



本次我们独立一台虚拟机在k8s之外，安装nginx，将k8s后端api服务通过nodeport方式把端口暴露给nginx，然后nginx在配置中配置此api服务，这样nginx就充当网关使用。

nginx的安装就不再这里多说了，记得一定要有auth_request模块，没有的话自己去安装。

nginx的配置

```conf
server{
    listen 8081;
    access_log /var/log/nginx/looklook.com_access.log;
    error_log /var/log/nginx//looklook.com_error.log;

    location /auth {
	    internal;
      proxy_set_header X-Original-URI $request_uri;
	    proxy_pass_request_body off;
	    proxy_set_header Content-Length "";
	    proxy_pass http://*************:31001/identity/v1/verify/token;
    }

    location ~ /usercenter/ {
       auth_request /auth;
       auth_request_set $user $upstream_http_x_user;
       proxy_set_header x-user $user;

       proxy_set_header Host $http_host;
       proxy_set_header X-Real-IP $remote_addr;
       proxy_set_header REMOTE-HOST $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_pass http://*************:31002;
   }

   location ~ /travel/ {
       auth_request /auth;
       auth_request_set $user $upstream_http_x_user;
       proxy_set_header x-user $user;

       proxy_set_header Host $http_host;
       proxy_set_header X-Real-IP $remote_addr;
       proxy_set_header REMOTE-HOST $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_pass http://*************:31003;
   }


    location ~ /order/ {
       auth_request /auth;
       auth_request_set $user $upstream_http_x_user;
       proxy_set_header x-user $user;

       proxy_set_header Host $http_host;
       proxy_set_header X-Real-IP $remote_addr;
       proxy_set_header REMOTE-HOST $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_pass http://*************:31004;
   }

    location ~ /payment/ {
       auth_request /auth;
       auth_request_set $user $upstream_http_x_user;
       proxy_set_header x-user $user;

       proxy_set_header Host $http_host;
       proxy_set_header X-Real-IP $remote_addr;
       proxy_set_header REMOTE-HOST $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_pass http://*************:31005;
   }
}
```

如果是线上的话，应该配置多台nginx保持高可用，在nginx前面还会有一个slb，你的域名包括https配置都应该解析到slb，在slb前面在有防火墙等这些。



#### 8、结束语

至此，整个系列就结束了，整体架构图应该如第一篇所展示，本系列希望能给你带来帮助。
