syntax = "v1"

info(
    title: "抽奖发起人联系方式"
    desc: "抽奖发起人联系方式"
)

type Contact {
    Id int64 `json:"id"`
    UserId int64 `json:"userId"`
    Content string `json:"content"`
    Remark string `json:"remark"`
}

type (
    CreateContactReq {
        Content string `json:"content"`
        Remark string `json:"remark"`
    }
    CreateContactResp {
        Id int64 `json:"id"`
    }
)

type (
    UpDateContactReq {
        Id int64 `json:"id"`
        Content string `json:"content"`
        Remark string `json:"remark"`
    }
    UpDateContactResp {
        Id int64 `json:"id"`
    }
)

type (
    ContactDetailReq {
        Id int64 `json:"id"`
    }
    ContactDetailResp {
        Id int64 `json:"id"`
        Content string `json:"content"`
        Remark string `json:"remark"`
    }
)

type (
    ContactDelReq {
        Id []int64 `json:"id"`
    }
    ContactDelResp {
    }
)

type (
    ContactListReq {
        Page int64 `json:"page,range=[1:]"`
        PageSize int64 `json:"pageSize"`
    }
    ContactListResp {
        List []Contact `json:"list"`
    }
)