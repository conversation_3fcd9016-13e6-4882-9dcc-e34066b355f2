package logic

import (
	"context"
	"github.com/jinzhu/copier"

	"looklook/app/shop/cmd/rpc/internal/svc"
	"looklook/app/shop/cmd/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetWishExchangeListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetWishExchangeListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetWishExchangeListLogic {
	return &GetWishExchangeListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetWishExchangeListLogic) GetWishExchangeList(in *pb.GoodsWishListReq) (*pb.GoodsWishListResp, error) {
	// todo: add your logic here and delete this line
	list, err := l.svcCtx.WishRedemptionRecords.GetWishExchangeListByUId(in.UserId, in.Page, in.PageSize)
	if err != nil {
		return nil, err
	}

	wishes := make([]*pb.WishExchange, 0)

	for _, item := range list {
		pbWishes := new(pb.WishExchange)
		_ = copier.Copy(pbWishes, item)
		wishes = append(wishes, pbWishes)
	}

	logx.Errorf("Testt-7 wishes: %v", wishes)
	return &pb.GoodsWishListResp{
		Total: int64(len(list)),
		List:  wishes,
	}, nil
}
