// Code generated by goctl. DO NOT EDIT.
// Source: checkin.proto

package checkin

import (
	"context"

	"looklook/app/checkin/cmd/rpc/pb"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	AddCheckinRecordReq           = pb.AddCheckinRecordReq
	AddCheckinRecordResp          = pb.AddCheckinRecordResp
	AddIntegralRecordReq          = pb.AddIntegralRecordReq
	AddIntegralRecordResp         = pb.AddIntegralRecordResp
	AddIntegralReq                = pb.AddIntegralReq
	AddIntegralResp               = pb.AddIntegralResp
	AddTaskRecordReq              = pb.AddTaskRecordReq
	AddTaskRecordResp             = pb.AddTaskRecordResp
	AddTasksReq                   = pb.AddTasksReq
	AddTasksResp                  = pb.AddTasksResp
	CheckinRecord                 = pb.CheckinRecord
	DeductIntegralReq             = pb.DeductIntegralReq
	DeductIntegralResp            = pb.DeductIntegralResp
	DelIntegralRecordReq          = pb.DelIntegralRecordReq
	DelIntegralRecordResp         = pb.DelIntegralRecordResp
	DelTaskRecordReq              = pb.DelTaskRecordReq
	DelTaskRecordResp             = pb.DelTaskRecordResp
	DelTasksReq                   = pb.DelTasksReq
	DelTasksResp                  = pb.DelTasksResp
	GetCheckinRecordByUserIdReq   = pb.GetCheckinRecordByUserIdReq
	GetCheckinRecordByUserIdResp  = pb.GetCheckinRecordByUserIdResp
	GetIntegralByUserIdReq        = pb.GetIntegralByUserIdReq
	GetIntegralByUserIdResp       = pb.GetIntegralByUserIdResp
	GetIntegralRecordByIdReq      = pb.GetIntegralRecordByIdReq
	GetIntegralRecordByIdResp     = pb.GetIntegralRecordByIdResp
	GetIntegralRecordByUserIdReq  = pb.GetIntegralRecordByUserIdReq
	GetIntegralRecordByUserIdResp = pb.GetIntegralRecordByUserIdResp
	GetTaskProgressReq            = pb.GetTaskProgressReq
	GetTaskProgressResp           = pb.GetTaskProgressResp
	GetTaskRecordByIdReq          = pb.GetTaskRecordByIdReq
	GetTaskRecordByIdResp         = pb.GetTaskRecordByIdResp
	GetTaskRecordByUserIdReq      = pb.GetTaskRecordByUserIdReq
	GetTaskRecordByUserIdResp     = pb.GetTaskRecordByUserIdResp
	GetTasksByIdReq               = pb.GetTasksByIdReq
	GetTasksByIdResp              = pb.GetTasksByIdResp
	Integral                      = pb.Integral
	IntegralRecord                = pb.IntegralRecord
	NoticeWishCheckinData         = pb.NoticeWishCheckinData
	NoticeWishCheckinReq          = pb.NoticeWishCheckinReq
	NoticeWishCheckinResp         = pb.NoticeWishCheckinResp
	SearchIntegralRecordReq       = pb.SearchIntegralRecordReq
	SearchIntegralRecordResp      = pb.SearchIntegralRecordResp
	SearchTaskRecordReq           = pb.SearchTaskRecordReq
	SearchTaskRecordResp          = pb.SearchTaskRecordResp
	SearchTasksReq                = pb.SearchTasksReq
	SearchTasksResp               = pb.SearchTasksResp
	TaskProgress                  = pb.TaskProgress
	TaskRecord                    = pb.TaskRecord
	Tasks                         = pb.Tasks
	UpdateCheckinRecordReq        = pb.UpdateCheckinRecordReq
	UpdateCheckinRecordResp       = pb.UpdateCheckinRecordResp
	UpdateIntegralRecordReq       = pb.UpdateIntegralRecordReq
	UpdateIntegralRecordResp      = pb.UpdateIntegralRecordResp
	UpdateIntegralReq             = pb.UpdateIntegralReq
	UpdateIntegralResp            = pb.UpdateIntegralResp
	UpdateSubReq                  = pb.UpdateSubReq
	UpdateSubResp                 = pb.UpdateSubResp
	UpdateTaskRecordReq           = pb.UpdateTaskRecordReq
	UpdateTaskRecordResp          = pb.UpdateTaskRecordResp
	UpdateTasksReq                = pb.UpdateTasksReq
	UpdateTasksResp               = pb.UpdateTasksResp

	Checkin interface {
		// -----------------------checkinRecord--------------------
		AddCheckinRecord(ctx context.Context, in *AddCheckinRecordReq, opts ...grpc.CallOption) (*AddCheckinRecordResp, error)
		UpdateCheckinRecord(ctx context.Context, in *UpdateCheckinRecordReq, opts ...grpc.CallOption) (*UpdateCheckinRecordResp, error)
		GetCheckinRecordByUserId(ctx context.Context, in *GetCheckinRecordByUserIdReq, opts ...grpc.CallOption) (*GetCheckinRecordByUserIdResp, error)
		// -----------------------integralRecord-------------------
		AddIntegral(ctx context.Context, in *AddIntegralReq, opts ...grpc.CallOption) (*AddIntegralResp, error)
		AddIntegralRecord(ctx context.Context, in *AddIntegralRecordReq, opts ...grpc.CallOption) (*AddIntegralRecordResp, error)
		UpdateIntegralRecord(ctx context.Context, in *UpdateIntegralRecordReq, opts ...grpc.CallOption) (*UpdateIntegralRecordResp, error)
		DelIntegralRecord(ctx context.Context, in *DelIntegralRecordReq, opts ...grpc.CallOption) (*DelIntegralRecordResp, error)
		GetIntegralRecordById(ctx context.Context, in *GetIntegralRecordByIdReq, opts ...grpc.CallOption) (*GetIntegralRecordByIdResp, error)
		GetIntegralRecordByUserId(ctx context.Context, in *GetIntegralRecordByUserIdReq, opts ...grpc.CallOption) (*GetIntegralRecordByUserIdResp, error)
		SearchIntegralRecord(ctx context.Context, in *SearchIntegralRecordReq, opts ...grpc.CallOption) (*SearchIntegralRecordResp, error)
		DeductIntegral(ctx context.Context, in *DeductIntegralReq, opts ...grpc.CallOption) (*DeductIntegralResp, error)
		// -----------------------taskRecord-----------------------
		AddTaskRecord(ctx context.Context, in *AddTaskRecordReq, opts ...grpc.CallOption) (*AddTaskRecordResp, error)
		UpdateTaskRecord(ctx context.Context, in *UpdateTaskRecordReq, opts ...grpc.CallOption) (*UpdateTaskRecordResp, error)
		DelTaskRecord(ctx context.Context, in *DelTaskRecordReq, opts ...grpc.CallOption) (*DelTaskRecordResp, error)
		GetTaskRecordById(ctx context.Context, in *GetTaskRecordByIdReq, opts ...grpc.CallOption) (*GetTaskRecordByIdResp, error)
		GetTaskRecordByUserId(ctx context.Context, in *GetTaskRecordByUserIdReq, opts ...grpc.CallOption) (*GetTaskRecordByUserIdResp, error)
		SearchTaskRecord(ctx context.Context, in *SearchTaskRecordReq, opts ...grpc.CallOption) (*SearchTaskRecordResp, error)
		// -----------------------tasks-----------------------------
		AddTasks(ctx context.Context, in *AddTasksReq, opts ...grpc.CallOption) (*AddTasksResp, error)
		UpdateTasks(ctx context.Context, in *UpdateTasksReq, opts ...grpc.CallOption) (*UpdateTasksResp, error)
		DelTasks(ctx context.Context, in *DelTasksReq, opts ...grpc.CallOption) (*DelTasksResp, error)
		GetTasksById(ctx context.Context, in *GetTasksByIdReq, opts ...grpc.CallOption) (*GetTasksByIdResp, error)
		SearchTasks(ctx context.Context, in *SearchTasksReq, opts ...grpc.CallOption) (*SearchTasksResp, error)
		// -----------------------taskProgress----------------------
		GetTaskProgress(ctx context.Context, in *GetTaskProgressReq, opts ...grpc.CallOption) (*GetTaskProgressResp, error)
		UpdateSub(ctx context.Context, in *UpdateSubReq, opts ...grpc.CallOption) (*UpdateSubResp, error)
		// -----------------------others----------------------
		NoticeWishCheckin(ctx context.Context, in *NoticeWishCheckinReq, opts ...grpc.CallOption) (*NoticeWishCheckinResp, error)
		GetIntegralByUserId(ctx context.Context, in *GetIntegralByUserIdReq, opts ...grpc.CallOption) (*GetIntegralByUserIdResp, error)
	}

	defaultCheckin struct {
		cli zrpc.Client
	}
)

func NewCheckin(cli zrpc.Client) Checkin {
	return &defaultCheckin{
		cli: cli,
	}
}

// -----------------------checkinRecord--------------------
func (m *defaultCheckin) AddCheckinRecord(ctx context.Context, in *AddCheckinRecordReq, opts ...grpc.CallOption) (*AddCheckinRecordResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.AddCheckinRecord(ctx, in, opts...)
}

func (m *defaultCheckin) UpdateCheckinRecord(ctx context.Context, in *UpdateCheckinRecordReq, opts ...grpc.CallOption) (*UpdateCheckinRecordResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.UpdateCheckinRecord(ctx, in, opts...)
}

func (m *defaultCheckin) GetCheckinRecordByUserId(ctx context.Context, in *GetCheckinRecordByUserIdReq, opts ...grpc.CallOption) (*GetCheckinRecordByUserIdResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.GetCheckinRecordByUserId(ctx, in, opts...)
}

// -----------------------integralRecord-------------------
func (m *defaultCheckin) AddIntegral(ctx context.Context, in *AddIntegralReq, opts ...grpc.CallOption) (*AddIntegralResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.AddIntegral(ctx, in, opts...)
}

func (m *defaultCheckin) AddIntegralRecord(ctx context.Context, in *AddIntegralRecordReq, opts ...grpc.CallOption) (*AddIntegralRecordResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.AddIntegralRecord(ctx, in, opts...)
}

func (m *defaultCheckin) UpdateIntegralRecord(ctx context.Context, in *UpdateIntegralRecordReq, opts ...grpc.CallOption) (*UpdateIntegralRecordResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.UpdateIntegralRecord(ctx, in, opts...)
}

func (m *defaultCheckin) DelIntegralRecord(ctx context.Context, in *DelIntegralRecordReq, opts ...grpc.CallOption) (*DelIntegralRecordResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.DelIntegralRecord(ctx, in, opts...)
}

func (m *defaultCheckin) GetIntegralRecordById(ctx context.Context, in *GetIntegralRecordByIdReq, opts ...grpc.CallOption) (*GetIntegralRecordByIdResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.GetIntegralRecordById(ctx, in, opts...)
}

func (m *defaultCheckin) GetIntegralRecordByUserId(ctx context.Context, in *GetIntegralRecordByUserIdReq, opts ...grpc.CallOption) (*GetIntegralRecordByUserIdResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.GetIntegralRecordByUserId(ctx, in, opts...)
}

func (m *defaultCheckin) SearchIntegralRecord(ctx context.Context, in *SearchIntegralRecordReq, opts ...grpc.CallOption) (*SearchIntegralRecordResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.SearchIntegralRecord(ctx, in, opts...)
}

func (m *defaultCheckin) DeductIntegral(ctx context.Context, in *DeductIntegralReq, opts ...grpc.CallOption) (*DeductIntegralResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.DeductIntegral(ctx, in, opts...)
}

// -----------------------taskRecord-----------------------
func (m *defaultCheckin) AddTaskRecord(ctx context.Context, in *AddTaskRecordReq, opts ...grpc.CallOption) (*AddTaskRecordResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.AddTaskRecord(ctx, in, opts...)
}

func (m *defaultCheckin) UpdateTaskRecord(ctx context.Context, in *UpdateTaskRecordReq, opts ...grpc.CallOption) (*UpdateTaskRecordResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.UpdateTaskRecord(ctx, in, opts...)
}

func (m *defaultCheckin) DelTaskRecord(ctx context.Context, in *DelTaskRecordReq, opts ...grpc.CallOption) (*DelTaskRecordResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.DelTaskRecord(ctx, in, opts...)
}

func (m *defaultCheckin) GetTaskRecordById(ctx context.Context, in *GetTaskRecordByIdReq, opts ...grpc.CallOption) (*GetTaskRecordByIdResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.GetTaskRecordById(ctx, in, opts...)
}

func (m *defaultCheckin) GetTaskRecordByUserId(ctx context.Context, in *GetTaskRecordByUserIdReq, opts ...grpc.CallOption) (*GetTaskRecordByUserIdResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.GetTaskRecordByUserId(ctx, in, opts...)
}

func (m *defaultCheckin) SearchTaskRecord(ctx context.Context, in *SearchTaskRecordReq, opts ...grpc.CallOption) (*SearchTaskRecordResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.SearchTaskRecord(ctx, in, opts...)
}

// -----------------------tasks-----------------------------
func (m *defaultCheckin) AddTasks(ctx context.Context, in *AddTasksReq, opts ...grpc.CallOption) (*AddTasksResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.AddTasks(ctx, in, opts...)
}

func (m *defaultCheckin) UpdateTasks(ctx context.Context, in *UpdateTasksReq, opts ...grpc.CallOption) (*UpdateTasksResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.UpdateTasks(ctx, in, opts...)
}

func (m *defaultCheckin) DelTasks(ctx context.Context, in *DelTasksReq, opts ...grpc.CallOption) (*DelTasksResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.DelTasks(ctx, in, opts...)
}

func (m *defaultCheckin) GetTasksById(ctx context.Context, in *GetTasksByIdReq, opts ...grpc.CallOption) (*GetTasksByIdResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.GetTasksById(ctx, in, opts...)
}

func (m *defaultCheckin) SearchTasks(ctx context.Context, in *SearchTasksReq, opts ...grpc.CallOption) (*SearchTasksResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.SearchTasks(ctx, in, opts...)
}

// -----------------------taskProgress----------------------
func (m *defaultCheckin) GetTaskProgress(ctx context.Context, in *GetTaskProgressReq, opts ...grpc.CallOption) (*GetTaskProgressResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.GetTaskProgress(ctx, in, opts...)
}

func (m *defaultCheckin) UpdateSub(ctx context.Context, in *UpdateSubReq, opts ...grpc.CallOption) (*UpdateSubResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.UpdateSub(ctx, in, opts...)
}

// -----------------------others----------------------
func (m *defaultCheckin) NoticeWishCheckin(ctx context.Context, in *NoticeWishCheckinReq, opts ...grpc.CallOption) (*NoticeWishCheckinResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.NoticeWishCheckin(ctx, in, opts...)
}

func (m *defaultCheckin) GetIntegralByUserId(ctx context.Context, in *GetIntegralByUserIdReq, opts ...grpc.CallOption) (*GetIntegralByUserIdResp, error) {
	client := pb.NewCheckinClient(m.cli.Conn())
	return client.GetIntegralByUserId(ctx, in, opts...)
}
