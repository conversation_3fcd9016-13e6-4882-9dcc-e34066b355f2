// Code generated by goctl. DO NOT EDIT.

package model

import (
	"context"
	"database/sql"
	"fmt"
	"looklook/deploy/script/mysql/genModel"
	"strings"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
	"looklook/common/globalkey"
)

var (
	goodsFieldNames          = builder.RawFieldNames(&Goods{})
	goodsRows                = strings.Join(goodsFieldNames, ",")
	goodsRowsExpectAutoSet   = strings.Join(stringx.Remove(goodsFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), ",")
	goodsRowsWithPlaceHolder = strings.Join(stringx.Remove(goodsFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), "=?,") + "=?"

	cacheShopGoodsIdPrefix = "cache:shop:goods:id:"
)

type (
	goodsModel interface {
		Insert(ctx context.Context, data *Goods) (sql.Result, error)
		TransInsert(ctx context.Context, session sqlx.Session, data *Goods) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Goods, error)
		Update(ctx context.Context, data *Goods) error
		List(ctx context.Context, page, limit int64) ([]*Goods, error)
		TransUpdate(ctx context.Context, session sqlx.Session, data *Goods) error
		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		SelectBuilder() squirrel.SelectBuilder
		FindSum(ctx context.Context, sumBuilder squirrel.SelectBuilder, field string) (float64, error)
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder, field string) (int64, error)
		FindAll(ctx context.Context, rowBuilder squirrel.SelectBuilder, orderBy string) ([]*Goods, error)
		FindPageListByPage(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*Goods, error)
		FindPageListByPageWithTotal(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*Goods, int64, error)
		FindPageListByIdDESC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*Goods, error)
		FindPageListByIdASC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*Goods, error)
		Delete(ctx context.Context, id int64) error
	}

	defaultGoodsModel struct {
		sqlc.CachedConn
		table string
	}

	Goods struct {
		Id                   int64          `db:"id"`
		CategoryName         sql.NullString `db:"category_name"`
		CouponRemainQuantity sql.NullInt64  `db:"coupon_remain_quantity"`
		ExtraCouponAmount    sql.NullInt64  `db:"extra_coupon_amount"`
		PromotionRate        sql.NullInt64  `db:"promotion_rate"`
		SubsidyGoodsType     sql.NullInt64  `db:"subsidy_goods_type"`
		CatIds               sql.NullString `db:"cat_ids"`
		CouponMinOrderAmount sql.NullInt64  `db:"coupon_min_order_amount"`
		CategoryId           sql.NullInt64  `db:"category_id"`
		MallId               sql.NullInt64  `db:"mall_id"`
		SubsidyAmount        sql.NullInt64  `db:"subsidy_amount"`
		CouponPrice          sql.NullInt64  `db:"coupon_price"`
		MallName             sql.NullString `db:"mall_name"`
		CouponTotalQuantity  sql.NullInt64  `db:"coupon_total_quantity"`
		MarketFee            sql.NullInt64  `db:"market_fee"`
		MerchantType         sql.NullInt64  `db:"merchant_type"`
		LgstTxt              sql.NullString `db:"lgst_txt"`
		GoodsName            sql.NullString `db:"goods_name"`
		SalesTip             sql.NullString `db:"sales_tip"`
		HasMaterial          sql.NullString `db:"has_material"`
		GoodsId              sql.NullInt64  `db:"goods_id"`
		PredictPromotionRate sql.NullInt64  `db:"predict_promotion_rate"`
		DescTxt              sql.NullString `db:"desc_txt"`
		GoodsDesc            sql.NullString `db:"goods_desc"`
		OptName              sql.NullString `db:"opt_name"`
		RealtimeSalesTip     sql.NullTime   `db:"realtime_sales_tip"`
		ShareRate            sql.NullInt64  `db:"share_rate"`
		GoodsThumbnailUrl    sql.NullString `db:"goods_thumbnail_url"`
		OptIds               sql.NullString `db:"opt_ids"`
		OptId                sql.NullInt64  `db:"opt_id"`
		SearchId             sql.NullInt64  `db:"search_id"`
		GoodsImageUrl        sql.NullString `db:"goods_image_url"`
		ActivityTags         sql.NullString `db:"activity_tags"`
		HasCoupon            sql.NullString `db:"has_coupon"`
		MinNormalPrice       sql.NullInt64  `db:"min_normal_price"`
		ServTxt              sql.NullString `db:"serv_txt"`
		UnifiedTags          sql.NullString `db:"unified_tags"`
		CouponStartTime      sql.NullInt64  `db:"coupon_start_time"`
		MinGroupPrice        sql.NullInt64  `db:"min_group_price"`
		CouponDiscount       sql.NullInt64  `db:"coupon_discount"`
		GoodsSign            sql.NullString `db:"goods_sign"`
		CouponEndTime        sql.NullInt64  `db:"coupon_end_time"`
	}
)

func newGoodsModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultGoodsModel {
	return &defaultGoodsModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`goods`",
	}
}

func (m *defaultGoodsModel) Delete(ctx context.Context, id int64) error {
	shopGoodsIdKey := fmt.Sprintf("%s%v", cacheShopGoodsIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		return conn.ExecCtx(ctx, query, id)
	}, shopGoodsIdKey)
	return err
}

func (m *defaultGoodsModel) FindOne(ctx context.Context, id int64) (*Goods, error) {
	shopGoodsIdKey := fmt.Sprintf("%s%v", cacheShopGoodsIdPrefix, id)
	var resp Goods
	err := m.QueryRowCtx(ctx, &resp, shopGoodsIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", goodsRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, genModel.ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultGoodsModel) Insert(ctx context.Context, data *Goods) (sql.Result, error) {
	shopGoodsIdKey := fmt.Sprintf("%s%v", cacheShopGoodsIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, goodsRowsExpectAutoSet)
		return conn.ExecCtx(ctx, query, data.CategoryName, data.CouponRemainQuantity, data.ExtraCouponAmount, data.PromotionRate, data.SubsidyGoodsType, data.CatIds, data.CouponMinOrderAmount, data.CategoryId, data.MallId, data.SubsidyAmount, data.CouponPrice, data.MallName, data.CouponTotalQuantity, data.MarketFee, data.MerchantType, data.LgstTxt, data.GoodsName, data.SalesTip, data.HasMaterial, data.GoodsId, data.PredictPromotionRate, data.DescTxt, data.GoodsDesc, data.OptName, data.RealtimeSalesTip, data.ShareRate, data.GoodsThumbnailUrl, data.OptIds, data.OptId, data.SearchId, data.GoodsImageUrl, data.ActivityTags, data.HasCoupon, data.MinNormalPrice, data.ServTxt, data.UnifiedTags, data.CouponStartTime, data.MinGroupPrice, data.CouponDiscount, data.GoodsSign, data.CouponEndTime)
	}, shopGoodsIdKey)
	return ret, err
}

func (m *defaultGoodsModel) TransInsert(ctx context.Context, session sqlx.Session, data *Goods) (sql.Result, error) {
	shopGoodsIdKey := fmt.Sprintf("%s%v", cacheShopGoodsIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, goodsRowsExpectAutoSet)
		return session.ExecCtx(ctx, query, data.CategoryName, data.CouponRemainQuantity, data.ExtraCouponAmount, data.PromotionRate, data.SubsidyGoodsType, data.CatIds, data.CouponMinOrderAmount, data.CategoryId, data.MallId, data.SubsidyAmount, data.CouponPrice, data.MallName, data.CouponTotalQuantity, data.MarketFee, data.MerchantType, data.LgstTxt, data.GoodsName, data.SalesTip, data.HasMaterial, data.GoodsId, data.PredictPromotionRate, data.DescTxt, data.GoodsDesc, data.OptName, data.RealtimeSalesTip, data.ShareRate, data.GoodsThumbnailUrl, data.OptIds, data.OptId, data.SearchId, data.GoodsImageUrl, data.ActivityTags, data.HasCoupon, data.MinNormalPrice, data.ServTxt, data.UnifiedTags, data.CouponStartTime, data.MinGroupPrice, data.CouponDiscount, data.GoodsSign, data.CouponEndTime)
	}, shopGoodsIdKey)
	return ret, err
}
func (m *defaultGoodsModel) Update(ctx context.Context, data *Goods) error {
	shopGoodsIdKey := fmt.Sprintf("%s%v", cacheShopGoodsIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, goodsRowsWithPlaceHolder)
		return conn.ExecCtx(ctx, query, data.CategoryName, data.CouponRemainQuantity, data.ExtraCouponAmount, data.PromotionRate, data.SubsidyGoodsType, data.CatIds, data.CouponMinOrderAmount, data.CategoryId, data.MallId, data.SubsidyAmount, data.CouponPrice, data.MallName, data.CouponTotalQuantity, data.MarketFee, data.MerchantType, data.LgstTxt, data.GoodsName, data.SalesTip, data.HasMaterial, data.GoodsId, data.PredictPromotionRate, data.DescTxt, data.GoodsDesc, data.OptName, data.RealtimeSalesTip, data.ShareRate, data.GoodsThumbnailUrl, data.OptIds, data.OptId, data.SearchId, data.GoodsImageUrl, data.ActivityTags, data.HasCoupon, data.MinNormalPrice, data.ServTxt, data.UnifiedTags, data.CouponStartTime, data.MinGroupPrice, data.CouponDiscount, data.GoodsSign, data.CouponEndTime, data.Id)
	}, shopGoodsIdKey)
	return err
}

func (m *defaultGoodsModel) TransUpdate(ctx context.Context, session sqlx.Session, data *Goods) error {
	shopGoodsIdKey := fmt.Sprintf("%s%v", cacheShopGoodsIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, goodsRowsWithPlaceHolder)
		return session.ExecCtx(ctx, query, data.CategoryName, data.CouponRemainQuantity, data.ExtraCouponAmount, data.PromotionRate, data.SubsidyGoodsType, data.CatIds, data.CouponMinOrderAmount, data.CategoryId, data.MallId, data.SubsidyAmount, data.CouponPrice, data.MallName, data.CouponTotalQuantity, data.MarketFee, data.MerchantType, data.LgstTxt, data.GoodsName, data.SalesTip, data.HasMaterial, data.GoodsId, data.PredictPromotionRate, data.DescTxt, data.GoodsDesc, data.OptName, data.RealtimeSalesTip, data.ShareRate, data.GoodsThumbnailUrl, data.OptIds, data.OptId, data.SearchId, data.GoodsImageUrl, data.ActivityTags, data.HasCoupon, data.MinNormalPrice, data.ServTxt, data.UnifiedTags, data.CouponStartTime, data.MinGroupPrice, data.CouponDiscount, data.GoodsSign, data.CouponEndTime, data.Id)
	}, shopGoodsIdKey)
	return err
}

func (m *defaultGoodsModel) List(ctx context.Context, page, limit int64) ([]*Goods, error) {
	query := fmt.Sprintf("select %s from %s limit ?,?", goodsRows, m.table)
	var resp []*Goods
	//err := m.conn.QueryRowsCtx(ctx, &resp, query, (page-1)*limit, limit)
	err := m.QueryRowsNoCacheCtx(ctx, &resp, query, (page-1)*limit, limit)
	return resp, err
}

func (m *defaultGoodsModel) Trans(ctx context.Context, fn func(ctx context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, func(ctx context.Context, session sqlx.Session) error {
		return fn(ctx, session)
	})
}

func (m *defaultGoodsModel) FindSum(ctx context.Context, builder squirrel.SelectBuilder, field string) (float64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindSum Least One Field"), "FindSum Least One Field")
	}

	builder = builder.Columns("IFNULL(SUM(" + field + "),0)")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp float64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultGoodsModel) FindCount(ctx context.Context, builder squirrel.SelectBuilder, field string) (int64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindCount Least One Field"), "FindCount Least One Field")
	}

	builder = builder.Columns("COUNT(" + field + ")")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultGoodsModel) FindAll(ctx context.Context, builder squirrel.SelectBuilder, orderBy string) ([]*Goods, error) {

	builder = builder.Columns(goodsRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*Goods
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultGoodsModel) FindPageListByPage(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*Goods, error) {

	builder = builder.Columns(goodsRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*Goods
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultGoodsModel) FindPageListByPageWithTotal(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*Goods, int64, error) {

	total, err := m.FindCount(ctx, builder, "id")
	if err != nil {
		return nil, 0, err
	}

	builder = builder.Columns(goodsRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, total, err
	}

	var resp []*Goods
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, total, nil
	default:
		return nil, total, err
	}
}

func (m *defaultGoodsModel) FindPageListByIdDESC(ctx context.Context, builder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*Goods, error) {

	builder = builder.Columns(goodsRows)

	if preMinId > 0 {
		builder = builder.Where(" id < ? ", preMinId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id DESC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*Goods
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultGoodsModel) FindPageListByIdASC(ctx context.Context, builder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*Goods, error) {

	builder = builder.Columns(goodsRows)

	if preMaxId > 0 {
		builder = builder.Where(" id > ? ", preMaxId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id ASC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*Goods
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultGoodsModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select().From(m.table)
}

func (m *defaultGoodsModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheShopGoodsIdPrefix, primary)
}

func (m *defaultGoodsModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", goodsRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultGoodsModel) tableName() string {
	return m.table
}
