package logic

import (
	"context"
	"github.com/jinzhu/copier"

	"looklook/app/shop/cmd/rpc/internal/svc"
	"looklook/app/shop/cmd/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetOrderListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetOrderListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetOrderListLogic {
	return &GetOrderListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetOrderListLogic) GetOrderList(in *pb.OrderListReq) (*pb.OrderListResp, error) {
	// todo: add your logic here and delete this line
	list, err := l.svcCtx.Order.GetOrderListByUId(in.UserId, in.Page, in.PageSize)
	if err != nil {
		return nil, err
	}

	orders := make([]*pb.Order, 0)

	for _, item := range list {
		pbOrders := new(pb.Order)
		_ = copier.Copy(pbOrders, item)
		orders = append(orders, pbOrders)
	}
	return &pb.OrderListResp{
		Total: int64(len(list)),
		List:  orders,
	}, nil
}
