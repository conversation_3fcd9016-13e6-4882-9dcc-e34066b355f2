// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v4.25.2
// source: notice.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NoticeLotteryDrawReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserIds   []int64 `protobuf:"varint,1,rep,packed,name=userIds,proto3" json:"userIds,omitempty"`
	LotteryId int64   `protobuf:"varint,2,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"`
}

func (x *NoticeLotteryDrawReq) Reset() {
	*x = NoticeLotteryDrawReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_notice_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NoticeLotteryDrawReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoticeLotteryDrawReq) ProtoMessage() {}

func (x *NoticeLotteryDrawReq) ProtoReflect() protoreflect.Message {
	mi := &file_notice_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoticeLotteryDrawReq.ProtoReflect.Descriptor instead.
func (*NoticeLotteryDrawReq) Descriptor() ([]byte, []int) {
	return file_notice_proto_rawDescGZIP(), []int{0}
}

func (x *NoticeLotteryDrawReq) GetUserIds() []int64 {
	if x != nil {
		return x.UserIds
	}
	return nil
}

func (x *NoticeLotteryDrawReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

type NoticeLotteryDrawResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *NoticeLotteryDrawResp) Reset() {
	*x = NoticeLotteryDrawResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_notice_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NoticeLotteryDrawResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoticeLotteryDrawResp) ProtoMessage() {}

func (x *NoticeLotteryDrawResp) ProtoReflect() protoreflect.Message {
	mi := &file_notice_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoticeLotteryDrawResp.ProtoReflect.Descriptor instead.
func (*NoticeLotteryDrawResp) Descriptor() ([]byte, []int) {
	return file_notice_proto_rawDescGZIP(), []int{1}
}

type NoticeSubscribePreference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Openid      string `protobuf:"bytes,2,opt,name=openid,proto3" json:"openid,omitempty"`
	TemplateId  string `protobuf:"bytes,3,opt,name=templateId,proto3" json:"templateId,omitempty"`
	AcceptCount int64  `protobuf:"varint,4,opt,name=acceptCount,proto3" json:"acceptCount,omitempty"`
	CreateTime  int64  `protobuf:"varint,5,opt,name=createTime,proto3" json:"createTime,omitempty"`
	UpdateTime  int64  `protobuf:"varint,6,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
}

func (x *NoticeSubscribePreference) Reset() {
	*x = NoticeSubscribePreference{}
	if protoimpl.UnsafeEnabled {
		mi := &file_notice_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NoticeSubscribePreference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoticeSubscribePreference) ProtoMessage() {}

func (x *NoticeSubscribePreference) ProtoReflect() protoreflect.Message {
	mi := &file_notice_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoticeSubscribePreference.ProtoReflect.Descriptor instead.
func (*NoticeSubscribePreference) Descriptor() ([]byte, []int) {
	return file_notice_proto_rawDescGZIP(), []int{2}
}

func (x *NoticeSubscribePreference) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *NoticeSubscribePreference) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

func (x *NoticeSubscribePreference) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *NoticeSubscribePreference) GetAcceptCount() int64 {
	if x != nil {
		return x.AcceptCount
	}
	return 0
}

func (x *NoticeSubscribePreference) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *NoticeSubscribePreference) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

type SaveNoticeSubscribePreferenceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Openid     string `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`
	TemplateId string `protobuf:"bytes,2,opt,name=templateId,proto3" json:"templateId,omitempty"`
	Type       int64  `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *SaveNoticeSubscribePreferenceReq) Reset() {
	*x = SaveNoticeSubscribePreferenceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_notice_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveNoticeSubscribePreferenceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveNoticeSubscribePreferenceReq) ProtoMessage() {}

func (x *SaveNoticeSubscribePreferenceReq) ProtoReflect() protoreflect.Message {
	mi := &file_notice_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveNoticeSubscribePreferenceReq.ProtoReflect.Descriptor instead.
func (*SaveNoticeSubscribePreferenceReq) Descriptor() ([]byte, []int) {
	return file_notice_proto_rawDescGZIP(), []int{3}
}

func (x *SaveNoticeSubscribePreferenceReq) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

func (x *SaveNoticeSubscribePreferenceReq) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *SaveNoticeSubscribePreferenceReq) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

type SaveNoticeSubscribePreferenceResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SaveNoticeSubscribePreferenceResp) Reset() {
	*x = SaveNoticeSubscribePreferenceResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_notice_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveNoticeSubscribePreferenceResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveNoticeSubscribePreferenceResp) ProtoMessage() {}

func (x *SaveNoticeSubscribePreferenceResp) ProtoReflect() protoreflect.Message {
	mi := &file_notice_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveNoticeSubscribePreferenceResp.ProtoReflect.Descriptor instead.
func (*SaveNoticeSubscribePreferenceResp) Descriptor() ([]byte, []int) {
	return file_notice_proto_rawDescGZIP(), []int{4}
}

type GetNoticeSubscribePreferenceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Openid     string `protobuf:"bytes,1,opt,name=openid,proto3" json:"openid,omitempty"`
	TemplateId string `protobuf:"bytes,2,opt,name=templateId,proto3" json:"templateId,omitempty"`
}

func (x *GetNoticeSubscribePreferenceReq) Reset() {
	*x = GetNoticeSubscribePreferenceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_notice_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNoticeSubscribePreferenceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNoticeSubscribePreferenceReq) ProtoMessage() {}

func (x *GetNoticeSubscribePreferenceReq) ProtoReflect() protoreflect.Message {
	mi := &file_notice_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNoticeSubscribePreferenceReq.ProtoReflect.Descriptor instead.
func (*GetNoticeSubscribePreferenceReq) Descriptor() ([]byte, []int) {
	return file_notice_proto_rawDescGZIP(), []int{5}
}

func (x *GetNoticeSubscribePreferenceReq) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

func (x *GetNoticeSubscribePreferenceReq) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

type GetNoticeSubscribePreferenceResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Openid      string `protobuf:"bytes,2,opt,name=openid,proto3" json:"openid,omitempty"`
	TemplateId  string `protobuf:"bytes,3,opt,name=templateId,proto3" json:"templateId,omitempty"`
	AcceptCount int64  `protobuf:"varint,4,opt,name=acceptCount,proto3" json:"acceptCount,omitempty"`
}

func (x *GetNoticeSubscribePreferenceResp) Reset() {
	*x = GetNoticeSubscribePreferenceResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_notice_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNoticeSubscribePreferenceResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNoticeSubscribePreferenceResp) ProtoMessage() {}

func (x *GetNoticeSubscribePreferenceResp) ProtoReflect() protoreflect.Message {
	mi := &file_notice_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNoticeSubscribePreferenceResp.ProtoReflect.Descriptor instead.
func (*GetNoticeSubscribePreferenceResp) Descriptor() ([]byte, []int) {
	return file_notice_proto_rawDescGZIP(), []int{6}
}

func (x *GetNoticeSubscribePreferenceResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetNoticeSubscribePreferenceResp) GetOpenid() string {
	if x != nil {
		return x.Openid
	}
	return ""
}

func (x *GetNoticeSubscribePreferenceResp) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *GetNoticeSubscribePreferenceResp) GetAcceptCount() int64 {
	if x != nil {
		return x.AcceptCount
	}
	return 0
}

type NoticeLotteryStartReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    int64 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`       //用户
	LotteryId int64 `protobuf:"varint,2,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"` //抽奖信息
	StartTime int64 `protobuf:"varint,3,opt,name=startTime,proto3" json:"startTime,omitempty"` //开始时间(时间戳)
}

func (x *NoticeLotteryStartReq) Reset() {
	*x = NoticeLotteryStartReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_notice_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NoticeLotteryStartReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoticeLotteryStartReq) ProtoMessage() {}

func (x *NoticeLotteryStartReq) ProtoReflect() protoreflect.Message {
	mi := &file_notice_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoticeLotteryStartReq.ProtoReflect.Descriptor instead.
func (*NoticeLotteryStartReq) Descriptor() ([]byte, []int) {
	return file_notice_proto_rawDescGZIP(), []int{7}
}

func (x *NoticeLotteryStartReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *NoticeLotteryStartReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

func (x *NoticeLotteryStartReq) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

type NoticeLotteryStartResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsSuccess bool `protobuf:"varint,1,opt,name=isSuccess,proto3" json:"isSuccess,omitempty"` //是否订阅成功
}

func (x *NoticeLotteryStartResp) Reset() {
	*x = NoticeLotteryStartResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_notice_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NoticeLotteryStartResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoticeLotteryStartResp) ProtoMessage() {}

func (x *NoticeLotteryStartResp) ProtoReflect() protoreflect.Message {
	mi := &file_notice_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoticeLotteryStartResp.ProtoReflect.Descriptor instead.
func (*NoticeLotteryStartResp) Descriptor() ([]byte, []int) {
	return file_notice_proto_rawDescGZIP(), []int{8}
}

func (x *NoticeLotteryStartResp) GetIsSuccess() bool {
	if x != nil {
		return x.IsSuccess
	}
	return false
}

type NoticeWishSignInReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId     int64 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`         //用户
	Reward     int64 `protobuf:"varint,2,opt,name=reward,proto3" json:"reward,omitempty"`         //心愿值奖励
	Accumulate int64 `protobuf:"varint,3,opt,name=accumulate,proto3" json:"accumulate,omitempty"` //累计次数
}

func (x *NoticeWishSignInReq) Reset() {
	*x = NoticeWishSignInReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_notice_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NoticeWishSignInReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoticeWishSignInReq) ProtoMessage() {}

func (x *NoticeWishSignInReq) ProtoReflect() protoreflect.Message {
	mi := &file_notice_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoticeWishSignInReq.ProtoReflect.Descriptor instead.
func (*NoticeWishSignInReq) Descriptor() ([]byte, []int) {
	return file_notice_proto_rawDescGZIP(), []int{9}
}

func (x *NoticeWishSignInReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *NoticeWishSignInReq) GetReward() int64 {
	if x != nil {
		return x.Reward
	}
	return 0
}

func (x *NoticeWishSignInReq) GetAccumulate() int64 {
	if x != nil {
		return x.Accumulate
	}
	return 0
}

type NoticeWishSignInResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *NoticeWishSignInResp) Reset() {
	*x = NoticeWishSignInResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_notice_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NoticeWishSignInResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoticeWishSignInResp) ProtoMessage() {}

func (x *NoticeWishSignInResp) ProtoReflect() protoreflect.Message {
	mi := &file_notice_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoticeWishSignInResp.ProtoReflect.Descriptor instead.
func (*NoticeWishSignInResp) Descriptor() ([]byte, []int) {
	return file_notice_proto_rawDescGZIP(), []int{10}
}

var File_notice_proto protoreflect.FileDescriptor

var file_notice_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x02,
	0x70, 0x62, 0x22, 0x4e, 0x0a, 0x14, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x4c, 0x6f, 0x74, 0x74,
	0x65, 0x72, 0x79, 0x44, 0x72, 0x61, 0x77, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x07, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79,
	0x49, 0x64, 0x22, 0x17, 0x0a, 0x15, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x4c, 0x6f, 0x74, 0x74,
	0x65, 0x72, 0x79, 0x44, 0x72, 0x61, 0x77, 0x52, 0x65, 0x73, 0x70, 0x22, 0xc5, 0x01, 0x0a, 0x19,
	0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69,
	0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49,
	0x64, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x22, 0x6e, 0x0a, 0x20, 0x53, 0x61, 0x76, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x63,
	0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x22, 0x23, 0x0a, 0x21, 0x53, 0x61, 0x76, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x63,
	0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x59, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x4e,
	0x6f, 0x74, 0x69, 0x63, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x70, 0x65, 0x6e, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65,
	0x6e, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x49, 0x64, 0x22, 0x8c, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x63,
	0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x69, 0x64,
	0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64,
	0x12, 0x20, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x22, 0x6b, 0x0a, 0x15, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x4c, 0x6f, 0x74, 0x74,
	0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x22,
	0x36, 0x0a, 0x16, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x53,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73,
	0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x65, 0x0a, 0x13, 0x4e, 0x6f, 0x74, 0x69, 0x63,
	0x65, 0x57, 0x69, 0x73, 0x68, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x16,
	0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x1e,
	0x0a, 0x0a, 0x61, 0x63, 0x63, 0x75, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x75, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x22, 0x16,
	0x0a, 0x14, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x57, 0x69, 0x73, 0x68, 0x53, 0x69, 0x67, 0x6e,
	0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x32, 0xbd, 0x03, 0x0a, 0x06, 0x6e, 0x6f, 0x74, 0x69, 0x63,
	0x65, 0x12, 0x48, 0x0a, 0x11, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x4c, 0x6f, 0x74, 0x74, 0x65,
	0x72, 0x79, 0x44, 0x72, 0x61, 0x77, 0x12, 0x18, 0x2e, 0x70, 0x62, 0x2e, 0x4e, 0x6f, 0x74, 0x69,
	0x63, 0x65, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x44, 0x72, 0x61, 0x77, 0x52, 0x65, 0x71,
	0x1a, 0x19, 0x2e, 0x70, 0x62, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x4c, 0x6f, 0x74, 0x74,
	0x65, 0x72, 0x79, 0x44, 0x72, 0x61, 0x77, 0x52, 0x65, 0x73, 0x70, 0x12, 0x69, 0x0a, 0x1c, 0x47,
	0x65, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x23, 0x2e, 0x70, 0x62,
	0x2e, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x1a, 0x24, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x6c, 0x0a, 0x1d, 0x53, 0x61, 0x76, 0x65, 0x4e, 0x6f,
	0x74, 0x69, 0x63, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x24, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x61, 0x76,
	0x65, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e,
	0x70, 0x62, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x53, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x4b, 0x0a, 0x12, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x4c, 0x6f,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x19, 0x2e, 0x70, 0x62, 0x2e,
	0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x70, 0x62, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x63,
	0x65, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x43, 0x0a, 0x0e, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x57, 0x69, 0x73, 0x68, 0x53,
	0x69, 0x67, 0x6e, 0x12, 0x17, 0x2e, 0x70, 0x62, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x57,
	0x69, 0x73, 0x68, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x70,
	0x62, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x57, 0x69, 0x73, 0x68, 0x53, 0x69, 0x67, 0x6e,
	0x49, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_notice_proto_rawDescOnce sync.Once
	file_notice_proto_rawDescData = file_notice_proto_rawDesc
)

func file_notice_proto_rawDescGZIP() []byte {
	file_notice_proto_rawDescOnce.Do(func() {
		file_notice_proto_rawDescData = protoimpl.X.CompressGZIP(file_notice_proto_rawDescData)
	})
	return file_notice_proto_rawDescData
}

var file_notice_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_notice_proto_goTypes = []interface{}{
	(*NoticeLotteryDrawReq)(nil),              // 0: pb.NoticeLotteryDrawReq
	(*NoticeLotteryDrawResp)(nil),             // 1: pb.NoticeLotteryDrawResp
	(*NoticeSubscribePreference)(nil),         // 2: pb.NoticeSubscribePreference
	(*SaveNoticeSubscribePreferenceReq)(nil),  // 3: pb.SaveNoticeSubscribePreferenceReq
	(*SaveNoticeSubscribePreferenceResp)(nil), // 4: pb.SaveNoticeSubscribePreferenceResp
	(*GetNoticeSubscribePreferenceReq)(nil),   // 5: pb.GetNoticeSubscribePreferenceReq
	(*GetNoticeSubscribePreferenceResp)(nil),  // 6: pb.GetNoticeSubscribePreferenceResp
	(*NoticeLotteryStartReq)(nil),             // 7: pb.NoticeLotteryStartReq
	(*NoticeLotteryStartResp)(nil),            // 8: pb.NoticeLotteryStartResp
	(*NoticeWishSignInReq)(nil),               // 9: pb.NoticeWishSignInReq
	(*NoticeWishSignInResp)(nil),              // 10: pb.NoticeWishSignInResp
}
var file_notice_proto_depIdxs = []int32{
	0,  // 0: pb.notice.NoticeLotteryDraw:input_type -> pb.NoticeLotteryDrawReq
	5,  // 1: pb.notice.GetNoticeSubscribePreference:input_type -> pb.GetNoticeSubscribePreferenceReq
	3,  // 2: pb.notice.SaveNoticeSubscribePreference:input_type -> pb.SaveNoticeSubscribePreferenceReq
	7,  // 3: pb.notice.NoticeLotteryStart:input_type -> pb.NoticeLotteryStartReq
	9,  // 4: pb.notice.NoticeWishSign:input_type -> pb.NoticeWishSignInReq
	1,  // 5: pb.notice.NoticeLotteryDraw:output_type -> pb.NoticeLotteryDrawResp
	6,  // 6: pb.notice.GetNoticeSubscribePreference:output_type -> pb.GetNoticeSubscribePreferenceResp
	4,  // 7: pb.notice.SaveNoticeSubscribePreference:output_type -> pb.SaveNoticeSubscribePreferenceResp
	8,  // 8: pb.notice.NoticeLotteryStart:output_type -> pb.NoticeLotteryStartResp
	10, // 9: pb.notice.NoticeWishSign:output_type -> pb.NoticeWishSignInResp
	5,  // [5:10] is the sub-list for method output_type
	0,  // [0:5] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_notice_proto_init() }
func file_notice_proto_init() {
	if File_notice_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_notice_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NoticeLotteryDrawReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_notice_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NoticeLotteryDrawResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_notice_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NoticeSubscribePreference); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_notice_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveNoticeSubscribePreferenceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_notice_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveNoticeSubscribePreferenceResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_notice_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNoticeSubscribePreferenceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_notice_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNoticeSubscribePreferenceResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_notice_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NoticeLotteryStartReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_notice_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NoticeLotteryStartResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_notice_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NoticeWishSignInReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_notice_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NoticeWishSignInResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_notice_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_notice_proto_goTypes,
		DependencyIndexes: file_notice_proto_depIdxs,
		MessageInfos:      file_notice_proto_msgTypes,
	}.Build()
	File_notice_proto = out.File
	file_notice_proto_rawDesc = nil
	file_notice_proto_goTypes = nil
	file_notice_proto_depIdxs = nil
}
