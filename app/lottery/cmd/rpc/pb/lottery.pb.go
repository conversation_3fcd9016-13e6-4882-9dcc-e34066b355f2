// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.19.4
// source: lottery.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// --------------------------------抽奖表--------------------------------
type Lottery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
	UserId        int64  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`
	Name          string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Thumb         string `protobuf:"bytes,4,opt,name=thumb,proto3" json:"thumb,omitempty"`
	PublishTime   int64  `protobuf:"varint,5,opt,name=publishTime,proto3" json:"publishTime,omitempty"`
	JoinNumber    int64  `protobuf:"varint,6,opt,name=joinNumber,proto3" json:"joinNumber,omitempty"`
	Introduce     string `protobuf:"bytes,7,opt,name=introduce,proto3" json:"introduce,omitempty"`
	AwardDeadline int64  `protobuf:"varint,8,opt,name=awardDeadline,proto3" json:"awardDeadline,omitempty"`
	IsSelected    int64  `protobuf:"varint,9,opt,name=isSelected,proto3" json:"isSelected,omitempty"`
	AnnounceType  int64  `protobuf:"varint,10,opt,name=announceType,proto3" json:"announceType,omitempty"`
	AnnounceTime  int64  `protobuf:"varint,11,opt,name=announceTime,proto3" json:"announceTime,omitempty"`
	IsAnnounced   int64  `protobuf:"varint,12,opt,name=isAnnounced,proto3" json:"isAnnounced,omitempty"`
	CreateTime    int64  `protobuf:"varint,13,opt,name=createTime,proto3" json:"createTime,omitempty"`
	UpdateTime    int64  `protobuf:"varint,14,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
	SponsorId     int64  `protobuf:"varint,15,opt,name=SponsorId,proto3" json:"SponsorId,omitempty"`
}

func (x *Lottery) Reset() {
	*x = Lottery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Lottery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Lottery) ProtoMessage() {}

func (x *Lottery) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Lottery.ProtoReflect.Descriptor instead.
func (*Lottery) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{0}
}

func (x *Lottery) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Lottery) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *Lottery) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Lottery) GetThumb() string {
	if x != nil {
		return x.Thumb
	}
	return ""
}

func (x *Lottery) GetPublishTime() int64 {
	if x != nil {
		return x.PublishTime
	}
	return 0
}

func (x *Lottery) GetJoinNumber() int64 {
	if x != nil {
		return x.JoinNumber
	}
	return 0
}

func (x *Lottery) GetIntroduce() string {
	if x != nil {
		return x.Introduce
	}
	return ""
}

func (x *Lottery) GetAwardDeadline() int64 {
	if x != nil {
		return x.AwardDeadline
	}
	return 0
}

func (x *Lottery) GetIsSelected() int64 {
	if x != nil {
		return x.IsSelected
	}
	return 0
}

func (x *Lottery) GetAnnounceType() int64 {
	if x != nil {
		return x.AnnounceType
	}
	return 0
}

func (x *Lottery) GetAnnounceTime() int64 {
	if x != nil {
		return x.AnnounceTime
	}
	return 0
}

func (x *Lottery) GetIsAnnounced() int64 {
	if x != nil {
		return x.IsAnnounced
	}
	return 0
}

func (x *Lottery) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *Lottery) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *Lottery) GetSponsorId() int64 {
	if x != nil {
		return x.SponsorId
	}
	return 0
}

type AddLotteryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId        int64      `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"` //发起抽奖用户ID
	Name          string     `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`      //默认取一等奖名称
	Thumb         string     `protobuf:"bytes,3,opt,name=thumb,proto3" json:"thumb,omitempty"`    //默认取一等经配图
	JoinNumber    int64      `protobuf:"varint,4,opt,name=joinNumber,proto3" json:"joinNumber,omitempty"`
	Introduce     string     `protobuf:"bytes,5,opt,name=introduce,proto3" json:"introduce,omitempty"`
	AwardDeadline int64      `protobuf:"varint,6,opt,name=awardDeadline,proto3" json:"awardDeadline,omitempty"`
	AnnounceType  int64      `protobuf:"varint,8,opt,name=announceType,proto3" json:"announceType,omitempty"`
	Prizes        []*Prize   `protobuf:"bytes,9,rep,name=prizes,proto3" json:"prizes,omitempty"` //奖品 支持多个
	AnnounceTime  int64      `protobuf:"varint,10,opt,name=announceTime,proto3" json:"announceTime,omitempty"`
	SponsorId     int64      `protobuf:"varint,11,opt,name=SponsorId,proto3" json:"SponsorId,omitempty"`
	IsClocked     int64      `protobuf:"varint,12,opt,name=isClocked,proto3" json:"isClocked,omitempty"`     // 是否开启打卡任务
	ClockTask     *ClockTask `protobuf:"bytes,13,opt,name=clockTask,proto3" json:"clockTask,omitempty"`      // 打卡任务 最多支持一个
	PublishType   int64      `protobuf:"varint,14,opt,name=publishType,proto3" json:"publishType,omitempty"` // 发布类型 1发布测试 2发布正式
}

func (x *AddLotteryReq) Reset() {
	*x = AddLotteryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddLotteryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddLotteryReq) ProtoMessage() {}

func (x *AddLotteryReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddLotteryReq.ProtoReflect.Descriptor instead.
func (*AddLotteryReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{1}
}

func (x *AddLotteryReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AddLotteryReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AddLotteryReq) GetThumb() string {
	if x != nil {
		return x.Thumb
	}
	return ""
}

func (x *AddLotteryReq) GetJoinNumber() int64 {
	if x != nil {
		return x.JoinNumber
	}
	return 0
}

func (x *AddLotteryReq) GetIntroduce() string {
	if x != nil {
		return x.Introduce
	}
	return ""
}

func (x *AddLotteryReq) GetAwardDeadline() int64 {
	if x != nil {
		return x.AwardDeadline
	}
	return 0
}

func (x *AddLotteryReq) GetAnnounceType() int64 {
	if x != nil {
		return x.AnnounceType
	}
	return 0
}

func (x *AddLotteryReq) GetPrizes() []*Prize {
	if x != nil {
		return x.Prizes
	}
	return nil
}

func (x *AddLotteryReq) GetAnnounceTime() int64 {
	if x != nil {
		return x.AnnounceTime
	}
	return 0
}

func (x *AddLotteryReq) GetSponsorId() int64 {
	if x != nil {
		return x.SponsorId
	}
	return 0
}

func (x *AddLotteryReq) GetIsClocked() int64 {
	if x != nil {
		return x.IsClocked
	}
	return 0
}

func (x *AddLotteryReq) GetClockTask() *ClockTask {
	if x != nil {
		return x.ClockTask
	}
	return nil
}

func (x *AddLotteryReq) GetPublishType() int64 {
	if x != nil {
		return x.PublishType
	}
	return 0
}

type AddLotteryResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //抽奖ID
}

func (x *AddLotteryResp) Reset() {
	*x = AddLotteryResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddLotteryResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddLotteryResp) ProtoMessage() {}

func (x *AddLotteryResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddLotteryResp.ProtoReflect.Descriptor instead.
func (*AddLotteryResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{2}
}

func (x *AddLotteryResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdateLotteryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                       //id
	UserId        int64  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`               //发起抽奖用户ID
	Name          string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                    //默认取一等奖名称
	Thumb         string `protobuf:"bytes,4,opt,name=thumb,proto3" json:"thumb,omitempty"`                  //默认取一等经配图
	PublishTime   int64  `protobuf:"varint,5,opt,name=publishTime,proto3" json:"publishTime,omitempty"`     //开奖时间
	JoinNumber    int64  `protobuf:"varint,6,opt,name=joinNumber,proto3" json:"joinNumber,omitempty"`       //自动开奖人数
	Introduce     string `protobuf:"bytes,7,opt,name=introduce,proto3" json:"introduce,omitempty"`          //抽奖说明
	AwardDeadline int64  `protobuf:"varint,8,opt,name=awardDeadline,proto3" json:"awardDeadline,omitempty"` //领奖截止时间
	IsSelected    int64  `protobuf:"varint,9,opt,name=isSelected,proto3" json:"isSelected,omitempty"`       //是否精选 1是 0否
	AnnounceType  int64  `protobuf:"varint,10,opt,name=announceType,proto3" json:"announceType,omitempty"`
	AnnounceTime  int64  `protobuf:"varint,11,opt,name=announceTime,proto3" json:"announceTime,omitempty"`
	IsAnnounced   int64  `protobuf:"varint,12,opt,name=isAnnounced,proto3" json:"isAnnounced,omitempty"`
	SponsorId     int64  `protobuf:"varint,13,opt,name=SponsorId,proto3" json:"SponsorId,omitempty"`
}

func (x *UpdateLotteryReq) Reset() {
	*x = UpdateLotteryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLotteryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLotteryReq) ProtoMessage() {}

func (x *UpdateLotteryReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLotteryReq.ProtoReflect.Descriptor instead.
func (*UpdateLotteryReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateLotteryReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateLotteryReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UpdateLotteryReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateLotteryReq) GetThumb() string {
	if x != nil {
		return x.Thumb
	}
	return ""
}

func (x *UpdateLotteryReq) GetPublishTime() int64 {
	if x != nil {
		return x.PublishTime
	}
	return 0
}

func (x *UpdateLotteryReq) GetJoinNumber() int64 {
	if x != nil {
		return x.JoinNumber
	}
	return 0
}

func (x *UpdateLotteryReq) GetIntroduce() string {
	if x != nil {
		return x.Introduce
	}
	return ""
}

func (x *UpdateLotteryReq) GetAwardDeadline() int64 {
	if x != nil {
		return x.AwardDeadline
	}
	return 0
}

func (x *UpdateLotteryReq) GetIsSelected() int64 {
	if x != nil {
		return x.IsSelected
	}
	return 0
}

func (x *UpdateLotteryReq) GetAnnounceType() int64 {
	if x != nil {
		return x.AnnounceType
	}
	return 0
}

func (x *UpdateLotteryReq) GetAnnounceTime() int64 {
	if x != nil {
		return x.AnnounceTime
	}
	return 0
}

func (x *UpdateLotteryReq) GetIsAnnounced() int64 {
	if x != nil {
		return x.IsAnnounced
	}
	return 0
}

func (x *UpdateLotteryReq) GetSponsorId() int64 {
	if x != nil {
		return x.SponsorId
	}
	return 0
}

type UpdateLotteryResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateLotteryResp) Reset() {
	*x = UpdateLotteryResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLotteryResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLotteryResp) ProtoMessage() {}

func (x *UpdateLotteryResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLotteryResp.ProtoReflect.Descriptor instead.
func (*UpdateLotteryResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{4}
}

type DelLotteryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *DelLotteryReq) Reset() {
	*x = DelLotteryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelLotteryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelLotteryReq) ProtoMessage() {}

func (x *DelLotteryReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelLotteryReq.ProtoReflect.Descriptor instead.
func (*DelLotteryReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{5}
}

func (x *DelLotteryReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DelLotteryResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DelLotteryResp) Reset() {
	*x = DelLotteryResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelLotteryResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelLotteryResp) ProtoMessage() {}

func (x *DelLotteryResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelLotteryResp.ProtoReflect.Descriptor instead.
func (*DelLotteryResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{6}
}

type GetLotteryByIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *GetLotteryByIdReq) Reset() {
	*x = GetLotteryByIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotteryByIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotteryByIdReq) ProtoMessage() {}

func (x *GetLotteryByIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotteryByIdReq.ProtoReflect.Descriptor instead.
func (*GetLotteryByIdReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{7}
}

func (x *GetLotteryByIdReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetLotteryByIdResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lottery *Lottery `protobuf:"bytes,1,opt,name=lottery,proto3" json:"lottery,omitempty"` //lottery
}

func (x *GetLotteryByIdResp) Reset() {
	*x = GetLotteryByIdResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotteryByIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotteryByIdResp) ProtoMessage() {}

func (x *GetLotteryByIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotteryByIdResp.ProtoReflect.Descriptor instead.
func (*GetLotteryByIdResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{8}
}

func (x *GetLotteryByIdResp) GetLottery() *Lottery {
	if x != nil {
		return x.Lottery
	}
	return nil
}

type SearchLotteryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page          int64  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`                    //page
	Limit         int64  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`                  //limit
	Id            int64  `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`                        //id
	UserId        int64  `protobuf:"varint,4,opt,name=userId,proto3" json:"userId,omitempty"`                //发起抽奖用户ID
	Name          string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`                     //默认取一等奖名称
	Thumb         string `protobuf:"bytes,6,opt,name=thumb,proto3" json:"thumb,omitempty"`                   //默认取一等经配图
	PublishTime   int64  `protobuf:"varint,7,opt,name=publishTime,proto3" json:"publishTime,omitempty"`      // 发布抽奖时间
	JoinNumber    int64  `protobuf:"varint,8,opt,name=joinNumber,proto3" json:"joinNumber,omitempty"`        //自动开奖人数
	Introduce     string `protobuf:"bytes,9,opt,name=introduce,proto3" json:"introduce,omitempty"`           //抽奖说明
	AwardDeadline int64  `protobuf:"varint,10,opt,name=awardDeadline,proto3" json:"awardDeadline,omitempty"` //领奖截止时间
	CreateTime    int64  `protobuf:"varint,11,opt,name=createTime,proto3" json:"createTime,omitempty"`       //createTime
	UpdateTime    int64  `protobuf:"varint,12,opt,name=updateTime,proto3" json:"updateTime,omitempty"`       //updateTime
	IsSelected    int64  `protobuf:"varint,13,opt,name=isSelected,proto3" json:"isSelected,omitempty"`       //是否精选 1是 0否
	LastId        int64  `protobuf:"varint,14,opt,name=lastId,proto3" json:"lastId,omitempty"`               // 页面最后一条记录的id
	AnnounceType  int64  `protobuf:"varint,15,opt,name=announceType,proto3" json:"announceType,omitempty"`
	AnnounceTime  int64  `protobuf:"varint,16,opt,name=announceTime,proto3" json:"announceTime,omitempty"`
	IsAnnounced   int64  `protobuf:"varint,17,opt,name=isAnnounced,proto3" json:"isAnnounced,omitempty"`
	SponsorId     int64  `protobuf:"varint,18,opt,name=SponsorId,proto3" json:"SponsorId,omitempty"` // 赞助商id
}

func (x *SearchLotteryReq) Reset() {
	*x = SearchLotteryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchLotteryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchLotteryReq) ProtoMessage() {}

func (x *SearchLotteryReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchLotteryReq.ProtoReflect.Descriptor instead.
func (*SearchLotteryReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{9}
}

func (x *SearchLotteryReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchLotteryReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *SearchLotteryReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SearchLotteryReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *SearchLotteryReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchLotteryReq) GetThumb() string {
	if x != nil {
		return x.Thumb
	}
	return ""
}

func (x *SearchLotteryReq) GetPublishTime() int64 {
	if x != nil {
		return x.PublishTime
	}
	return 0
}

func (x *SearchLotteryReq) GetJoinNumber() int64 {
	if x != nil {
		return x.JoinNumber
	}
	return 0
}

func (x *SearchLotteryReq) GetIntroduce() string {
	if x != nil {
		return x.Introduce
	}
	return ""
}

func (x *SearchLotteryReq) GetAwardDeadline() int64 {
	if x != nil {
		return x.AwardDeadline
	}
	return 0
}

func (x *SearchLotteryReq) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *SearchLotteryReq) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *SearchLotteryReq) GetIsSelected() int64 {
	if x != nil {
		return x.IsSelected
	}
	return 0
}

func (x *SearchLotteryReq) GetLastId() int64 {
	if x != nil {
		return x.LastId
	}
	return 0
}

func (x *SearchLotteryReq) GetAnnounceType() int64 {
	if x != nil {
		return x.AnnounceType
	}
	return 0
}

func (x *SearchLotteryReq) GetAnnounceTime() int64 {
	if x != nil {
		return x.AnnounceTime
	}
	return 0
}

func (x *SearchLotteryReq) GetIsAnnounced() int64 {
	if x != nil {
		return x.IsAnnounced
	}
	return 0
}

func (x *SearchLotteryReq) GetSponsorId() int64 {
	if x != nil {
		return x.SponsorId
	}
	return 0
}

type SearchLotteryResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lottery []*Lottery `protobuf:"bytes,1,rep,name=lottery,proto3" json:"lottery,omitempty"` //lottery
}

func (x *SearchLotteryResp) Reset() {
	*x = SearchLotteryResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchLotteryResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchLotteryResp) ProtoMessage() {}

func (x *SearchLotteryResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchLotteryResp.ProtoReflect.Descriptor instead.
func (*SearchLotteryResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{10}
}

func (x *SearchLotteryResp) GetLottery() []*Lottery {
	if x != nil {
		return x.Lottery
	}
	return nil
}

type SetIsSelectedLotteryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId int64 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"` // 要进行设置的管理员Id
	Id     int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`         // 抽奖id
}

func (x *SetIsSelectedLotteryReq) Reset() {
	*x = SetIsSelectedLotteryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetIsSelectedLotteryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetIsSelectedLotteryReq) ProtoMessage() {}

func (x *SetIsSelectedLotteryReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetIsSelectedLotteryReq.ProtoReflect.Descriptor instead.
func (*SetIsSelectedLotteryReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{11}
}

func (x *SetIsSelectedLotteryReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *SetIsSelectedLotteryReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type SetIsSelectedLotteryResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsSelected int64 `protobuf:"varint,1,opt,name=isSelected,proto3" json:"isSelected,omitempty"` //是否精选 1是 0否
}

func (x *SetIsSelectedLotteryResp) Reset() {
	*x = SetIsSelectedLotteryResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetIsSelectedLotteryResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetIsSelectedLotteryResp) ProtoMessage() {}

func (x *SetIsSelectedLotteryResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetIsSelectedLotteryResp.ProtoReflect.Descriptor instead.
func (*SetIsSelectedLotteryResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{12}
}

func (x *SetIsSelectedLotteryResp) GetIsSelected() int64 {
	if x != nil {
		return x.IsSelected
	}
	return 0
}

type LotteryDetailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`         // 抽奖id
	UserId int64 `protobuf:"varint,2,opt,name=UserId,proto3" json:"UserId,omitempty"` // 当前用户id
}

func (x *LotteryDetailReq) Reset() {
	*x = LotteryDetailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LotteryDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LotteryDetailReq) ProtoMessage() {}

func (x *LotteryDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LotteryDetailReq.ProtoReflect.Descriptor instead.
func (*LotteryDetailReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{13}
}

func (x *LotteryDetailReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LotteryDetailReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type LotteryDetailResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lottery        *Lottery `protobuf:"bytes,1,opt,name=lottery,proto3" json:"lottery,omitempty"`                // 抽奖信息
	Prizes         []*Prize `protobuf:"bytes,2,rep,name=prizes,proto3" json:"prizes,omitempty"`                  // 奖品列表
	IsParticipated int64    `protobuf:"varint,3,opt,name=isParticipated,proto3" json:"isParticipated,omitempty"` // 当前用户是否已经参与当前抽奖, 0否; 1是
}

func (x *LotteryDetailResp) Reset() {
	*x = LotteryDetailResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LotteryDetailResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LotteryDetailResp) ProtoMessage() {}

func (x *LotteryDetailResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LotteryDetailResp.ProtoReflect.Descriptor instead.
func (*LotteryDetailResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{14}
}

func (x *LotteryDetailResp) GetLottery() *Lottery {
	if x != nil {
		return x.Lottery
	}
	return nil
}

func (x *LotteryDetailResp) GetPrizes() []*Prize {
	if x != nil {
		return x.Prizes
	}
	return nil
}

func (x *LotteryDetailResp) GetIsParticipated() int64 {
	if x != nil {
		return x.IsParticipated
	}
	return 0
}

type LotterySponsorReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SponsorId int64 `protobuf:"varint,1,opt,name=SponsorId,proto3" json:"SponsorId,omitempty"` // 赞助商id
}

func (x *LotterySponsorReq) Reset() {
	*x = LotterySponsorReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LotterySponsorReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LotterySponsorReq) ProtoMessage() {}

func (x *LotterySponsorReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LotterySponsorReq.ProtoReflect.Descriptor instead.
func (*LotterySponsorReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{15}
}

func (x *LotterySponsorReq) GetSponsorId() int64 {
	if x != nil {
		return x.SponsorId
	}
	return 0
}

type LotterySponsorResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                 //id
	UserId     int64  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`         //userId
	Type       int64  `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`             //1微信号 2公众号 3小程序 4微信群 5视频号
	AppletType int64  `protobuf:"varint,4,opt,name=appletType,proto3" json:"appletType,omitempty"` //type=3时该字段才有意义，1小程序链接 2路径跳转 3二维码跳转
	Name       string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`              //名称
	Desc       string `protobuf:"bytes,6,opt,name=desc,proto3" json:"desc,omitempty"`              //描述
	Avatar     string `protobuf:"bytes,7,opt,name=avatar,proto3" json:"avatar,omitempty"`          //avatar
	IsShow     int64  `protobuf:"varint,8,opt,name=isShow,proto3" json:"isShow,omitempty"`         //1显示 2不显示
	QrCode     string `protobuf:"bytes,9,opt,name=qrCode,proto3" json:"qrCode,omitempty"`          //二维码图片地址, type=1 2 3&applet_type=3 4的时候启用
	InputA     string `protobuf:"bytes,10,opt,name=inputA,proto3" json:"inputA,omitempty"`         //type=5 applet_type=2 or applet_type=1 输入框A
	InputB     string `protobuf:"bytes,11,opt,name=inputB,proto3" json:"inputB,omitempty"`         //type=5 applet_type=2输入框B
}

func (x *LotterySponsorResp) Reset() {
	*x = LotterySponsorResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LotterySponsorResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LotterySponsorResp) ProtoMessage() {}

func (x *LotterySponsorResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LotterySponsorResp.ProtoReflect.Descriptor instead.
func (*LotterySponsorResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{16}
}

func (x *LotterySponsorResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LotterySponsorResp) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *LotterySponsorResp) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *LotterySponsorResp) GetAppletType() int64 {
	if x != nil {
		return x.AppletType
	}
	return 0
}

func (x *LotterySponsorResp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LotterySponsorResp) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *LotterySponsorResp) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *LotterySponsorResp) GetIsShow() int64 {
	if x != nil {
		return x.IsShow
	}
	return 0
}

func (x *LotterySponsorResp) GetQrCode() string {
	if x != nil {
		return x.QrCode
	}
	return ""
}

func (x *LotterySponsorResp) GetInputA() string {
	if x != nil {
		return x.InputA
	}
	return ""
}

func (x *LotterySponsorResp) GetInputB() string {
	if x != nil {
		return x.InputB
	}
	return ""
}

type AnnounceLotteryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AnnounceType int64 `protobuf:"varint,1,opt,name=AnnounceType,proto3" json:"AnnounceType,omitempty"`
}

func (x *AnnounceLotteryReq) Reset() {
	*x = AnnounceLotteryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnounceLotteryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnounceLotteryReq) ProtoMessage() {}

func (x *AnnounceLotteryReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnounceLotteryReq.ProtoReflect.Descriptor instead.
func (*AnnounceLotteryReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{17}
}

func (x *AnnounceLotteryReq) GetAnnounceType() int64 {
	if x != nil {
		return x.AnnounceType
	}
	return 0
}

type AnnounceLotteryResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AnnounceLotteryResp) Reset() {
	*x = AnnounceLotteryResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnounceLotteryResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnounceLotteryResp) ProtoMessage() {}

func (x *AnnounceLotteryResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnounceLotteryResp.ProtoReflect.Descriptor instead.
func (*AnnounceLotteryResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{18}
}

type CheckUserCreatedLotteryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId int64 `protobuf:"varint,1,opt,name=UserId,proto3" json:"UserId,omitempty"` // 用户id
}

func (x *CheckUserCreatedLotteryReq) Reset() {
	*x = CheckUserCreatedLotteryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckUserCreatedLotteryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUserCreatedLotteryReq) ProtoMessage() {}

func (x *CheckUserCreatedLotteryReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUserCreatedLotteryReq.ProtoReflect.Descriptor instead.
func (*CheckUserCreatedLotteryReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{19}
}

func (x *CheckUserCreatedLotteryReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type CheckUserCreatedLotteryResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsCreated int64 `protobuf:"varint,1,opt,name=IsCreated,proto3" json:"IsCreated,omitempty"` // 用户是否发起过抽奖：0否; 1是
}

func (x *CheckUserCreatedLotteryResp) Reset() {
	*x = CheckUserCreatedLotteryResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckUserCreatedLotteryResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUserCreatedLotteryResp) ProtoMessage() {}

func (x *CheckUserCreatedLotteryResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUserCreatedLotteryResp.ProtoReflect.Descriptor instead.
func (*CheckUserCreatedLotteryResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{20}
}

func (x *CheckUserCreatedLotteryResp) GetIsCreated() int64 {
	if x != nil {
		return x.IsCreated
	}
	return 0
}

type CheckUserCreatedLotteryAndTodayReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId int64 `protobuf:"varint,1,opt,name=UserId,proto3" json:"UserId,omitempty"` // 用户id
}

func (x *CheckUserCreatedLotteryAndTodayReq) Reset() {
	*x = CheckUserCreatedLotteryAndTodayReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckUserCreatedLotteryAndTodayReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUserCreatedLotteryAndTodayReq) ProtoMessage() {}

func (x *CheckUserCreatedLotteryAndTodayReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUserCreatedLotteryAndTodayReq.ProtoReflect.Descriptor instead.
func (*CheckUserCreatedLotteryAndTodayReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{21}
}

func (x *CheckUserCreatedLotteryAndTodayReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type CheckUserCreatedLotteryAndTodayResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Yes int64 `protobuf:"varint,1,opt,name=Yes,proto3" json:"Yes,omitempty"` // 用户是否在今天之内发布并有超过五个人参加：0否; 1是
}

func (x *CheckUserCreatedLotteryAndTodayResp) Reset() {
	*x = CheckUserCreatedLotteryAndTodayResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckUserCreatedLotteryAndTodayResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUserCreatedLotteryAndTodayResp) ProtoMessage() {}

func (x *CheckUserCreatedLotteryAndTodayResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUserCreatedLotteryAndTodayResp.ProtoReflect.Descriptor instead.
func (*CheckUserCreatedLotteryAndTodayResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{22}
}

func (x *CheckUserCreatedLotteryAndTodayResp) GetYes() int64 {
	if x != nil {
		return x.Yes
	}
	return 0
}

type CheckUserCreatedLotteryAndThisWeekReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId int64 `protobuf:"varint,1,opt,name=UserId,proto3" json:"UserId,omitempty"` // 用户id
}

func (x *CheckUserCreatedLotteryAndThisWeekReq) Reset() {
	*x = CheckUserCreatedLotteryAndThisWeekReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckUserCreatedLotteryAndThisWeekReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUserCreatedLotteryAndThisWeekReq) ProtoMessage() {}

func (x *CheckUserCreatedLotteryAndThisWeekReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUserCreatedLotteryAndThisWeekReq.ProtoReflect.Descriptor instead.
func (*CheckUserCreatedLotteryAndThisWeekReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{23}
}

func (x *CheckUserCreatedLotteryAndThisWeekReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type CheckUserCreatedLotteryAndThisWeekResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Yes int64 `protobuf:"varint,1,opt,name=Yes,proto3" json:"Yes,omitempty"` // 用户是否在这周之内发起并有超过十个人参加：0否; 1是
}

func (x *CheckUserCreatedLotteryAndThisWeekResp) Reset() {
	*x = CheckUserCreatedLotteryAndThisWeekResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckUserCreatedLotteryAndThisWeekResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUserCreatedLotteryAndThisWeekResp) ProtoMessage() {}

func (x *CheckUserCreatedLotteryAndThisWeekResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUserCreatedLotteryAndThisWeekResp.ProtoReflect.Descriptor instead.
func (*CheckUserCreatedLotteryAndThisWeekResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{24}
}

func (x *CheckUserCreatedLotteryAndThisWeekResp) GetYes() int64 {
	if x != nil {
		return x.Yes
	}
	return 0
}

type GetLotteryListAfterLoginReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId     int64 `protobuf:"varint,1,opt,name=UserId,proto3" json:"UserId,omitempty"`         // 用户id
	LastId     int64 `protobuf:"varint,2,opt,name=LastId,proto3" json:"LastId,omitempty"`         // 最后一条记录的id
	Page       int64 `protobuf:"varint,3,opt,name=Page,proto3" json:"Page,omitempty"`             // 页码
	Size       int64 `protobuf:"varint,4,opt,name=Size,proto3" json:"Size,omitempty"`             // 每页条数
	IsSelected int64 `protobuf:"varint,5,opt,name=IsSelected,proto3" json:"IsSelected,omitempty"` // 是否精选 1是 0否
}

func (x *GetLotteryListAfterLoginReq) Reset() {
	*x = GetLotteryListAfterLoginReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotteryListAfterLoginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotteryListAfterLoginReq) ProtoMessage() {}

func (x *GetLotteryListAfterLoginReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotteryListAfterLoginReq.ProtoReflect.Descriptor instead.
func (*GetLotteryListAfterLoginReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{25}
}

func (x *GetLotteryListAfterLoginReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetLotteryListAfterLoginReq) GetLastId() int64 {
	if x != nil {
		return x.LastId
	}
	return 0
}

func (x *GetLotteryListAfterLoginReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetLotteryListAfterLoginReq) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *GetLotteryListAfterLoginReq) GetIsSelected() int64 {
	if x != nil {
		return x.IsSelected
	}
	return 0
}

type GetLotteryListAfterLoginResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*Lottery `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"` // 抽奖列表
}

func (x *GetLotteryListAfterLoginResp) Reset() {
	*x = GetLotteryListAfterLoginResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotteryListAfterLoginResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotteryListAfterLoginResp) ProtoMessage() {}

func (x *GetLotteryListAfterLoginResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotteryListAfterLoginResp.ProtoReflect.Descriptor instead.
func (*GetLotteryListAfterLoginResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{26}
}

func (x *GetLotteryListAfterLoginResp) GetList() []*Lottery {
	if x != nil {
		return x.List
	}
	return nil
}

// 获取当前用户参与抽奖总数、发起抽奖总数、中奖记录总数
type GetLotteryStatisticReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId int64 `protobuf:"varint,1,opt,name=UserId,proto3" json:"UserId,omitempty"` // 用户id
}

func (x *GetLotteryStatisticReq) Reset() {
	*x = GetLotteryStatisticReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotteryStatisticReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotteryStatisticReq) ProtoMessage() {}

func (x *GetLotteryStatisticReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotteryStatisticReq.ProtoReflect.Descriptor instead.
func (*GetLotteryStatisticReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{27}
}

func (x *GetLotteryStatisticReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GetLotteryStatisticResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ParticipationCount int64 `protobuf:"varint,1,opt,name=ParticipationCount,proto3" json:"ParticipationCount,omitempty"` // 参与抽奖总数
	CreatedCount       int64 `protobuf:"varint,2,opt,name=CreatedCount,proto3" json:"CreatedCount,omitempty"`             // 发起抽奖总数
	WonCount           int64 `protobuf:"varint,3,opt,name=WonCount,proto3" json:"WonCount,omitempty"`                     // 中奖记录总数
}

func (x *GetLotteryStatisticResp) Reset() {
	*x = GetLotteryStatisticResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotteryStatisticResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotteryStatisticResp) ProtoMessage() {}

func (x *GetLotteryStatisticResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotteryStatisticResp.ProtoReflect.Descriptor instead.
func (*GetLotteryStatisticResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{28}
}

func (x *GetLotteryStatisticResp) GetParticipationCount() int64 {
	if x != nil {
		return x.ParticipationCount
	}
	return 0
}

func (x *GetLotteryStatisticResp) GetCreatedCount() int64 {
	if x != nil {
		return x.CreatedCount
	}
	return 0
}

func (x *GetLotteryStatisticResp) GetWonCount() int64 {
	if x != nil {
		return x.WonCount
	}
	return 0
}

// 获取抽奖列表lastId
type GetLotteryListLastIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetLotteryListLastIdReq) Reset() {
	*x = GetLotteryListLastIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotteryListLastIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotteryListLastIdReq) ProtoMessage() {}

func (x *GetLotteryListLastIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotteryListLastIdReq.ProtoReflect.Descriptor instead.
func (*GetLotteryListLastIdReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{29}
}

type GetLotteryListLastIdResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LastId int64 `protobuf:"varint,1,opt,name=LastId,proto3" json:"LastId,omitempty"` // 最后一条记录的id
}

func (x *GetLotteryListLastIdResp) Reset() {
	*x = GetLotteryListLastIdResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotteryListLastIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotteryListLastIdResp) ProtoMessage() {}

func (x *GetLotteryListLastIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotteryListLastIdResp.ProtoReflect.Descriptor instead.
func (*GetLotteryListLastIdResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{30}
}

func (x *GetLotteryListLastIdResp) GetLastId() int64 {
	if x != nil {
		return x.LastId
	}
	return 0
}

// 发布抽奖
type PublishLotteryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LotteryId int64 `protobuf:"varint,1,opt,name=LotteryId,proto3" json:"LotteryId,omitempty"` // 抽奖id
}

func (x *PublishLotteryReq) Reset() {
	*x = PublishLotteryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PublishLotteryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishLotteryReq) ProtoMessage() {}

func (x *PublishLotteryReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishLotteryReq.ProtoReflect.Descriptor instead.
func (*PublishLotteryReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{31}
}

func (x *PublishLotteryReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

type PublishLotteryResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PublishLotteryResp) Reset() {
	*x = PublishLotteryResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PublishLotteryResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishLotteryResp) ProtoMessage() {}

func (x *PublishLotteryResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishLotteryResp.ProtoReflect.Descriptor instead.
func (*PublishLotteryResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{32}
}

// --------------------------------奖品表--------------------------------
type Prize struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                  //id
	LotteryId  int64  `protobuf:"varint,2,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"`    //抽奖ID
	Type       int64  `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`              //奖品类型：1奖品 2优惠券 3兑换码 4商城 5微信红包封面 6红包
	Name       string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`               //奖品名称
	Level      int64  `protobuf:"varint,5,opt,name=level,proto3" json:"level,omitempty"`            //几等奖 默认1
	Thumb      string `protobuf:"bytes,6,opt,name=thumb,proto3" json:"thumb,omitempty"`             //奖品图
	Count      int64  `protobuf:"varint,7,opt,name=count,proto3" json:"count,omitempty"`            //奖品份数
	GrantType  int64  `protobuf:"varint,8,opt,name=grantType,proto3" json:"grantType,omitempty"`    //奖品发放方式：1快递邮寄 2让中奖者联系我 3中奖者填写信息 4跳转到其他小程序
	CreateTime int64  `protobuf:"varint,9,opt,name=createTime,proto3" json:"createTime,omitempty"`  //createTime
	UpdateTime int64  `protobuf:"varint,10,opt,name=updateTime,proto3" json:"updateTime,omitempty"` //updateTime
}

func (x *Prize) Reset() {
	*x = Prize{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Prize) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Prize) ProtoMessage() {}

func (x *Prize) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Prize.ProtoReflect.Descriptor instead.
func (*Prize) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{33}
}

func (x *Prize) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Prize) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

func (x *Prize) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *Prize) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Prize) GetLevel() int64 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *Prize) GetThumb() string {
	if x != nil {
		return x.Thumb
	}
	return ""
}

func (x *Prize) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *Prize) GetGrantType() int64 {
	if x != nil {
		return x.GrantType
	}
	return 0
}

func (x *Prize) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *Prize) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

type AddPrizeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LotteryId int64  `protobuf:"varint,1,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"` //抽奖ID
	Type      int64  `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`           //奖品类型：1奖品 2优惠券 3兑换码 4商城 5微信红包封面 6红包
	Name      string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`            //奖品名称
	Level     int64  `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`         //几等奖 默认1
	Thumb     string `protobuf:"bytes,5,opt,name=thumb,proto3" json:"thumb,omitempty"`          //奖品图
	Count     int64  `protobuf:"varint,6,opt,name=count,proto3" json:"count,omitempty"`         //奖品份数
	GrantType int64  `protobuf:"varint,7,opt,name=grantType,proto3" json:"grantType,omitempty"` //奖品发放方式：1快递邮寄 2让中奖者联系我 3中奖者填写信息 4跳转到其他小程序
}

func (x *AddPrizeReq) Reset() {
	*x = AddPrizeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddPrizeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPrizeReq) ProtoMessage() {}

func (x *AddPrizeReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPrizeReq.ProtoReflect.Descriptor instead.
func (*AddPrizeReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{34}
}

func (x *AddPrizeReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

func (x *AddPrizeReq) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *AddPrizeReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AddPrizeReq) GetLevel() int64 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *AddPrizeReq) GetThumb() string {
	if x != nil {
		return x.Thumb
	}
	return ""
}

func (x *AddPrizeReq) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *AddPrizeReq) GetGrantType() int64 {
	if x != nil {
		return x.GrantType
	}
	return 0
}

type AddPrizeResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddPrizeResp) Reset() {
	*x = AddPrizeResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddPrizeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPrizeResp) ProtoMessage() {}

func (x *AddPrizeResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPrizeResp.ProtoReflect.Descriptor instead.
func (*AddPrizeResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{35}
}

type UpdatePrizeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`               //id
	LotteryId int64  `protobuf:"varint,2,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"` //抽奖ID
	Type      int64  `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`           //奖品类型：1奖品 2优惠券 3兑换码 4商城 5微信红包封面 6红包
	Name      string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`            //奖品名称
	Level     int64  `protobuf:"varint,5,opt,name=level,proto3" json:"level,omitempty"`         //几等奖 默认1
	Thumb     string `protobuf:"bytes,6,opt,name=thumb,proto3" json:"thumb,omitempty"`          //奖品图
	Count     int64  `protobuf:"varint,7,opt,name=count,proto3" json:"count,omitempty"`         //奖品份数
	GrantType int64  `protobuf:"varint,8,opt,name=grantType,proto3" json:"grantType,omitempty"` //奖品发放方式：1快递邮寄 2让中奖者联系我 3中奖者填写信息 4跳转到其他小程序
}

func (x *UpdatePrizeReq) Reset() {
	*x = UpdatePrizeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePrizeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePrizeReq) ProtoMessage() {}

func (x *UpdatePrizeReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePrizeReq.ProtoReflect.Descriptor instead.
func (*UpdatePrizeReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{36}
}

func (x *UpdatePrizeReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePrizeReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

func (x *UpdatePrizeReq) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *UpdatePrizeReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdatePrizeReq) GetLevel() int64 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *UpdatePrizeReq) GetThumb() string {
	if x != nil {
		return x.Thumb
	}
	return ""
}

func (x *UpdatePrizeReq) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *UpdatePrizeReq) GetGrantType() int64 {
	if x != nil {
		return x.GrantType
	}
	return 0
}

type UpdatePrizeResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdatePrizeResp) Reset() {
	*x = UpdatePrizeResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePrizeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePrizeResp) ProtoMessage() {}

func (x *UpdatePrizeResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePrizeResp.ProtoReflect.Descriptor instead.
func (*UpdatePrizeResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{37}
}

type DelPrizeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *DelPrizeReq) Reset() {
	*x = DelPrizeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelPrizeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelPrizeReq) ProtoMessage() {}

func (x *DelPrizeReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelPrizeReq.ProtoReflect.Descriptor instead.
func (*DelPrizeReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{38}
}

func (x *DelPrizeReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DelPrizeResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DelPrizeResp) Reset() {
	*x = DelPrizeResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelPrizeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelPrizeResp) ProtoMessage() {}

func (x *DelPrizeResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelPrizeResp.ProtoReflect.Descriptor instead.
func (*DelPrizeResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{39}
}

type GetPrizeByIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *GetPrizeByIdReq) Reset() {
	*x = GetPrizeByIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPrizeByIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPrizeByIdReq) ProtoMessage() {}

func (x *GetPrizeByIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPrizeByIdReq.ProtoReflect.Descriptor instead.
func (*GetPrizeByIdReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{40}
}

func (x *GetPrizeByIdReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetPrizeByIdResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Prize *Prize `protobuf:"bytes,1,opt,name=prize,proto3" json:"prize,omitempty"` //prize
}

func (x *GetPrizeByIdResp) Reset() {
	*x = GetPrizeByIdResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPrizeByIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPrizeByIdResp) ProtoMessage() {}

func (x *GetPrizeByIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPrizeByIdResp.ProtoReflect.Descriptor instead.
func (*GetPrizeByIdResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{41}
}

func (x *GetPrizeByIdResp) GetPrize() *Prize {
	if x != nil {
		return x.Prize
	}
	return nil
}

type SearchPrizeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page      int64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`           //page
	Limit     int64 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`         //limit
	LotteryId int64 `protobuf:"varint,3,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"` //抽奖ID
}

func (x *SearchPrizeReq) Reset() {
	*x = SearchPrizeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchPrizeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPrizeReq) ProtoMessage() {}

func (x *SearchPrizeReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPrizeReq.ProtoReflect.Descriptor instead.
func (*SearchPrizeReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{42}
}

func (x *SearchPrizeReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchPrizeReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *SearchPrizeReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

type SearchPrizeResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Prize []*Prize `protobuf:"bytes,1,rep,name=prize,proto3" json:"prize,omitempty"` //prize
}

func (x *SearchPrizeResp) Reset() {
	*x = SearchPrizeResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchPrizeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPrizeResp) ProtoMessage() {}

func (x *SearchPrizeResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPrizeResp.ProtoReflect.Descriptor instead.
func (*SearchPrizeResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{43}
}

func (x *SearchPrizeResp) GetPrize() []*Prize {
	if x != nil {
		return x.Prize
	}
	return nil
}

type CreatePrize struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type      int64  `protobuf:"varint,1,opt,name=Type,proto3" json:"Type,omitempty"`           //奖品类型：1奖品 2优惠券 3兑换码 4商城 5微信红包封面 6红包
	Name      []byte `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty"`            //奖品名称
	Count     int64  `protobuf:"varint,3,opt,name=Count,proto3" json:"Count,omitempty"`         //奖品份数
	Thumb     []byte `protobuf:"bytes,4,opt,name=Thumb,proto3" json:"Thumb,omitempty"`          //默认一等奖配图
	Level     int64  `protobuf:"varint,5,opt,name=Level,proto3" json:"Level,omitempty"`         //奖品等级 1一等奖 2二等奖 3三等奖，依次类推
	GrantType int64  `protobuf:"varint,6,opt,name=GrantType,proto3" json:"GrantType,omitempty"` //奖品发放方式：1快递邮寄 2让中奖者联系我 3中奖者填写信息 4跳转到其他小程序
}

func (x *CreatePrize) Reset() {
	*x = CreatePrize{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePrize) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePrize) ProtoMessage() {}

func (x *CreatePrize) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePrize.ProtoReflect.Descriptor instead.
func (*CreatePrize) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{44}
}

func (x *CreatePrize) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *CreatePrize) GetName() []byte {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *CreatePrize) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *CreatePrize) GetThumb() []byte {
	if x != nil {
		return x.Thumb
	}
	return nil
}

func (x *CreatePrize) GetLevel() int64 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *CreatePrize) GetGrantType() int64 {
	if x != nil {
		return x.GrantType
	}
	return 0
}

type LotteryPrizes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LotteryId       int64    `protobuf:"varint,2,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"`             // 抽奖id
	Prizes          []*Prize `protobuf:"bytes,3,rep,name=Prizes,proto3" json:"Prizes,omitempty"`                    // 奖品列表
	ParticipationId int64    `protobuf:"varint,4,opt,name=ParticipationId,proto3" json:"ParticipationId,omitempty"` // 参与id
	Time            int64    `protobuf:"varint,5,opt,name=Time,proto3" json:"Time,omitempty"`                       // 参与/创建/中奖时间
}

func (x *LotteryPrizes) Reset() {
	*x = LotteryPrizes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LotteryPrizes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LotteryPrizes) ProtoMessage() {}

func (x *LotteryPrizes) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LotteryPrizes.ProtoReflect.Descriptor instead.
func (*LotteryPrizes) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{45}
}

func (x *LotteryPrizes) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

func (x *LotteryPrizes) GetPrizes() []*Prize {
	if x != nil {
		return x.Prizes
	}
	return nil
}

func (x *LotteryPrizes) GetParticipationId() int64 {
	if x != nil {
		return x.ParticipationId
	}
	return 0
}

func (x *LotteryPrizes) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

// 获取当前用户发起/参与/中奖的抽奖列表
type GetLotteryPrizesListByUserIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type        int64 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`               // 1发起的 2参与的 3中奖的
	Size        int64 `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`               // 每页条数
	UserId      int64 `protobuf:"varint,3,opt,name=userId,proto3" json:"userId,omitempty"`           // 当前用户id
	LastId      int64 `protobuf:"varint,4,opt,name=lastId,proto3" json:"lastId,omitempty"`           // 最后一条记录的id
	IsAnnounced int64 `protobuf:"varint,5,opt,name=isAnnounced,proto3" json:"isAnnounced,omitempty"` // 是否已开奖 0否 1是
}

func (x *GetLotteryPrizesListByUserIdReq) Reset() {
	*x = GetLotteryPrizesListByUserIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotteryPrizesListByUserIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotteryPrizesListByUserIdReq) ProtoMessage() {}

func (x *GetLotteryPrizesListByUserIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotteryPrizesListByUserIdReq.ProtoReflect.Descriptor instead.
func (*GetLotteryPrizesListByUserIdReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{46}
}

func (x *GetLotteryPrizesListByUserIdReq) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *GetLotteryPrizesListByUserIdReq) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *GetLotteryPrizesListByUserIdReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetLotteryPrizesListByUserIdReq) GetLastId() int64 {
	if x != nil {
		return x.LastId
	}
	return 0
}

func (x *GetLotteryPrizesListByUserIdReq) GetIsAnnounced() int64 {
	if x != nil {
		return x.IsAnnounced
	}
	return 0
}

type GetLotteryPrizesListByUserIdResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LotteryPrizes []*LotteryPrizes `protobuf:"bytes,1,rep,name=LotteryPrizes,proto3" json:"LotteryPrizes,omitempty"` // 抽奖列表
}

func (x *GetLotteryPrizesListByUserIdResp) Reset() {
	*x = GetLotteryPrizesListByUserIdResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLotteryPrizesListByUserIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLotteryPrizesListByUserIdResp) ProtoMessage() {}

func (x *GetLotteryPrizesListByUserIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLotteryPrizesListByUserIdResp.ProtoReflect.Descriptor instead.
func (*GetLotteryPrizesListByUserIdResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{47}
}

func (x *GetLotteryPrizesListByUserIdResp) GetLotteryPrizes() []*LotteryPrizes {
	if x != nil {
		return x.LotteryPrizes
	}
	return nil
}

type AddLotteryParticipationReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    int64 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	LotteryId int64 `protobuf:"varint,2,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"`
}

func (x *AddLotteryParticipationReq) Reset() {
	*x = AddLotteryParticipationReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddLotteryParticipationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddLotteryParticipationReq) ProtoMessage() {}

func (x *AddLotteryParticipationReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddLotteryParticipationReq.ProtoReflect.Descriptor instead.
func (*AddLotteryParticipationReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{48}
}

func (x *AddLotteryParticipationReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AddLotteryParticipationReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

type AddLotteryParticipationResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *AddLotteryParticipationResp) Reset() {
	*x = AddLotteryParticipationResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddLotteryParticipationResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddLotteryParticipationResp) ProtoMessage() {}

func (x *AddLotteryParticipationResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddLotteryParticipationResp.ProtoReflect.Descriptor instead.
func (*AddLotteryParticipationResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{49}
}

func (x *AddLotteryParticipationResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type SearchLotteryParticipationReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LotteryId int64 `protobuf:"varint,1,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"`
	PageIndex int64 `protobuf:"varint,2,opt,name=pageIndex,proto3" json:"pageIndex,omitempty"`
	PageSize  int64 `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
}

func (x *SearchLotteryParticipationReq) Reset() {
	*x = SearchLotteryParticipationReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchLotteryParticipationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchLotteryParticipationReq) ProtoMessage() {}

func (x *SearchLotteryParticipationReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchLotteryParticipationReq.ProtoReflect.Descriptor instead.
func (*SearchLotteryParticipationReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{50}
}

func (x *SearchLotteryParticipationReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

func (x *SearchLotteryParticipationReq) GetPageIndex() int64 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *SearchLotteryParticipationReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type LotteryParticipation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	LotteryId int64 `protobuf:"varint,2,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"`
	UserId    int64 `protobuf:"varint,3,opt,name=userId,proto3" json:"userId,omitempty"`
	IsWon     bool  `protobuf:"varint,4,opt,name=isWon,proto3" json:"isWon,omitempty"`
	PrizeId   int64 `protobuf:"varint,5,opt,name=prizeId,proto3" json:"prizeId,omitempty"`
}

func (x *LotteryParticipation) Reset() {
	*x = LotteryParticipation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LotteryParticipation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LotteryParticipation) ProtoMessage() {}

func (x *LotteryParticipation) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LotteryParticipation.ProtoReflect.Descriptor instead.
func (*LotteryParticipation) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{51}
}

func (x *LotteryParticipation) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LotteryParticipation) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

func (x *LotteryParticipation) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *LotteryParticipation) GetIsWon() bool {
	if x != nil {
		return x.IsWon
	}
	return false
}

func (x *LotteryParticipation) GetPrizeId() int64 {
	if x != nil {
		return x.PrizeId
	}
	return 0
}

type SearchLotteryParticipationResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count int64                   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	List  []*LotteryParticipation `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *SearchLotteryParticipationResp) Reset() {
	*x = SearchLotteryParticipationResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchLotteryParticipationResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchLotteryParticipationResp) ProtoMessage() {}

func (x *SearchLotteryParticipationResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchLotteryParticipationResp.ProtoReflect.Descriptor instead.
func (*SearchLotteryParticipationResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{52}
}

func (x *SearchLotteryParticipationResp) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *SearchLotteryParticipationResp) GetList() []*LotteryParticipation {
	if x != nil {
		return x.List
	}
	return nil
}

type GetPrizeListByLotteryIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LotteryId int64 `protobuf:"varint,1,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"` //id
}

func (x *GetPrizeListByLotteryIdReq) Reset() {
	*x = GetPrizeListByLotteryIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPrizeListByLotteryIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPrizeListByLotteryIdReq) ProtoMessage() {}

func (x *GetPrizeListByLotteryIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPrizeListByLotteryIdReq.ProtoReflect.Descriptor instead.
func (*GetPrizeListByLotteryIdReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{53}
}

func (x *GetPrizeListByLotteryIdReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

type GetPrizeListByLotteryIdResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Prizes []*Prize `protobuf:"bytes,1,rep,name=prizes,proto3" json:"prizes,omitempty"` // 奖品列表
}

func (x *GetPrizeListByLotteryIdResp) Reset() {
	*x = GetPrizeListByLotteryIdResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPrizeListByLotteryIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPrizeListByLotteryIdResp) ProtoMessage() {}

func (x *GetPrizeListByLotteryIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPrizeListByLotteryIdResp.ProtoReflect.Descriptor instead.
func (*GetPrizeListByLotteryIdResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{54}
}

func (x *GetPrizeListByLotteryIdResp) GetPrizes() []*Prize {
	if x != nil {
		return x.Prizes
	}
	return nil
}

type GetParticipationUserIdsByLotteryIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LotteryId int64 `protobuf:"varint,1,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"`
}

func (x *GetParticipationUserIdsByLotteryIdReq) Reset() {
	*x = GetParticipationUserIdsByLotteryIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetParticipationUserIdsByLotteryIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetParticipationUserIdsByLotteryIdReq) ProtoMessage() {}

func (x *GetParticipationUserIdsByLotteryIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetParticipationUserIdsByLotteryIdReq.ProtoReflect.Descriptor instead.
func (*GetParticipationUserIdsByLotteryIdReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{55}
}

func (x *GetParticipationUserIdsByLotteryIdReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

type GetParticipationUserIdsByLotteryIdResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserIds []int64 `protobuf:"varint,1,rep,packed,name=userIds,proto3" json:"userIds,omitempty"`
}

func (x *GetParticipationUserIdsByLotteryIdResp) Reset() {
	*x = GetParticipationUserIdsByLotteryIdResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetParticipationUserIdsByLotteryIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetParticipationUserIdsByLotteryIdResp) ProtoMessage() {}

func (x *GetParticipationUserIdsByLotteryIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetParticipationUserIdsByLotteryIdResp.ProtoReflect.Descriptor instead.
func (*GetParticipationUserIdsByLotteryIdResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{56}
}

func (x *GetParticipationUserIdsByLotteryIdResp) GetUserIds() []int64 {
	if x != nil {
		return x.UserIds
	}
	return nil
}

type CheckIsParticipatedReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LotteryId int64 `protobuf:"varint,1,opt,name=LotteryId,proto3" json:"LotteryId,omitempty"` // 当前抽奖id
	UserId    int64 `protobuf:"varint,2,opt,name=UserId,proto3" json:"UserId,omitempty"`       // 当前用户id
}

func (x *CheckIsParticipatedReq) Reset() {
	*x = CheckIsParticipatedReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckIsParticipatedReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckIsParticipatedReq) ProtoMessage() {}

func (x *CheckIsParticipatedReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckIsParticipatedReq.ProtoReflect.Descriptor instead.
func (*CheckIsParticipatedReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{57}
}

func (x *CheckIsParticipatedReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

func (x *CheckIsParticipatedReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type CheckIsParticipatedResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsParticipated int64 `protobuf:"varint,1,opt,name=IsParticipated,proto3" json:"IsParticipated,omitempty"` // 当前用户是否已经参与当前抽奖
}

func (x *CheckIsParticipatedResp) Reset() {
	*x = CheckIsParticipatedResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckIsParticipatedResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckIsParticipatedResp) ProtoMessage() {}

func (x *CheckIsParticipatedResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckIsParticipatedResp.ProtoReflect.Descriptor instead.
func (*CheckIsParticipatedResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{58}
}

func (x *CheckIsParticipatedResp) GetIsParticipated() int64 {
	if x != nil {
		return x.IsParticipated
	}
	return 0
}

type GetSelectedLotteryStatisticReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId int64 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
}

func (x *GetSelectedLotteryStatisticReq) Reset() {
	*x = GetSelectedLotteryStatisticReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSelectedLotteryStatisticReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSelectedLotteryStatisticReq) ProtoMessage() {}

func (x *GetSelectedLotteryStatisticReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSelectedLotteryStatisticReq.ProtoReflect.Descriptor instead.
func (*GetSelectedLotteryStatisticReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{59}
}

func (x *GetSelectedLotteryStatisticReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GetSelectedLotteryStatisticResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DayCount  int64 `protobuf:"varint,1,opt,name=dayCount,proto3" json:"dayCount,omitempty"`
	WeekCount int64 `protobuf:"varint,2,opt,name=weekCount,proto3" json:"weekCount,omitempty"`
}

func (x *GetSelectedLotteryStatisticResp) Reset() {
	*x = GetSelectedLotteryStatisticResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSelectedLotteryStatisticResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSelectedLotteryStatisticResp) ProtoMessage() {}

func (x *GetSelectedLotteryStatisticResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSelectedLotteryStatisticResp.ProtoReflect.Descriptor instead.
func (*GetSelectedLotteryStatisticResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{60}
}

func (x *GetSelectedLotteryStatisticResp) GetDayCount() int64 {
	if x != nil {
		return x.DayCount
	}
	return 0
}

func (x *GetSelectedLotteryStatisticResp) GetWeekCount() int64 {
	if x != nil {
		return x.WeekCount
	}
	return 0
}

type CheckSelectedLotteryParticipatedReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId int64 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
}

func (x *CheckSelectedLotteryParticipatedReq) Reset() {
	*x = CheckSelectedLotteryParticipatedReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckSelectedLotteryParticipatedReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckSelectedLotteryParticipatedReq) ProtoMessage() {}

func (x *CheckSelectedLotteryParticipatedReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckSelectedLotteryParticipatedReq.ProtoReflect.Descriptor instead.
func (*CheckSelectedLotteryParticipatedReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{61}
}

func (x *CheckSelectedLotteryParticipatedReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type CheckSelectedLotteryParticipatedResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Participated int64 `protobuf:"varint,1,opt,name=participated,proto3" json:"participated,omitempty"`
}

func (x *CheckSelectedLotteryParticipatedResp) Reset() {
	*x = CheckSelectedLotteryParticipatedResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckSelectedLotteryParticipatedResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckSelectedLotteryParticipatedResp) ProtoMessage() {}

func (x *CheckSelectedLotteryParticipatedResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckSelectedLotteryParticipatedResp.ProtoReflect.Descriptor instead.
func (*CheckSelectedLotteryParticipatedResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{62}
}

func (x *CheckSelectedLotteryParticipatedResp) GetParticipated() int64 {
	if x != nil {
		return x.Participated
	}
	return 0
}

type CheckUserIsWonReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    int64 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	LotteryId int64 `protobuf:"varint,2,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"`
}

func (x *CheckUserIsWonReq) Reset() {
	*x = CheckUserIsWonReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckUserIsWonReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUserIsWonReq) ProtoMessage() {}

func (x *CheckUserIsWonReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUserIsWonReq.ProtoReflect.Descriptor instead.
func (*CheckUserIsWonReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{63}
}

func (x *CheckUserIsWonReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *CheckUserIsWonReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

type CheckUserIsWonResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsWon int64 `protobuf:"varint,1,opt,name=isWon,proto3" json:"isWon,omitempty"`
}

func (x *CheckUserIsWonResp) Reset() {
	*x = CheckUserIsWonResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckUserIsWonResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUserIsWonResp) ProtoMessage() {}

func (x *CheckUserIsWonResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUserIsWonResp.ProtoReflect.Descriptor instead.
func (*CheckUserIsWonResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{64}
}

func (x *CheckUserIsWonResp) GetIsWon() int64 {
	if x != nil {
		return x.IsWon
	}
	return 0
}

// 获取当前用户中奖列表
type GetWonListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId int64 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	LastId int64 `protobuf:"varint,2,opt,name=lastId,proto3" json:"lastId,omitempty"`
	Size   int64 `protobuf:"varint,4,opt,name=size,proto3" json:"size,omitempty"`
}

func (x *GetWonListReq) Reset() {
	*x = GetWonListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWonListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWonListReq) ProtoMessage() {}

func (x *GetWonListReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWonListReq.ProtoReflect.Descriptor instead.
func (*GetWonListReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{65}
}

func (x *GetWonListReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetWonListReq) GetLastId() int64 {
	if x != nil {
		return x.LastId
	}
	return 0
}

func (x *GetWonListReq) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

type WonList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	LotteryId int64  `protobuf:"varint,2,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"`
	UserId    int64  `protobuf:"varint,3,opt,name=userId,proto3" json:"userId,omitempty"`
	IsWon     bool   `protobuf:"varint,4,opt,name=isWon,proto3" json:"isWon,omitempty"`
	Prize     *Prize `protobuf:"bytes,5,opt,name=prize,proto3" json:"prize,omitempty"`
}

func (x *WonList) Reset() {
	*x = WonList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WonList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WonList) ProtoMessage() {}

func (x *WonList) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WonList.ProtoReflect.Descriptor instead.
func (*WonList) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{66}
}

func (x *WonList) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WonList) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

func (x *WonList) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *WonList) GetIsWon() bool {
	if x != nil {
		return x.IsWon
	}
	return false
}

func (x *WonList) GetPrize() *Prize {
	if x != nil {
		return x.Prize
	}
	return nil
}

type GetWonListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*WonList `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *GetWonListResp) Reset() {
	*x = GetWonListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWonListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWonListResp) ProtoMessage() {}

func (x *GetWonListResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWonListResp.ProtoReflect.Descriptor instead.
func (*GetWonListResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{67}
}

func (x *GetWonListResp) GetList() []*WonList {
	if x != nil {
		return x.List
	}
	return nil
}

// 获取中奖记录总数/获取当前用户累计奖品数量
type GetWonListCountReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId int64 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
}

func (x *GetWonListCountReq) Reset() {
	*x = GetWonListCountReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWonListCountReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWonListCountReq) ProtoMessage() {}

func (x *GetWonListCountReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWonListCountReq.ProtoReflect.Descriptor instead.
func (*GetWonListCountReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{68}
}

func (x *GetWonListCountReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GetWonListCountResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count int64 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *GetWonListCountResp) Reset() {
	*x = GetWonListCountResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWonListCountResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWonListCountResp) ProtoMessage() {}

func (x *GetWonListCountResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWonListCountResp.ProtoReflect.Descriptor instead.
func (*GetWonListCountResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{69}
}

func (x *GetWonListCountResp) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 获取当前抽奖中奖者名单
type GetWonListByLotteryIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LotteryId int64 `protobuf:"varint,1,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"`
}

func (x *GetWonListByLotteryIdReq) Reset() {
	*x = GetWonListByLotteryIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWonListByLotteryIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWonListByLotteryIdReq) ProtoMessage() {}

func (x *GetWonListByLotteryIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWonListByLotteryIdReq.ProtoReflect.Descriptor instead.
func (*GetWonListByLotteryIdReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{70}
}

func (x *GetWonListByLotteryIdReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Nickname []byte `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Avatar   []byte `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{71}
}

func (x *UserInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserInfo) GetNickname() []byte {
	if x != nil {
		return x.Nickname
	}
	return nil
}

func (x *UserInfo) GetAvatar() []byte {
	if x != nil {
		return x.Avatar
	}
	return nil
}

type WonList2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Prize       *Prize      `protobuf:"bytes,1,opt,name=prize,proto3" json:"prize,omitempty"`
	WinnerCount int64       `protobuf:"varint,2,opt,name=WinnerCount,proto3" json:"WinnerCount,omitempty"`
	Users       []*UserInfo `protobuf:"bytes,3,rep,name=users,proto3" json:"users,omitempty"`
}

func (x *WonList2) Reset() {
	*x = WonList2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WonList2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WonList2) ProtoMessage() {}

func (x *WonList2) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WonList2.ProtoReflect.Descriptor instead.
func (*WonList2) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{72}
}

func (x *WonList2) GetPrize() *Prize {
	if x != nil {
		return x.Prize
	}
	return nil
}

func (x *WonList2) GetWinnerCount() int64 {
	if x != nil {
		return x.WinnerCount
	}
	return 0
}

func (x *WonList2) GetUsers() []*UserInfo {
	if x != nil {
		return x.Users
	}
	return nil
}

type GetWonListByLotteryIdResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*WonList2 `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *GetWonListByLotteryIdResp) Reset() {
	*x = GetWonListByLotteryIdResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWonListByLotteryIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWonListByLotteryIdResp) ProtoMessage() {}

func (x *GetWonListByLotteryIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWonListByLotteryIdResp.ProtoReflect.Descriptor instead.
func (*GetWonListByLotteryIdResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{73}
}

func (x *GetWonListByLotteryIdResp) GetList() []*WonList2 {
	if x != nil {
		return x.List
	}
	return nil
}

// --------------------------------打卡任务表--------------------------------
type ClockTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type             int64  `protobuf:"varint,1,opt,name=Type,proto3" json:"Type,omitempty"`
	Seconds          int64  `protobuf:"varint,2,opt,name=Seconds,proto3" json:"Seconds,omitempty"`
	AppletType       int64  `protobuf:"varint,3,opt,name=AppletType,proto3" json:"AppletType,omitempty"`
	PageLink         string `protobuf:"bytes,4,opt,name=PageLink,proto3" json:"PageLink,omitempty"`
	AppId            string `protobuf:"bytes,5,opt,name=AppId,proto3" json:"AppId,omitempty"`
	PagePath         string `protobuf:"bytes,6,opt,name=PagePath,proto3" json:"PagePath,omitempty"`
	Image            string `protobuf:"bytes,7,opt,name=Image,proto3" json:"Image,omitempty"`
	VideoAccountId   string `protobuf:"bytes,8,opt,name=VideoAccountId,proto3" json:"VideoAccountId,omitempty"`
	VideoId          string `protobuf:"bytes,9,opt,name=VideoId,proto3" json:"VideoId,omitempty"`
	ArticleLink      string `protobuf:"bytes,10,opt,name=ArticleLink,proto3" json:"ArticleLink,omitempty"`
	Copywriting      string `protobuf:"bytes,11,opt,name=Copywriting,proto3" json:"Copywriting,omitempty"`
	ChanceType       int64  `protobuf:"varint,12,opt,name=ChanceType,proto3" json:"ChanceType,omitempty"`
	IncreaseMultiple int64  `protobuf:"varint,13,opt,name=IncreaseMultiple,proto3" json:"IncreaseMultiple,omitempty"`
}

func (x *ClockTask) Reset() {
	*x = ClockTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClockTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClockTask) ProtoMessage() {}

func (x *ClockTask) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClockTask.ProtoReflect.Descriptor instead.
func (*ClockTask) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{74}
}

func (x *ClockTask) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *ClockTask) GetSeconds() int64 {
	if x != nil {
		return x.Seconds
	}
	return 0
}

func (x *ClockTask) GetAppletType() int64 {
	if x != nil {
		return x.AppletType
	}
	return 0
}

func (x *ClockTask) GetPageLink() string {
	if x != nil {
		return x.PageLink
	}
	return ""
}

func (x *ClockTask) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *ClockTask) GetPagePath() string {
	if x != nil {
		return x.PagePath
	}
	return ""
}

func (x *ClockTask) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *ClockTask) GetVideoAccountId() string {
	if x != nil {
		return x.VideoAccountId
	}
	return ""
}

func (x *ClockTask) GetVideoId() string {
	if x != nil {
		return x.VideoId
	}
	return ""
}

func (x *ClockTask) GetArticleLink() string {
	if x != nil {
		return x.ArticleLink
	}
	return ""
}

func (x *ClockTask) GetCopywriting() string {
	if x != nil {
		return x.Copywriting
	}
	return ""
}

func (x *ClockTask) GetChanceType() int64 {
	if x != nil {
		return x.ChanceType
	}
	return 0
}

func (x *ClockTask) GetIncreaseMultiple() int64 {
	if x != nil {
		return x.IncreaseMultiple
	}
	return 0
}

// --------------------------------打卡任务记录表--------------------------------
type AddClockTaskRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LotteryId        int64 `protobuf:"varint,1,opt,name=LotteryId,proto3" json:"LotteryId,omitempty"`
	UserId           int64 `protobuf:"varint,2,opt,name=UserId,proto3" json:"UserId,omitempty"`
	ClockTaskId      int64 `protobuf:"varint,3,opt,name=ClockTaskId,proto3" json:"ClockTaskId,omitempty"`
	IncreaseMultiple int64 `protobuf:"varint,4,opt,name=IncreaseMultiple,proto3" json:"IncreaseMultiple,omitempty"`
}

func (x *AddClockTaskRecordReq) Reset() {
	*x = AddClockTaskRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddClockTaskRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddClockTaskRecordReq) ProtoMessage() {}

func (x *AddClockTaskRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddClockTaskRecordReq.ProtoReflect.Descriptor instead.
func (*AddClockTaskRecordReq) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{75}
}

func (x *AddClockTaskRecordReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

func (x *AddClockTaskRecordReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AddClockTaskRecordReq) GetClockTaskId() int64 {
	if x != nil {
		return x.ClockTaskId
	}
	return 0
}

func (x *AddClockTaskRecordReq) GetIncreaseMultiple() int64 {
	if x != nil {
		return x.IncreaseMultiple
	}
	return 0
}

type AddClockTaskRecordResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *AddClockTaskRecordResp) Reset() {
	*x = AddClockTaskRecordResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_lottery_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddClockTaskRecordResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddClockTaskRecordResp) ProtoMessage() {}

func (x *AddClockTaskRecordResp) ProtoReflect() protoreflect.Message {
	mi := &file_lottery_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddClockTaskRecordResp.ProtoReflect.Descriptor instead.
func (*AddClockTaskRecordResp) Descriptor() ([]byte, []int) {
	return file_lottery_proto_rawDescGZIP(), []int{76}
}

func (x *AddClockTaskRecordResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

var File_lottery_proto protoreflect.FileDescriptor

var file_lottery_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x02, 0x70, 0x62, 0x22, 0xc9, 0x03, 0x0a, 0x07, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x68, 0x75, 0x6d, 0x62, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x68, 0x75, 0x6d,
	0x62, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6a, 0x6f, 0x69, 0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6a, 0x6f, 0x69, 0x6e, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x65, 0x12, 0x24, 0x0a, 0x0d, 0x61, 0x77, 0x61, 0x72, 0x64, 0x44, 0x65, 0x61, 0x64, 0x6c, 0x69,
	0x6e, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x77, 0x61, 0x72, 0x64, 0x44,
	0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x73, 0x53, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x69, 0x73, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x6e, 0x6e, 0x6f, 0x75,
	0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x61,
	0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x61,
	0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0c, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x69, 0x73, 0x41, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x64, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x69, 0x73, 0x41, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65,
	0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x49, 0x64, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x49, 0x64, 0x22,
	0xab, 0x03, 0x0a, 0x0d, 0x41, 0x64, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65,
	0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x68,
	0x75, 0x6d, 0x62, 0x12, 0x1e, 0x0a, 0x0a, 0x6a, 0x6f, 0x69, 0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6a, 0x6f, 0x69, 0x6e, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x65, 0x12, 0x24, 0x0a, 0x0d, 0x61, 0x77, 0x61, 0x72, 0x64, 0x44, 0x65, 0x61, 0x64, 0x6c, 0x69,
	0x6e, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x77, 0x61, 0x72, 0x64, 0x44,
	0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x6e, 0x6e, 0x6f, 0x75,
	0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x61,
	0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x06, 0x70,
	0x72, 0x69, 0x7a, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x70, 0x62,
	0x2e, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x06, 0x70, 0x72, 0x69, 0x7a, 0x65, 0x73, 0x12, 0x22,
	0x0a, 0x0c, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x49, 0x64, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x69, 0x73, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x12, 0x2b,
	0x0a, 0x09, 0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x61, 0x73, 0x6b, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0d, 0x2e, 0x70, 0x62, 0x2e, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x09, 0x63, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x20, 0x0a, 0x0b, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x22, 0x20, 0x0a,
	0x0e, 0x41, 0x64, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22,
	0x92, 0x03, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x75, 0x62,
	0x6c, 0x69, 0x73, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6a, 0x6f, 0x69, 0x6e,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6a, 0x6f,
	0x69, 0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6e, 0x74, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6e, 0x74,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x61, 0x77, 0x61, 0x72, 0x64, 0x44,
	0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61,
	0x77, 0x61, 0x72, 0x64, 0x44, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x69, 0x73, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x69, 0x73, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x22, 0x0a, 0x0c,
	0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0c, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x22, 0x0a, 0x0c, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x73, 0x41, 0x6e, 0x6e, 0x6f, 0x75, 0x6e,
	0x63, 0x65, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x69, 0x73, 0x41, 0x6e, 0x6e,
	0x6f, 0x75, 0x6e, 0x63, 0x65, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f,
	0x72, 0x49, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x53, 0x70, 0x6f, 0x6e, 0x73,
	0x6f, 0x72, 0x49, 0x64, 0x22, 0x13, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x22, 0x1f, 0x0a, 0x0d, 0x44, 0x65, 0x6c,
	0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x10, 0x0a, 0x0e, 0x44, 0x65,
	0x6c, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x22, 0x23, 0x0a, 0x11,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65,
	0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x22, 0x3b, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x42,
	0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x25, 0x0a, 0x07, 0x6c, 0x6f, 0x74, 0x74, 0x65,
	0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x62, 0x2e, 0x4c, 0x6f,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x07, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x22, 0x94,
	0x04, 0x0a, 0x10, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79,
	0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x68, 0x75,
	0x6d, 0x62, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x12,
	0x20, 0x0a, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6a, 0x6f, 0x69, 0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6a, 0x6f, 0x69, 0x6e, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x12,
	0x24, 0x0a, 0x0d, 0x61, 0x77, 0x61, 0x72, 0x64, 0x44, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x77, 0x61, 0x72, 0x64, 0x44, 0x65, 0x61,
	0x64, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x73, 0x53, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x69, 0x73, 0x53, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x61, 0x73, 0x74, 0x49, 0x64, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6c, 0x61, 0x73, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a,
	0x0c, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0c, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x73, 0x41, 0x6e, 0x6e, 0x6f, 0x75,
	0x6e, 0x63, 0x65, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x69, 0x73, 0x41, 0x6e,
	0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x70, 0x6f, 0x6e, 0x73,
	0x6f, 0x72, 0x49, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x53, 0x70, 0x6f, 0x6e,
	0x73, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x3a, 0x0a, 0x11, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4c,
	0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x25, 0x0a, 0x07, 0x6c, 0x6f,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x62,
	0x2e, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x07, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72,
	0x79, 0x22, 0x41, 0x0a, 0x17, 0x53, 0x65, 0x74, 0x49, 0x73, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x3a, 0x0a, 0x18, 0x53, 0x65, 0x74, 0x49, 0x73, 0x53, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x73, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x69, 0x73, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x22, 0x3a, 0x0a, 0x10, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x85, 0x01, 0x0a,
	0x11, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x25, 0x0a, 0x07, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x62, 0x2e, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79,
	0x52, 0x07, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x12, 0x21, 0x0a, 0x06, 0x70, 0x72, 0x69,
	0x7a, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x70, 0x62, 0x2e, 0x50,
	0x72, 0x69, 0x7a, 0x65, 0x52, 0x06, 0x70, 0x72, 0x69, 0x7a, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0e,
	0x69, 0x73, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x74, 0x65, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x69, 0x73, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70,
	0x61, 0x74, 0x65, 0x64, 0x22, 0x31, 0x0a, 0x11, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x53,
	0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x70, 0x6f,
	0x6e, 0x73, 0x6f, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x53, 0x70,
	0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x90, 0x02, 0x0a, 0x12, 0x4c, 0x6f, 0x74, 0x74,
	0x65, 0x72, 0x79, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x70,
	0x70, 0x6c, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x61, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65,
	0x73, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x73,
	0x53, 0x68, 0x6f, 0x77, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x69, 0x73, 0x53, 0x68,
	0x6f, 0x77, 0x12, 0x16, 0x0a, 0x06, 0x71, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x71, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6e,
	0x70, 0x75, 0x74, 0x41, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6e, 0x70, 0x75,
	0x74, 0x41, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x42, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x42, 0x22, 0x38, 0x0a, 0x12, 0x41, 0x6e,
	0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71,
	0x12, 0x22, 0x0a, 0x0c, 0x41, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x41, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x22, 0x15, 0x0a, 0x13, 0x41, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65,
	0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x22, 0x34, 0x0a, 0x1a, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4c,
	0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x22, 0x3b, 0x0a, 0x1b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x1c, 0x0a, 0x09, 0x49, 0x73, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x49, 0x73, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x22, 0x3c,
	0x0a, 0x22, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x41, 0x6e, 0x64, 0x54, 0x6f, 0x64, 0x61,
	0x79, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x37, 0x0a, 0x23,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x41, 0x6e, 0x64, 0x54, 0x6f, 0x64, 0x61, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x59, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x03, 0x59, 0x65, 0x73, 0x22, 0x3f, 0x0a, 0x25, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79,
	0x41, 0x6e, 0x64, 0x54, 0x68, 0x69, 0x73, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x16,
	0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x3a, 0x0a, 0x26, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55,
	0x73, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72,
	0x79, 0x41, 0x6e, 0x64, 0x54, 0x68, 0x69, 0x73, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x10, 0x0a, 0x03, 0x59, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x59,
	0x65, 0x73, 0x22, 0x95, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72,
	0x79, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x66, 0x74, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52,
	0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x4c, 0x61,
	0x73, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x4c, 0x61, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x04, 0x50, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x49, 0x73,
	0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x49, 0x73, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x22, 0x3f, 0x0a, 0x1c, 0x47, 0x65,
	0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x66, 0x74, 0x65,
	0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1f, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x62, 0x2e, 0x4c, 0x6f,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x30, 0x0a, 0x16, 0x47,
	0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74,
	0x69, 0x63, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x89, 0x01,
	0x0a, 0x17, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x69, 0x73, 0x74, 0x69, 0x63, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2e, 0x0a, 0x12, 0x50, 0x61, 0x72,
	0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x57, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x57, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x19, 0x0a, 0x17, 0x47, 0x65, 0x74,
	0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x49,
	0x64, 0x52, 0x65, 0x71, 0x22, 0x32, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65,
	0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x16, 0x0a, 0x06, 0x4c, 0x61, 0x73, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x4c, 0x61, 0x73, 0x74, 0x49, 0x64, 0x22, 0x31, 0x0a, 0x11, 0x50, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a,
	0x09, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x22, 0x14, 0x0a, 0x12, 0x50,
	0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x22, 0xfd, 0x01, 0x0a, 0x05, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6c,
	0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x68, 0x75, 0x6d, 0x62,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x22, 0xb3, 0x01, 0x0a, 0x0b, 0x41, 0x64, 0x64, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x65,
	0x71, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x68,
	0x75, 0x6d, 0x62, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x72, 0x61,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x67, 0x72,
	0x61, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x0e, 0x0a, 0x0c, 0x41, 0x64, 0x64, 0x50, 0x72,
	0x69, 0x7a, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0xc6, 0x01, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c,
	0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x12, 0x14, 0x0a, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x11, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x1d, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x22, 0x0e, 0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x22, 0x21, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x42, 0x79,
	0x49, 0x64, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x33, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x7a,
	0x65, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1f, 0x0a, 0x05, 0x70, 0x72, 0x69,
	0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x70, 0x62, 0x2e, 0x50, 0x72,
	0x69, 0x7a, 0x65, 0x52, 0x05, 0x70, 0x72, 0x69, 0x7a, 0x65, 0x22, 0x58, 0x0a, 0x0e, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72,
	0x79, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65,
	0x72, 0x79, 0x49, 0x64, 0x22, 0x32, 0x0a, 0x0f, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72,
	0x69, 0x7a, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1f, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x7a, 0x65,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x70, 0x62, 0x2e, 0x50, 0x72, 0x69, 0x7a,
	0x65, 0x52, 0x05, 0x70, 0x72, 0x69, 0x7a, 0x65, 0x22, 0x95, 0x01, 0x0a, 0x0b, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x68, 0x75, 0x6d, 0x62, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x54, 0x68, 0x75, 0x6d, 0x62, 0x12, 0x14, 0x0a, 0x05,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x4c, 0x65, 0x76,
	0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x8e, 0x01, 0x0a, 0x0d, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x50, 0x72, 0x69, 0x7a,
	0x65, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64,
	0x12, 0x21, 0x0a, 0x06, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x09, 0x2e, 0x70, 0x62, 0x2e, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x06, 0x50, 0x72, 0x69,
	0x7a, 0x65, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x50, 0x61,
	0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x54, 0x69, 0x6d,
	0x65, 0x22, 0x9b, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79,
	0x50, 0x72, 0x69, 0x7a, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x61, 0x73, 0x74, 0x49, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6c, 0x61, 0x73, 0x74, 0x49, 0x64, 0x12, 0x20, 0x0a,
	0x0b, 0x69, 0x73, 0x41, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x69, 0x73, 0x41, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x64, 0x22,
	0x5b, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x50, 0x72, 0x69,
	0x7a, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x37, 0x0a, 0x0d, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x50, 0x72,
	0x69, 0x7a, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x70, 0x62, 0x2e,
	0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x73, 0x52, 0x0d, 0x4c,
	0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x73, 0x22, 0x52, 0x0a, 0x1a,
	0x41, 0x64, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63,
	0x69, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64,
	0x22, 0x2d, 0x0a, 0x1b, 0x41, 0x64, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x50, 0x61,
	0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22,
	0x77, 0x0a, 0x1d, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79,
	0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1c,
	0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x8c, 0x01, 0x0a, 0x14, 0x4c, 0x6f, 0x74,
	0x74, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x73, 0x57, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x57, 0x6f, 0x6e, 0x12, 0x18, 0x0a,
	0x07, 0x70, 0x72, 0x69, 0x7a, 0x65, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x70, 0x72, 0x69, 0x7a, 0x65, 0x49, 0x64, 0x22, 0x64, 0x0a, 0x1e, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x2c, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x70, 0x62, 0x2e, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63,
	0x69, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x3a, 0x0a,
	0x1a, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x4c,
	0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x6c,
	0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x22, 0x40, 0x0a, 0x1b, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x69, 0x7a, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x4c, 0x6f, 0x74, 0x74, 0x65,
	0x72, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x21, 0x0a, 0x06, 0x70, 0x72, 0x69, 0x7a,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x70, 0x62, 0x2e, 0x50, 0x72,
	0x69, 0x7a, 0x65, 0x52, 0x06, 0x70, 0x72, 0x69, 0x7a, 0x65, 0x73, 0x22, 0x45, 0x0a, 0x25, 0x47,
	0x65, 0x74, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x42, 0x79, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49,
	0x64, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79,
	0x49, 0x64, 0x22, 0x42, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69,
	0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x42, 0x79, 0x4c,
	0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x22, 0x4e, 0x0a, 0x16, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49,
	0x73, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x71,
	0x12, 0x1c, 0x0a, 0x09, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x41, 0x0a, 0x17, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49,
	0x73, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x26, 0x0a, 0x0e, 0x49, 0x73, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61,
	0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x49, 0x73, 0x50, 0x61, 0x72,
	0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x74, 0x65, 0x64, 0x22, 0x38, 0x0a, 0x1e, 0x47, 0x65, 0x74,
	0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x22, 0x5b, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74,
	0x69, 0x63, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x79, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x61, 0x79, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x65, 0x65, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x77, 0x65, 0x65, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0x3d, 0x0a, 0x23, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70,
	0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22,
	0x4a, 0x0a, 0x24, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61,
	0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x61, 0x72, 0x74, 0x69,
	0x63, 0x69, 0x70, 0x61, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x70,
	0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x74, 0x65, 0x64, 0x22, 0x49, 0x0a, 0x11, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x49, 0x73, 0x57, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x74, 0x74,
	0x65, 0x72, 0x79, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f, 0x74,
	0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x22, 0x2a, 0x0a, 0x12, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x73, 0x57, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05,
	0x69, 0x73, 0x57, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x73, 0x57,
	0x6f, 0x6e, 0x22, 0x53, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6c,
	0x61, 0x73, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6c, 0x61, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x86, 0x01, 0x0a, 0x07, 0x57, 0x6f, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x73, 0x57,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x57, 0x6f, 0x6e, 0x12,
	0x1f, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x09,
	0x2e, 0x70, 0x62, 0x2e, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x05, 0x70, 0x72, 0x69, 0x7a, 0x65,
	0x22, 0x31, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x1f, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x70, 0x62, 0x2e, 0x57, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x22, 0x2c, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x6e, 0x4c, 0x69, 0x73,
	0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x22, 0x2b, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x38,
	0x0a, 0x18, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x4c, 0x6f,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c,
	0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x22, 0x4e, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x22, 0x71, 0x0a, 0x08, 0x57, 0x6f, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x32, 0x12, 0x1f, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x09, 0x2e, 0x70, 0x62, 0x2e, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x05,
	0x70, 0x72, 0x69, 0x7a, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x57, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x57, 0x69, 0x6e, 0x6e,
	0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x22, 0x3d, 0x0a, 0x19, 0x47,
	0x65, 0x74, 0x57, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x4c, 0x6f, 0x74, 0x74, 0x65,
	0x72, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x62, 0x2e, 0x57, 0x6f, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x32, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x8f, 0x03, 0x0a, 0x09, 0x43,
	0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x53,
	0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x41, 0x70, 0x70, 0x6c,
	0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x4c, 0x69,
	0x6e, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x61, 0x67, 0x65, 0x4c, 0x69,
	0x6e, 0x6b, 0x12, 0x14, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65,
	0x50, 0x61, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x61, 0x67, 0x65,
	0x50, 0x61, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x56, 0x69,
	0x64, 0x65, 0x6f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x49, 0x64, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b,
	0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x20,
	0x0a, 0x0b, 0x43, 0x6f, 0x70, 0x79, 0x77, 0x72, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x43, 0x6f, 0x70, 0x79, 0x77, 0x72, 0x69, 0x74, 0x69, 0x6e, 0x67,
	0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x68, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x43, 0x68, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x2a, 0x0a, 0x10, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x4d, 0x75, 0x6c, 0x74,
	0x69, 0x70, 0x6c, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x49, 0x6e, 0x63, 0x72,
	0x65, 0x61, 0x73, 0x65, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x22, 0x9b, 0x01, 0x0a,
	0x15, 0x41, 0x64, 0x64, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72,
	0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x4c, 0x6f, 0x74, 0x74, 0x65,
	0x72, 0x79, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b,
	0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x2a,
	0x0a, 0x10, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70,
	0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x61,
	0x73, 0x65, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x22, 0x28, 0x0a, 0x16, 0x41, 0x64,
	0x64, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x32, 0xf0, 0x14, 0x0a, 0x07, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79,
	0x12, 0x33, 0x0a, 0x0a, 0x41, 0x64, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x12, 0x11,
	0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65,
	0x71, 0x1a, 0x12, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3c, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c,
	0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x12, 0x14, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x70,
	0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x33, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72,
	0x79, 0x12, 0x11, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c, 0x4c, 0x6f, 0x74,
	0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x4c,
	0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x42, 0x79, 0x49, 0x64, 0x12, 0x15, 0x2e, 0x70, 0x62, 0x2e,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65,
	0x71, 0x1a, 0x16, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72,
	0x79, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3c, 0x0a, 0x0d, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x12, 0x14, 0x2e, 0x70, 0x62, 0x2e,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71,
	0x1a, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4c, 0x6f, 0x74, 0x74,
	0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x51, 0x0a, 0x14, 0x53, 0x65, 0x74, 0x49, 0x73,
	0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x12,
	0x1b, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x74, 0x49, 0x73, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x70,
	0x62, 0x2e, 0x53, 0x65, 0x74, 0x49, 0x73, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4c,
	0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3c, 0x0a, 0x0d, 0x4c, 0x6f,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x14, 0x2e, 0x70, 0x62,
	0x2e, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x71, 0x1a, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x0e, 0x4c, 0x6f, 0x74, 0x74,
	0x65, 0x72, 0x79, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x12, 0x15, 0x2e, 0x70, 0x62, 0x2e,
	0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65,
	0x71, 0x1a, 0x16, 0x2e, 0x70, 0x62, 0x2e, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x53, 0x70,
	0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x42, 0x0a, 0x0f, 0x41, 0x6e, 0x6e,
	0x6f, 0x75, 0x6e, 0x63, 0x65, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x12, 0x16, 0x2e, 0x70,
	0x62, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x75, 0x6e,
	0x63, 0x65, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x5a, 0x0a,
	0x17, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x12, 0x1e, 0x2e, 0x70, 0x62, 0x2e, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4c, 0x6f,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x70, 0x62, 0x2e, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4c, 0x6f,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x72, 0x0a, 0x1f, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x74,
	0x74, 0x65, 0x72, 0x79, 0x41, 0x6e, 0x64, 0x54, 0x6f, 0x64, 0x61, 0x79, 0x12, 0x26, 0x2e, 0x70,
	0x62, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x41, 0x6e, 0x64, 0x54, 0x6f, 0x64, 0x61,
	0x79, 0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e, 0x70, 0x62, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55,
	0x73, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72,
	0x79, 0x41, 0x6e, 0x64, 0x54, 0x6f, 0x64, 0x61, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x7b, 0x0a,
	0x22, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x41, 0x6e, 0x64, 0x54, 0x68, 0x69, 0x73, 0x57,
	0x65, 0x65, 0x6b, 0x12, 0x29, 0x2e, 0x70, 0x62, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79,
	0x41, 0x6e, 0x64, 0x54, 0x68, 0x69, 0x73, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x2a,
	0x2e, 0x70, 0x62, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x41, 0x6e, 0x64, 0x54, 0x68,
	0x69, 0x73, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x12, 0x5d, 0x0a, 0x18, 0x47, 0x65,
	0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x66, 0x74, 0x65,
	0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x1f, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x4c,
	0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x66, 0x74, 0x65, 0x72, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74,
	0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x66, 0x74, 0x65, 0x72,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4e, 0x0a, 0x13, 0x47, 0x65, 0x74,
	0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63,
	0x12, 0x1a, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x70,
	0x62, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x69, 0x73, 0x74, 0x69, 0x63, 0x52, 0x65, 0x73, 0x70, 0x12, 0x51, 0x0a, 0x14, 0x47, 0x65, 0x74,
	0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x1b, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72,
	0x79, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x49, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1c,
	0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x4c, 0x69,
	0x73, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x0e,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x12, 0x15,
	0x2e, 0x70, 0x62, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x4c, 0x6f, 0x74, 0x74, 0x65,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x70, 0x62, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x69, 0x0a,
	0x1c, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x50, 0x72, 0x69, 0x7a, 0x65,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x23, 0x2e,
	0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x50, 0x72, 0x69,
	0x7a, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52,
	0x65, 0x71, 0x1a, 0x24, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x74, 0x74, 0x65,
	0x72, 0x79, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2d, 0x0a, 0x08, 0x41, 0x64, 0x64, 0x50,
	0x72, 0x69, 0x7a, 0x65, 0x12, 0x0f, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x50, 0x72, 0x69,
	0x7a, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x50, 0x72,
	0x69, 0x7a, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x36, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x70, 0x62, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x2d, 0x0a, 0x08, 0x44, 0x65, 0x6c, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x12, 0x0f, 0x2e, 0x70, 0x62,
	0x2e, 0x44, 0x65, 0x6c, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x70,
	0x62, 0x2e, 0x44, 0x65, 0x6c, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x39,
	0x0a, 0x0c, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x42, 0x79, 0x49, 0x64, 0x12, 0x13,
	0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x42, 0x79, 0x49, 0x64,
	0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x7a,
	0x65, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x36, 0x0a, 0x0b, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x70,
	0x62, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x5a, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x42, 0x79, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1e, 0x2e, 0x70,
	0x62, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79,
	0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x70,
	0x62, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79,
	0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x5a, 0x0a,
	0x17, 0x41, 0x64, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x74, 0x69,
	0x63, 0x69, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64,
	0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64,
	0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x63, 0x0a, 0x1a, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63,
	0x69, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63,
	0x69, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x70, 0x62, 0x2e,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72,
	0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x7b,
	0x0a, 0x22, 0x47, 0x65, 0x74, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x42, 0x79, 0x4c, 0x6f, 0x74, 0x74, 0x65,
	0x72, 0x79, 0x49, 0x64, 0x12, 0x29, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x72,
	0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x73, 0x42, 0x79, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x1a,
	0x2a, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x42, 0x79, 0x4c, 0x6f,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4e, 0x0a, 0x13, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x49, 0x73, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x74,
	0x65, 0x64, 0x12, 0x1a, 0x2e, 0x70, 0x62, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x73, 0x50,
	0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1b,
	0x2e, 0x70, 0x62, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x73, 0x50, 0x61, 0x72, 0x74, 0x69,
	0x63, 0x69, 0x70, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x66, 0x0a, 0x1b, 0x47,
	0x65, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x12, 0x22, 0x2e, 0x70, 0x62, 0x2e,
	0x47, 0x65, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65,
	0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x52, 0x65, 0x71, 0x1a, 0x23,
	0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4c,
	0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x75, 0x0a, 0x20, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x74, 0x69,
	0x63, 0x69, 0x70, 0x61, 0x74, 0x65, 0x64, 0x12, 0x27, 0x2e, 0x70, 0x62, 0x2e, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72,
	0x79, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x71,
	0x1a, 0x28, 0x2e, 0x70, 0x62, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63,
	0x69, 0x70, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x0e, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x49, 0x73, 0x57, 0x6f, 0x6e, 0x12, 0x15, 0x2e, 0x70,
	0x62, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x49, 0x73, 0x57, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x70, 0x62, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x73, 0x57, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x33, 0x0a, 0x0a, 0x47,
	0x65, 0x74, 0x57, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x11, 0x2e, 0x70, 0x62, 0x2e, 0x47,
	0x65, 0x74, 0x57, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x70,
	0x62, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x42, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x16, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x6e, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x70, 0x62,
	0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x54, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x42, 0x79, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1c, 0x2e,
	0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x4c,
	0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x70, 0x62,
	0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x4c, 0x6f, 0x74,
	0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4b, 0x0a, 0x12, 0x41, 0x64,
	0x64, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x12, 0x19, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x70, 0x62,
	0x2e, 0x41, 0x64, 0x64, 0x43, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_lottery_proto_rawDescOnce sync.Once
	file_lottery_proto_rawDescData = file_lottery_proto_rawDesc
)

func file_lottery_proto_rawDescGZIP() []byte {
	file_lottery_proto_rawDescOnce.Do(func() {
		file_lottery_proto_rawDescData = protoimpl.X.CompressGZIP(file_lottery_proto_rawDescData)
	})
	return file_lottery_proto_rawDescData
}

var file_lottery_proto_msgTypes = make([]protoimpl.MessageInfo, 77)
var file_lottery_proto_goTypes = []interface{}{
	(*Lottery)(nil),                                // 0: pb.Lottery
	(*AddLotteryReq)(nil),                          // 1: pb.AddLotteryReq
	(*AddLotteryResp)(nil),                         // 2: pb.AddLotteryResp
	(*UpdateLotteryReq)(nil),                       // 3: pb.UpdateLotteryReq
	(*UpdateLotteryResp)(nil),                      // 4: pb.UpdateLotteryResp
	(*DelLotteryReq)(nil),                          // 5: pb.DelLotteryReq
	(*DelLotteryResp)(nil),                         // 6: pb.DelLotteryResp
	(*GetLotteryByIdReq)(nil),                      // 7: pb.GetLotteryByIdReq
	(*GetLotteryByIdResp)(nil),                     // 8: pb.GetLotteryByIdResp
	(*SearchLotteryReq)(nil),                       // 9: pb.SearchLotteryReq
	(*SearchLotteryResp)(nil),                      // 10: pb.SearchLotteryResp
	(*SetIsSelectedLotteryReq)(nil),                // 11: pb.SetIsSelectedLotteryReq
	(*SetIsSelectedLotteryResp)(nil),               // 12: pb.SetIsSelectedLotteryResp
	(*LotteryDetailReq)(nil),                       // 13: pb.LotteryDetailReq
	(*LotteryDetailResp)(nil),                      // 14: pb.LotteryDetailResp
	(*LotterySponsorReq)(nil),                      // 15: pb.LotterySponsorReq
	(*LotterySponsorResp)(nil),                     // 16: pb.LotterySponsorResp
	(*AnnounceLotteryReq)(nil),                     // 17: pb.AnnounceLotteryReq
	(*AnnounceLotteryResp)(nil),                    // 18: pb.AnnounceLotteryResp
	(*CheckUserCreatedLotteryReq)(nil),             // 19: pb.CheckUserCreatedLotteryReq
	(*CheckUserCreatedLotteryResp)(nil),            // 20: pb.CheckUserCreatedLotteryResp
	(*CheckUserCreatedLotteryAndTodayReq)(nil),     // 21: pb.CheckUserCreatedLotteryAndTodayReq
	(*CheckUserCreatedLotteryAndTodayResp)(nil),    // 22: pb.CheckUserCreatedLotteryAndTodayResp
	(*CheckUserCreatedLotteryAndThisWeekReq)(nil),  // 23: pb.CheckUserCreatedLotteryAndThisWeekReq
	(*CheckUserCreatedLotteryAndThisWeekResp)(nil), // 24: pb.CheckUserCreatedLotteryAndThisWeekResp
	(*GetLotteryListAfterLoginReq)(nil),            // 25: pb.GetLotteryListAfterLoginReq
	(*GetLotteryListAfterLoginResp)(nil),           // 26: pb.GetLotteryListAfterLoginResp
	(*GetLotteryStatisticReq)(nil),                 // 27: pb.GetLotteryStatisticReq
	(*GetLotteryStatisticResp)(nil),                // 28: pb.GetLotteryStatisticResp
	(*GetLotteryListLastIdReq)(nil),                // 29: pb.GetLotteryListLastIdReq
	(*GetLotteryListLastIdResp)(nil),               // 30: pb.GetLotteryListLastIdResp
	(*PublishLotteryReq)(nil),                      // 31: pb.PublishLotteryReq
	(*PublishLotteryResp)(nil),                     // 32: pb.PublishLotteryResp
	(*Prize)(nil),                                  // 33: pb.Prize
	(*AddPrizeReq)(nil),                            // 34: pb.AddPrizeReq
	(*AddPrizeResp)(nil),                           // 35: pb.AddPrizeResp
	(*UpdatePrizeReq)(nil),                         // 36: pb.UpdatePrizeReq
	(*UpdatePrizeResp)(nil),                        // 37: pb.UpdatePrizeResp
	(*DelPrizeReq)(nil),                            // 38: pb.DelPrizeReq
	(*DelPrizeResp)(nil),                           // 39: pb.DelPrizeResp
	(*GetPrizeByIdReq)(nil),                        // 40: pb.GetPrizeByIdReq
	(*GetPrizeByIdResp)(nil),                       // 41: pb.GetPrizeByIdResp
	(*SearchPrizeReq)(nil),                         // 42: pb.SearchPrizeReq
	(*SearchPrizeResp)(nil),                        // 43: pb.SearchPrizeResp
	(*CreatePrize)(nil),                            // 44: pb.CreatePrize
	(*LotteryPrizes)(nil),                          // 45: pb.LotteryPrizes
	(*GetLotteryPrizesListByUserIdReq)(nil),        // 46: pb.GetLotteryPrizesListByUserIdReq
	(*GetLotteryPrizesListByUserIdResp)(nil),       // 47: pb.GetLotteryPrizesListByUserIdResp
	(*AddLotteryParticipationReq)(nil),             // 48: pb.AddLotteryParticipationReq
	(*AddLotteryParticipationResp)(nil),            // 49: pb.AddLotteryParticipationResp
	(*SearchLotteryParticipationReq)(nil),          // 50: pb.SearchLotteryParticipationReq
	(*LotteryParticipation)(nil),                   // 51: pb.LotteryParticipation
	(*SearchLotteryParticipationResp)(nil),         // 52: pb.SearchLotteryParticipationResp
	(*GetPrizeListByLotteryIdReq)(nil),             // 53: pb.GetPrizeListByLotteryIdReq
	(*GetPrizeListByLotteryIdResp)(nil),            // 54: pb.GetPrizeListByLotteryIdResp
	(*GetParticipationUserIdsByLotteryIdReq)(nil),  // 55: pb.GetParticipationUserIdsByLotteryIdReq
	(*GetParticipationUserIdsByLotteryIdResp)(nil), // 56: pb.GetParticipationUserIdsByLotteryIdResp
	(*CheckIsParticipatedReq)(nil),                 // 57: pb.CheckIsParticipatedReq
	(*CheckIsParticipatedResp)(nil),                // 58: pb.CheckIsParticipatedResp
	(*GetSelectedLotteryStatisticReq)(nil),         // 59: pb.GetSelectedLotteryStatisticReq
	(*GetSelectedLotteryStatisticResp)(nil),        // 60: pb.GetSelectedLotteryStatisticResp
	(*CheckSelectedLotteryParticipatedReq)(nil),    // 61: pb.CheckSelectedLotteryParticipatedReq
	(*CheckSelectedLotteryParticipatedResp)(nil),   // 62: pb.CheckSelectedLotteryParticipatedResp
	(*CheckUserIsWonReq)(nil),                      // 63: pb.CheckUserIsWonReq
	(*CheckUserIsWonResp)(nil),                     // 64: pb.CheckUserIsWonResp
	(*GetWonListReq)(nil),                          // 65: pb.GetWonListReq
	(*WonList)(nil),                                // 66: pb.WonList
	(*GetWonListResp)(nil),                         // 67: pb.GetWonListResp
	(*GetWonListCountReq)(nil),                     // 68: pb.GetWonListCountReq
	(*GetWonListCountResp)(nil),                    // 69: pb.GetWonListCountResp
	(*GetWonListByLotteryIdReq)(nil),               // 70: pb.GetWonListByLotteryIdReq
	(*UserInfo)(nil),                               // 71: pb.UserInfo
	(*WonList2)(nil),                               // 72: pb.WonList2
	(*GetWonListByLotteryIdResp)(nil),              // 73: pb.GetWonListByLotteryIdResp
	(*ClockTask)(nil),                              // 74: pb.ClockTask
	(*AddClockTaskRecordReq)(nil),                  // 75: pb.AddClockTaskRecordReq
	(*AddClockTaskRecordResp)(nil),                 // 76: pb.AddClockTaskRecordResp
}
var file_lottery_proto_depIdxs = []int32{
	33, // 0: pb.AddLotteryReq.prizes:type_name -> pb.Prize
	74, // 1: pb.AddLotteryReq.clockTask:type_name -> pb.ClockTask
	0,  // 2: pb.GetLotteryByIdResp.lottery:type_name -> pb.Lottery
	0,  // 3: pb.SearchLotteryResp.lottery:type_name -> pb.Lottery
	0,  // 4: pb.LotteryDetailResp.lottery:type_name -> pb.Lottery
	33, // 5: pb.LotteryDetailResp.prizes:type_name -> pb.Prize
	0,  // 6: pb.GetLotteryListAfterLoginResp.list:type_name -> pb.Lottery
	33, // 7: pb.GetPrizeByIdResp.prize:type_name -> pb.Prize
	33, // 8: pb.SearchPrizeResp.prize:type_name -> pb.Prize
	33, // 9: pb.LotteryPrizes.Prizes:type_name -> pb.Prize
	45, // 10: pb.GetLotteryPrizesListByUserIdResp.LotteryPrizes:type_name -> pb.LotteryPrizes
	51, // 11: pb.SearchLotteryParticipationResp.list:type_name -> pb.LotteryParticipation
	33, // 12: pb.GetPrizeListByLotteryIdResp.prizes:type_name -> pb.Prize
	33, // 13: pb.WonList.prize:type_name -> pb.Prize
	66, // 14: pb.GetWonListResp.list:type_name -> pb.WonList
	33, // 15: pb.WonList2.prize:type_name -> pb.Prize
	71, // 16: pb.WonList2.users:type_name -> pb.UserInfo
	72, // 17: pb.GetWonListByLotteryIdResp.list:type_name -> pb.WonList2
	1,  // 18: pb.lottery.AddLottery:input_type -> pb.AddLotteryReq
	3,  // 19: pb.lottery.UpdateLottery:input_type -> pb.UpdateLotteryReq
	5,  // 20: pb.lottery.DelLottery:input_type -> pb.DelLotteryReq
	7,  // 21: pb.lottery.GetLotteryById:input_type -> pb.GetLotteryByIdReq
	9,  // 22: pb.lottery.SearchLottery:input_type -> pb.SearchLotteryReq
	11, // 23: pb.lottery.SetIsSelectedLottery:input_type -> pb.SetIsSelectedLotteryReq
	13, // 24: pb.lottery.LotteryDetail:input_type -> pb.LotteryDetailReq
	15, // 25: pb.lottery.LotterySponsor:input_type -> pb.LotterySponsorReq
	17, // 26: pb.lottery.AnnounceLottery:input_type -> pb.AnnounceLotteryReq
	19, // 27: pb.lottery.CheckUserCreatedLottery:input_type -> pb.CheckUserCreatedLotteryReq
	21, // 28: pb.lottery.CheckUserCreatedLotteryAndToday:input_type -> pb.CheckUserCreatedLotteryAndTodayReq
	23, // 29: pb.lottery.CheckUserCreatedLotteryAndThisWeek:input_type -> pb.CheckUserCreatedLotteryAndThisWeekReq
	25, // 30: pb.lottery.GetLotteryListAfterLogin:input_type -> pb.GetLotteryListAfterLoginReq
	27, // 31: pb.lottery.GetLotteryStatistic:input_type -> pb.GetLotteryStatisticReq
	29, // 32: pb.lottery.GetLotteryListLastId:input_type -> pb.GetLotteryListLastIdReq
	31, // 33: pb.lottery.PublishLottery:input_type -> pb.PublishLotteryReq
	46, // 34: pb.lottery.GetLotteryPrizesListByUserId:input_type -> pb.GetLotteryPrizesListByUserIdReq
	34, // 35: pb.lottery.AddPrize:input_type -> pb.AddPrizeReq
	36, // 36: pb.lottery.UpdatePrize:input_type -> pb.UpdatePrizeReq
	38, // 37: pb.lottery.DelPrize:input_type -> pb.DelPrizeReq
	40, // 38: pb.lottery.GetPrizeById:input_type -> pb.GetPrizeByIdReq
	42, // 39: pb.lottery.SearchPrize:input_type -> pb.SearchPrizeReq
	53, // 40: pb.lottery.GetPrizeListByLotteryId:input_type -> pb.GetPrizeListByLotteryIdReq
	48, // 41: pb.lottery.AddLotteryParticipation:input_type -> pb.AddLotteryParticipationReq
	50, // 42: pb.lottery.SearchLotteryParticipation:input_type -> pb.SearchLotteryParticipationReq
	55, // 43: pb.lottery.GetParticipationUserIdsByLotteryId:input_type -> pb.GetParticipationUserIdsByLotteryIdReq
	57, // 44: pb.lottery.CheckIsParticipated:input_type -> pb.CheckIsParticipatedReq
	59, // 45: pb.lottery.GetSelectedLotteryStatistic:input_type -> pb.GetSelectedLotteryStatisticReq
	61, // 46: pb.lottery.CheckSelectedLotteryParticipated:input_type -> pb.CheckSelectedLotteryParticipatedReq
	63, // 47: pb.lottery.CheckUserIsWon:input_type -> pb.CheckUserIsWonReq
	65, // 48: pb.lottery.GetWonList:input_type -> pb.GetWonListReq
	68, // 49: pb.lottery.GetWonListCount:input_type -> pb.GetWonListCountReq
	70, // 50: pb.lottery.GetWonListByLotteryId:input_type -> pb.GetWonListByLotteryIdReq
	75, // 51: pb.lottery.AddClockTaskRecord:input_type -> pb.AddClockTaskRecordReq
	2,  // 52: pb.lottery.AddLottery:output_type -> pb.AddLotteryResp
	4,  // 53: pb.lottery.UpdateLottery:output_type -> pb.UpdateLotteryResp
	6,  // 54: pb.lottery.DelLottery:output_type -> pb.DelLotteryResp
	8,  // 55: pb.lottery.GetLotteryById:output_type -> pb.GetLotteryByIdResp
	10, // 56: pb.lottery.SearchLottery:output_type -> pb.SearchLotteryResp
	12, // 57: pb.lottery.SetIsSelectedLottery:output_type -> pb.SetIsSelectedLotteryResp
	14, // 58: pb.lottery.LotteryDetail:output_type -> pb.LotteryDetailResp
	16, // 59: pb.lottery.LotterySponsor:output_type -> pb.LotterySponsorResp
	18, // 60: pb.lottery.AnnounceLottery:output_type -> pb.AnnounceLotteryResp
	20, // 61: pb.lottery.CheckUserCreatedLottery:output_type -> pb.CheckUserCreatedLotteryResp
	22, // 62: pb.lottery.CheckUserCreatedLotteryAndToday:output_type -> pb.CheckUserCreatedLotteryAndTodayResp
	24, // 63: pb.lottery.CheckUserCreatedLotteryAndThisWeek:output_type -> pb.CheckUserCreatedLotteryAndThisWeekResp
	26, // 64: pb.lottery.GetLotteryListAfterLogin:output_type -> pb.GetLotteryListAfterLoginResp
	28, // 65: pb.lottery.GetLotteryStatistic:output_type -> pb.GetLotteryStatisticResp
	30, // 66: pb.lottery.GetLotteryListLastId:output_type -> pb.GetLotteryListLastIdResp
	32, // 67: pb.lottery.PublishLottery:output_type -> pb.PublishLotteryResp
	47, // 68: pb.lottery.GetLotteryPrizesListByUserId:output_type -> pb.GetLotteryPrizesListByUserIdResp
	35, // 69: pb.lottery.AddPrize:output_type -> pb.AddPrizeResp
	37, // 70: pb.lottery.UpdatePrize:output_type -> pb.UpdatePrizeResp
	39, // 71: pb.lottery.DelPrize:output_type -> pb.DelPrizeResp
	41, // 72: pb.lottery.GetPrizeById:output_type -> pb.GetPrizeByIdResp
	43, // 73: pb.lottery.SearchPrize:output_type -> pb.SearchPrizeResp
	54, // 74: pb.lottery.GetPrizeListByLotteryId:output_type -> pb.GetPrizeListByLotteryIdResp
	49, // 75: pb.lottery.AddLotteryParticipation:output_type -> pb.AddLotteryParticipationResp
	52, // 76: pb.lottery.SearchLotteryParticipation:output_type -> pb.SearchLotteryParticipationResp
	56, // 77: pb.lottery.GetParticipationUserIdsByLotteryId:output_type -> pb.GetParticipationUserIdsByLotteryIdResp
	58, // 78: pb.lottery.CheckIsParticipated:output_type -> pb.CheckIsParticipatedResp
	60, // 79: pb.lottery.GetSelectedLotteryStatistic:output_type -> pb.GetSelectedLotteryStatisticResp
	62, // 80: pb.lottery.CheckSelectedLotteryParticipated:output_type -> pb.CheckSelectedLotteryParticipatedResp
	64, // 81: pb.lottery.CheckUserIsWon:output_type -> pb.CheckUserIsWonResp
	67, // 82: pb.lottery.GetWonList:output_type -> pb.GetWonListResp
	69, // 83: pb.lottery.GetWonListCount:output_type -> pb.GetWonListCountResp
	73, // 84: pb.lottery.GetWonListByLotteryId:output_type -> pb.GetWonListByLotteryIdResp
	76, // 85: pb.lottery.AddClockTaskRecord:output_type -> pb.AddClockTaskRecordResp
	52, // [52:86] is the sub-list for method output_type
	18, // [18:52] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_lottery_proto_init() }
func file_lottery_proto_init() {
	if File_lottery_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_lottery_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Lottery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddLotteryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddLotteryResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLotteryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLotteryResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelLotteryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelLotteryResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotteryByIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotteryByIdResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchLotteryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchLotteryResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetIsSelectedLotteryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetIsSelectedLotteryResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LotteryDetailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LotteryDetailResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LotterySponsorReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LotterySponsorResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnounceLotteryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnounceLotteryResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckUserCreatedLotteryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckUserCreatedLotteryResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckUserCreatedLotteryAndTodayReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckUserCreatedLotteryAndTodayResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckUserCreatedLotteryAndThisWeekReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckUserCreatedLotteryAndThisWeekResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotteryListAfterLoginReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotteryListAfterLoginResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotteryStatisticReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotteryStatisticResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotteryListLastIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotteryListLastIdResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PublishLotteryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PublishLotteryResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Prize); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddPrizeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddPrizeResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePrizeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePrizeResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelPrizeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelPrizeResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPrizeByIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPrizeByIdResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchPrizeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchPrizeResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePrize); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LotteryPrizes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotteryPrizesListByUserIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLotteryPrizesListByUserIdResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddLotteryParticipationReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddLotteryParticipationResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchLotteryParticipationReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LotteryParticipation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchLotteryParticipationResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPrizeListByLotteryIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPrizeListByLotteryIdResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetParticipationUserIdsByLotteryIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetParticipationUserIdsByLotteryIdResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckIsParticipatedReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckIsParticipatedResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSelectedLotteryStatisticReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSelectedLotteryStatisticResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckSelectedLotteryParticipatedReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckSelectedLotteryParticipatedResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckUserIsWonReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckUserIsWonResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWonListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WonList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWonListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWonListCountReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWonListCountResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWonListByLotteryIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WonList2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWonListByLotteryIdResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClockTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddClockTaskRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_lottery_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddClockTaskRecordResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_lottery_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   77,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_lottery_proto_goTypes,
		DependencyIndexes: file_lottery_proto_depIdxs,
		MessageInfos:      file_lottery_proto_msgTypes,
	}.Build()
	File_lottery_proto = out.File
	file_lottery_proto_rawDesc = nil
	file_lottery_proto_goTypes = nil
	file_lottery_proto_depIdxs = nil
}
