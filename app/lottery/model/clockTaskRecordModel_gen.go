// Code generated by goctl. DO NOT EDIT.

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
	"looklook/common/globalkey"
)

var (
	clockTaskRecordFieldNames          = builder.RawFieldNames(&ClockTaskRecord{})
	clockTaskRecordRows                = strings.Join(clockTaskRecordFieldNames, ",")
	clockTaskRecordRowsExpectAutoSet   = strings.Join(stringx.Remove(clockTaskRecordFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), ",")
	clockTaskRecordRowsWithPlaceHolder = strings.Join(stringx.Remove(clockTaskRecordFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), "=?,") + "=?"

	cacheLotteryClockTaskRecordIdPrefix = "cache:lottery:clockTaskRecord:id:"
)

type (
	clockTaskRecordModel interface {
		Insert(ctx context.Context, data *ClockTaskRecord) (sql.Result, error)
		TransInsert(ctx context.Context, session sqlx.Session, data *ClockTaskRecord) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ClockTaskRecord, error)
		Update(ctx context.Context, data *ClockTaskRecord) error
		List(ctx context.Context, page, limit int64) ([]*ClockTaskRecord, error)
		TransUpdate(ctx context.Context, session sqlx.Session, data *ClockTaskRecord) error
		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		SelectBuilder() squirrel.SelectBuilder
		FindSum(ctx context.Context, sumBuilder squirrel.SelectBuilder, field string) (float64, error)
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder, field string) (int64, error)
		FindAll(ctx context.Context, rowBuilder squirrel.SelectBuilder, orderBy string) ([]*ClockTaskRecord, error)
		FindPageListByPage(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*ClockTaskRecord, error)
		FindPageListByPageWithTotal(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*ClockTaskRecord, int64, error)
		FindPageListByIdDESC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*ClockTaskRecord, error)
		FindPageListByIdASC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*ClockTaskRecord, error)
		Delete(ctx context.Context, id int64) error
	}

	defaultClockTaskRecordModel struct {
		sqlc.CachedConn
		table string
	}

	ClockTaskRecord struct {
		Id               int64     `db:"id"`
		LotteryId        int64     `db:"lottery_id"`        // 抽奖ID
		ClockTaskId      int64     `db:"clock_task_id"`     // 打卡任务ID
		UserId           int64     `db:"user_id"`           // 用户id
		IncreaseMultiple int64     `db:"increase_multiple"` // 概率增加倍数
		DelState         int64     `db:"del_state"`
		CreateTime       time.Time `db:"create_time"`
		UpdateTime       time.Time `db:"update_time"`
	}
)

func newClockTaskRecordModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultClockTaskRecordModel {
	return &defaultClockTaskRecordModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`clock_task_record`",
	}
}

func (m *defaultClockTaskRecordModel) Delete(ctx context.Context, id int64) error {
	lotteryClockTaskRecordIdKey := fmt.Sprintf("%s%v", cacheLotteryClockTaskRecordIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		return conn.ExecCtx(ctx, query, id)
	}, lotteryClockTaskRecordIdKey)
	return err
}

func (m *defaultClockTaskRecordModel) FindOne(ctx context.Context, id int64) (*ClockTaskRecord, error) {
	lotteryClockTaskRecordIdKey := fmt.Sprintf("%s%v", cacheLotteryClockTaskRecordIdPrefix, id)
	var resp ClockTaskRecord
	err := m.QueryRowCtx(ctx, &resp, lotteryClockTaskRecordIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", clockTaskRecordRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultClockTaskRecordModel) Insert(ctx context.Context, data *ClockTaskRecord) (sql.Result, error) {
	lotteryClockTaskRecordIdKey := fmt.Sprintf("%s%v", cacheLotteryClockTaskRecordIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?)", m.table, clockTaskRecordRowsExpectAutoSet)
		return conn.ExecCtx(ctx, query, data.LotteryId, data.ClockTaskId, data.UserId, data.IncreaseMultiple, data.DelState)
	}, lotteryClockTaskRecordIdKey)
	return ret, err
}

func (m *defaultClockTaskRecordModel) TransInsert(ctx context.Context, session sqlx.Session, data *ClockTaskRecord) (sql.Result, error) {
	lotteryClockTaskRecordIdKey := fmt.Sprintf("%s%v", cacheLotteryClockTaskRecordIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?)", m.table, clockTaskRecordRowsExpectAutoSet)
		return session.ExecCtx(ctx, query, data.LotteryId, data.ClockTaskId, data.UserId, data.IncreaseMultiple, data.DelState)
	}, lotteryClockTaskRecordIdKey)
	return ret, err
}
func (m *defaultClockTaskRecordModel) Update(ctx context.Context, data *ClockTaskRecord) error {
	lotteryClockTaskRecordIdKey := fmt.Sprintf("%s%v", cacheLotteryClockTaskRecordIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, clockTaskRecordRowsWithPlaceHolder)
		return conn.ExecCtx(ctx, query, data.LotteryId, data.ClockTaskId, data.UserId, data.IncreaseMultiple, data.DelState, data.Id)
	}, lotteryClockTaskRecordIdKey)
	return err
}

func (m *defaultClockTaskRecordModel) TransUpdate(ctx context.Context, session sqlx.Session, data *ClockTaskRecord) error {
	lotteryClockTaskRecordIdKey := fmt.Sprintf("%s%v", cacheLotteryClockTaskRecordIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, clockTaskRecordRowsWithPlaceHolder)
		return session.ExecCtx(ctx, query, data.LotteryId, data.ClockTaskId, data.UserId, data.IncreaseMultiple, data.DelState, data.Id)
	}, lotteryClockTaskRecordIdKey)
	return err
}

func (m *defaultClockTaskRecordModel) List(ctx context.Context, page, limit int64) ([]*ClockTaskRecord, error) {
	query := fmt.Sprintf("select %s from %s limit ?,?", clockTaskRecordRows, m.table)
	var resp []*ClockTaskRecord
	//err := m.conn.QueryRowsCtx(ctx, &resp, query, (page-1)*limit, limit)
	err := m.QueryRowsNoCacheCtx(ctx, &resp, query, (page-1)*limit, limit)
	return resp, err
}

func (m *defaultClockTaskRecordModel) Trans(ctx context.Context, fn func(ctx context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, func(ctx context.Context, session sqlx.Session) error {
		return fn(ctx, session)
	})
}

func (m *defaultClockTaskRecordModel) FindSum(ctx context.Context, builder squirrel.SelectBuilder, field string) (float64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindSum Least One Field"), "FindSum Least One Field")
	}

	builder = builder.Columns("IFNULL(SUM(" + field + "),0)")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp float64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultClockTaskRecordModel) FindCount(ctx context.Context, builder squirrel.SelectBuilder, field string) (int64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindCount Least One Field"), "FindCount Least One Field")
	}

	builder = builder.Columns("COUNT(" + field + ")")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultClockTaskRecordModel) FindAll(ctx context.Context, builder squirrel.SelectBuilder, orderBy string) ([]*ClockTaskRecord, error) {

	builder = builder.Columns(clockTaskRecordRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ClockTaskRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultClockTaskRecordModel) FindPageListByPage(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*ClockTaskRecord, error) {

	builder = builder.Columns(clockTaskRecordRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ClockTaskRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultClockTaskRecordModel) FindPageListByPageWithTotal(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*ClockTaskRecord, int64, error) {

	total, err := m.FindCount(ctx, builder, "id")
	if err != nil {
		return nil, 0, err
	}

	builder = builder.Columns(clockTaskRecordRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, total, err
	}

	var resp []*ClockTaskRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, total, nil
	default:
		return nil, total, err
	}
}

func (m *defaultClockTaskRecordModel) FindPageListByIdDESC(ctx context.Context, builder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*ClockTaskRecord, error) {

	builder = builder.Columns(clockTaskRecordRows)

	if preMinId > 0 {
		builder = builder.Where(" id < ? ", preMinId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id DESC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ClockTaskRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultClockTaskRecordModel) FindPageListByIdASC(ctx context.Context, builder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*ClockTaskRecord, error) {

	builder = builder.Columns(clockTaskRecordRows)

	if preMaxId > 0 {
		builder = builder.Where(" id > ? ", preMaxId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id ASC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ClockTaskRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultClockTaskRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select().From(m.table)
}

func (m *defaultClockTaskRecordModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheLotteryClockTaskRecordIdPrefix, primary)
}

func (m *defaultClockTaskRecordModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", clockTaskRecordRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultClockTaskRecordModel) tableName() string {
	return m.table
}
