// Code generated by goctl. DO NOT EDIT.

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
	"looklook/common/globalkey"
)

var (
	taskRecordFieldNames          = builder.RawFieldNames(&TaskRecord{})
	taskRecordRows                = strings.Join(taskRecordFieldNames, ",")
	taskRecordRowsExpectAutoSet   = strings.Join(stringx.Remove(taskRecordFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), ",")
	taskRecordRowsWithPlaceHolder = strings.Join(stringx.Remove(taskRecordFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), "=?,") + "=?"

	cacheCheckinTaskRecordIdPrefix = "cache:checkin:taskRecord:id:"
)

type (
	taskRecordModel interface {
		Insert(ctx context.Context, data *TaskRecord) (sql.Result, error)
		TransInsert(ctx context.Context, session sqlx.Session, data *TaskRecord) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*TaskRecord, error)
		Update(ctx context.Context, data *TaskRecord) error
		List(ctx context.Context, page, limit int64) ([]*TaskRecord, error)
		TransUpdate(ctx context.Context, session sqlx.Session, data *TaskRecord) error
		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		SelectBuilder() squirrel.SelectBuilder
		FindSum(ctx context.Context, sumBuilder squirrel.SelectBuilder, field string) (float64, error)
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder, field string) (int64, error)
		FindAll(ctx context.Context, rowBuilder squirrel.SelectBuilder, orderBy string) ([]*TaskRecord, error)
		FindPageListByPage(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*TaskRecord, error)
		FindPageListByPageWithTotal(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*TaskRecord, int64, error)
		FindPageListByIdDESC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*TaskRecord, error)
		FindPageListByIdASC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*TaskRecord, error)
		Delete(ctx context.Context, id int64) error
	}

	defaultTaskRecordModel struct {
		sqlc.CachedConn
		table string
	}

	TaskRecord struct {
		Id         int64     `db:"id"`
		Type       int64     `db:"type"`
		UserId     int64     `db:"user_id"`
		TaskId     int64     `db:"task_id"`
		IsFinished int64     `db:"isFinished"` // 0 means not completed, 1 means completed
		CreateTime time.Time `db:"create_time"`
	}
)

func newTaskRecordModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultTaskRecordModel {
	return &defaultTaskRecordModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`task_record`",
	}
}

func (m *defaultTaskRecordModel) Delete(ctx context.Context, id int64) error {
	checkinTaskRecordIdKey := fmt.Sprintf("%s%v", cacheCheckinTaskRecordIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		return conn.ExecCtx(ctx, query, id)
	}, checkinTaskRecordIdKey)
	return err
}

func (m *defaultTaskRecordModel) FindOne(ctx context.Context, id int64) (*TaskRecord, error) {
	checkinTaskRecordIdKey := fmt.Sprintf("%s%v", cacheCheckinTaskRecordIdPrefix, id)
	var resp TaskRecord
	err := m.QueryRowCtx(ctx, &resp, checkinTaskRecordIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", taskRecordRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultTaskRecordModel) Insert(ctx context.Context, data *TaskRecord) (sql.Result, error) {
	checkinTaskRecordIdKey := fmt.Sprintf("%s%v", cacheCheckinTaskRecordIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, taskRecordRowsExpectAutoSet)
		return conn.ExecCtx(ctx, query, data.Type, data.UserId, data.TaskId, data.IsFinished)
	}, checkinTaskRecordIdKey)
	return ret, err
}

func (m *defaultTaskRecordModel) TransInsert(ctx context.Context, session sqlx.Session, data *TaskRecord) (sql.Result, error) {
	checkinTaskRecordIdKey := fmt.Sprintf("%s%v", cacheCheckinTaskRecordIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, taskRecordRowsExpectAutoSet)
		return session.ExecCtx(ctx, query, data.Type, data.UserId, data.TaskId, data.IsFinished)
	}, checkinTaskRecordIdKey)
	return ret, err
}
func (m *defaultTaskRecordModel) Update(ctx context.Context, data *TaskRecord) error {
	checkinTaskRecordIdKey := fmt.Sprintf("%s%v", cacheCheckinTaskRecordIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, taskRecordRowsWithPlaceHolder)
		return conn.ExecCtx(ctx, query, data.Type, data.UserId, data.TaskId, data.IsFinished, data.Id)
	}, checkinTaskRecordIdKey)
	return err
}

func (m *defaultTaskRecordModel) TransUpdate(ctx context.Context, session sqlx.Session, data *TaskRecord) error {
	checkinTaskRecordIdKey := fmt.Sprintf("%s%v", cacheCheckinTaskRecordIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, taskRecordRowsWithPlaceHolder)
		return session.ExecCtx(ctx, query, data.Type, data.UserId, data.TaskId, data.IsFinished, data.Id)
	}, checkinTaskRecordIdKey)
	return err
}

func (m *defaultTaskRecordModel) List(ctx context.Context, page, limit int64) ([]*TaskRecord, error) {
	query := fmt.Sprintf("select %s from %s limit ?,?", taskRecordRows, m.table)
	var resp []*TaskRecord
	//err := m.conn.QueryRowsCtx(ctx, &resp, query, (page-1)*limit, limit)
	err := m.QueryRowsNoCacheCtx(ctx, &resp, query, (page-1)*limit, limit)
	return resp, err
}

func (m *defaultTaskRecordModel) Trans(ctx context.Context, fn func(ctx context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, func(ctx context.Context, session sqlx.Session) error {
		return fn(ctx, session)
	})
}

func (m *defaultTaskRecordModel) FindSum(ctx context.Context, builder squirrel.SelectBuilder, field string) (float64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindSum Least One Field"), "FindSum Least One Field")
	}

	builder = builder.Columns("IFNULL(SUM(" + field + "),0)")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp float64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultTaskRecordModel) FindCount(ctx context.Context, builder squirrel.SelectBuilder, field string) (int64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindCount Least One Field"), "FindCount Least One Field")
	}

	builder = builder.Columns("COUNT(" + field + ")")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultTaskRecordModel) FindAll(ctx context.Context, builder squirrel.SelectBuilder, orderBy string) ([]*TaskRecord, error) {

	builder = builder.Columns(taskRecordRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*TaskRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultTaskRecordModel) FindPageListByPage(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*TaskRecord, error) {

	builder = builder.Columns(taskRecordRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*TaskRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultTaskRecordModel) FindPageListByPageWithTotal(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*TaskRecord, int64, error) {

	total, err := m.FindCount(ctx, builder, "id")
	if err != nil {
		return nil, 0, err
	}

	builder = builder.Columns(taskRecordRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, total, err
	}

	var resp []*TaskRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, total, nil
	default:
		return nil, total, err
	}
}

func (m *defaultTaskRecordModel) FindPageListByIdDESC(ctx context.Context, builder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*TaskRecord, error) {

	builder = builder.Columns(taskRecordRows)

	if preMinId > 0 {
		builder = builder.Where(" id < ? ", preMinId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id DESC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*TaskRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultTaskRecordModel) FindPageListByIdASC(ctx context.Context, builder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*TaskRecord, error) {

	builder = builder.Columns(taskRecordRows)

	if preMaxId > 0 {
		builder = builder.Where(" id > ? ", preMaxId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id ASC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*TaskRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultTaskRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select().From(m.table)
}

func (m *defaultTaskRecordModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheCheckinTaskRecordIdPrefix, primary)
}

func (m *defaultTaskRecordModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", taskRecordRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultTaskRecordModel) tableName() string {
	return m.table
}
