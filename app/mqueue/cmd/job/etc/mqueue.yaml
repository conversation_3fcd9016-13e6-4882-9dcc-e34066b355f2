Name: mqueue-job
Host: 0.0.0.0
Port: 3002
Mode: dev

#监控
Prometheus:
  Host: 0.0.0.0
  Port: 4010
  Path: /metrics

#Link Tracking
Telemetry:
  Name: mqueue-job
  Endpoint: http://jaeger:14268/api/traces
  Sampler: 1.0
  Batcher: jaeger

Log:
  ServiceName: mqueue-job
  Mode: console
  # 打印堆栈信息 方便查询错误
  Encoding: plain
  Level: error

Redis:
  Host: redis:6379
  Type: node
  Pass: G62m50oigInC30sf

#WxMiniConf
WxMiniConf:
  AppId: wx0fe80d42b0d37cc8
  Secret: 495f7df09f227dde93f57f875a8f0e9f

WxMsgConf:
  EventToken: RKeAteaxQU5zH16Wywq1v4nuOj2J4L6t
  EncodingAESKey: 4HkPH9w1HyZR45ayqTGhYM2OmINT7rp2VCkSxpwuClz

#rpc service.
OrderRpcConf:
  Endpoints:
    - 127.0.0.1:2001
  NonBlock: true

UsercenterRpcConf:
  Endpoints:
    - 127.0.0.1:2004
  NonBlock: true

LotteryRpcConf:
  Endpoints:
    - 127.0.0.1:2005
  NonBlock: true

NoticeRpcConf:
  Endpoints:
    - 127.0.0.1:2006
  NonBlock: true
  Timeout: 500000 #调试

CheckinRpcConf:
  Endpoints:
    - 127.0.0.1:2007
  NonBlock: true
