Name: notice-api
Host: 0.0.0.0
Port: 1006
Mode: dev

#jwtAuth
#JwtAuth:
#  AccessSecret: ae0536f9-6450-4606-8e13-5a19ed505da0

#监控
Prometheus:
  Host: 0.0.0.0
  Port: 4029
  Path: /metrics

#链路追踪
Telemetry:
  Name: notice-api
  Endpoint: http://jaeger:14268/api/traces
  Sampler: 1.0
  Batcher: jaeger

Log:
  ServiceName: notice-api
  Mode: console
  # 因为项目在docker中启动，所以logs是打印在docker中
  Path: logs
  # 打印堆栈信息 方便查询错误
  Encoding: plain
  Level: info
  Compress: true
  KeyDays: 7

#WxMiniConf
WxMiniConf:
  AppId: wx0fe80d42b0d37cc8
  Secret: 495f7df09f227dde93f57f875a8f0e9f

WxMsgConf:
  EventToken: RKeAteaxQU5zH16Wywq1v4nuOj2J4L6t
  EncodingAESKey: 4HkPH9w1HyZR45ayqTGhYM2OmINT7rp2VCkSxpwuClz

#Rpc
NoticeRpcConf:
  Endpoints:
    - 127.0.0.1:2006
  NonBlock: true
  Timeout: 500000 #调试