// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.19.4
// source: comment.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// --------------------------------comment--------------------------------
type Comment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                   //id
	UserId      int64  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`           //用户id
	LotteryId   int64  `protobuf:"varint,3,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"`     //抽奖id
	PrizeName   string `protobuf:"bytes,4,opt,name=prizeName,proto3" json:"prizeName,omitempty"`      //奖品名称
	Content     string `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`          //晒单评论内容
	Pics        string `protobuf:"bytes,6,opt,name=pics,proto3" json:"pics,omitempty"`                //晒单评论图片
	PraiseCount int64  `protobuf:"varint,7,opt,name=praiseCount,proto3" json:"praiseCount,omitempty"` //点赞数量
	CreateTime  int64  `protobuf:"varint,8,opt,name=createTime,proto3" json:"createTime,omitempty"`   //创建时间
	UpdateTime  int64  `protobuf:"varint,9,opt,name=updateTime,proto3" json:"updateTime,omitempty"`   //更新时间
	IsPraise    int64  `protobuf:"varint,10,opt,name=isPraise,proto3" json:"isPraise,omitempty"`      //当前用户是否点赞
}

func (x *Comment) Reset() {
	*x = Comment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Comment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Comment) ProtoMessage() {}

func (x *Comment) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Comment.ProtoReflect.Descriptor instead.
func (*Comment) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{0}
}

func (x *Comment) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Comment) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *Comment) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

func (x *Comment) GetPrizeName() string {
	if x != nil {
		return x.PrizeName
	}
	return ""
}

func (x *Comment) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *Comment) GetPics() string {
	if x != nil {
		return x.Pics
	}
	return ""
}

func (x *Comment) GetPraiseCount() int64 {
	if x != nil {
		return x.PraiseCount
	}
	return 0
}

func (x *Comment) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *Comment) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *Comment) GetIsPraise() int64 {
	if x != nil {
		return x.IsPraise
	}
	return 0
}

type AddCommentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId      int64  `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`           //用户id
	LotteryId   int64  `protobuf:"varint,2,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"`     //抽奖id
	PrizeName   string `protobuf:"bytes,3,opt,name=prizeName,proto3" json:"prizeName,omitempty"`      //奖品名称
	Content     string `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`          //晒单评论内容
	Pics        string `protobuf:"bytes,5,opt,name=pics,proto3" json:"pics,omitempty"`                //晒单评论图片
	PraiseCount int64  `protobuf:"varint,6,opt,name=praiseCount,proto3" json:"praiseCount,omitempty"` //点赞数量
}

func (x *AddCommentReq) Reset() {
	*x = AddCommentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddCommentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCommentReq) ProtoMessage() {}

func (x *AddCommentReq) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCommentReq.ProtoReflect.Descriptor instead.
func (*AddCommentReq) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{1}
}

func (x *AddCommentReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AddCommentReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

func (x *AddCommentReq) GetPrizeName() string {
	if x != nil {
		return x.PrizeName
	}
	return ""
}

func (x *AddCommentReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *AddCommentReq) GetPics() string {
	if x != nil {
		return x.Pics
	}
	return ""
}

func (x *AddCommentReq) GetPraiseCount() int64 {
	if x != nil {
		return x.PraiseCount
	}
	return 0
}

type AddCommentResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddCommentResp) Reset() {
	*x = AddCommentResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddCommentResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCommentResp) ProtoMessage() {}

func (x *AddCommentResp) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCommentResp.ProtoReflect.Descriptor instead.
func (*AddCommentResp) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{2}
}

type UpdateCommentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                   //id
	UserId      int64  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`           //用户id
	LotteryId   int64  `protobuf:"varint,3,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"`     //抽奖id
	PrizeName   string `protobuf:"bytes,4,opt,name=prizeName,proto3" json:"prizeName,omitempty"`      //奖品名称
	Content     string `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`          //晒单评论内容
	Pics        string `protobuf:"bytes,6,opt,name=pics,proto3" json:"pics,omitempty"`                //晒单评论图片
	PraiseCount int64  `protobuf:"varint,7,opt,name=praiseCount,proto3" json:"praiseCount,omitempty"` //点赞数量
}

func (x *UpdateCommentReq) Reset() {
	*x = UpdateCommentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCommentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCommentReq) ProtoMessage() {}

func (x *UpdateCommentReq) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCommentReq.ProtoReflect.Descriptor instead.
func (*UpdateCommentReq) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateCommentReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateCommentReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UpdateCommentReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

func (x *UpdateCommentReq) GetPrizeName() string {
	if x != nil {
		return x.PrizeName
	}
	return ""
}

func (x *UpdateCommentReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *UpdateCommentReq) GetPics() string {
	if x != nil {
		return x.Pics
	}
	return ""
}

func (x *UpdateCommentReq) GetPraiseCount() int64 {
	if x != nil {
		return x.PraiseCount
	}
	return 0
}

type UpdateCommentResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateCommentResp) Reset() {
	*x = UpdateCommentResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCommentResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCommentResp) ProtoMessage() {}

func (x *UpdateCommentResp) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCommentResp.ProtoReflect.Descriptor instead.
func (*UpdateCommentResp) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{4}
}

type DelCommentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *DelCommentReq) Reset() {
	*x = DelCommentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelCommentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelCommentReq) ProtoMessage() {}

func (x *DelCommentReq) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelCommentReq.ProtoReflect.Descriptor instead.
func (*DelCommentReq) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{5}
}

func (x *DelCommentReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DelCommentResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DelCommentResp) Reset() {
	*x = DelCommentResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelCommentResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelCommentResp) ProtoMessage() {}

func (x *DelCommentResp) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelCommentResp.ProtoReflect.Descriptor instead.
func (*DelCommentResp) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{6}
}

type GetCommentByIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *GetCommentByIdReq) Reset() {
	*x = GetCommentByIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCommentByIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommentByIdReq) ProtoMessage() {}

func (x *GetCommentByIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommentByIdReq.ProtoReflect.Descriptor instead.
func (*GetCommentByIdReq) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{7}
}

func (x *GetCommentByIdReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetCommentByIdResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Comment *Comment `protobuf:"bytes,1,opt,name=comment,proto3" json:"comment,omitempty"` //comment
}

func (x *GetCommentByIdResp) Reset() {
	*x = GetCommentByIdResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCommentByIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommentByIdResp) ProtoMessage() {}

func (x *GetCommentByIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommentByIdResp.ProtoReflect.Descriptor instead.
func (*GetCommentByIdResp) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{8}
}

func (x *GetCommentByIdResp) GetComment() *Comment {
	if x != nil {
		return x.Comment
	}
	return nil
}

type SearchCommentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page        int64  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`               //page
	Limit       int64  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`             //limit
	Id          int64  `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`                   //id
	UserId      int64  `protobuf:"varint,4,opt,name=userId,proto3" json:"userId,omitempty"`           //用户id
	LotteryId   int64  `protobuf:"varint,5,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"`     //抽奖id
	PrizeName   string `protobuf:"bytes,6,opt,name=prizeName,proto3" json:"prizeName,omitempty"`      //奖品名称
	Content     string `protobuf:"bytes,7,opt,name=content,proto3" json:"content,omitempty"`          //晒单评论内容
	Pics        string `protobuf:"bytes,8,opt,name=pics,proto3" json:"pics,omitempty"`                //晒单评论图片
	PraiseCount int64  `protobuf:"varint,9,opt,name=praiseCount,proto3" json:"praiseCount,omitempty"` //点赞数量
	CreateTime  int64  `protobuf:"varint,10,opt,name=createTime,proto3" json:"createTime,omitempty"`  //创建时间
	UpdateTime  int64  `protobuf:"varint,11,opt,name=updateTime,proto3" json:"updateTime,omitempty"`  //更新时间
	LastId      int64  `protobuf:"varint,12,opt,name=lastId,proto3" json:"lastId,omitempty"`          //lastId
	Sort        int64  `protobuf:"varint,13,opt,name=sort,proto3" json:"sort,omitempty"`              //排序方式：0-时间倒序，1-点赞数倒序
}

func (x *SearchCommentReq) Reset() {
	*x = SearchCommentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchCommentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCommentReq) ProtoMessage() {}

func (x *SearchCommentReq) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCommentReq.ProtoReflect.Descriptor instead.
func (*SearchCommentReq) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{9}
}

func (x *SearchCommentReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchCommentReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *SearchCommentReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SearchCommentReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *SearchCommentReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

func (x *SearchCommentReq) GetPrizeName() string {
	if x != nil {
		return x.PrizeName
	}
	return ""
}

func (x *SearchCommentReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SearchCommentReq) GetPics() string {
	if x != nil {
		return x.Pics
	}
	return ""
}

func (x *SearchCommentReq) GetPraiseCount() int64 {
	if x != nil {
		return x.PraiseCount
	}
	return 0
}

func (x *SearchCommentReq) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *SearchCommentReq) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *SearchCommentReq) GetLastId() int64 {
	if x != nil {
		return x.LastId
	}
	return 0
}

func (x *SearchCommentReq) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

type SearchCommentResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Comment []*Comment `protobuf:"bytes,1,rep,name=comment,proto3" json:"comment,omitempty"` //comment
}

func (x *SearchCommentResp) Reset() {
	*x = SearchCommentResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchCommentResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchCommentResp) ProtoMessage() {}

func (x *SearchCommentResp) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchCommentResp.ProtoReflect.Descriptor instead.
func (*SearchCommentResp) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{10}
}

func (x *SearchCommentResp) GetComment() []*Comment {
	if x != nil {
		return x.Comment
	}
	return nil
}

// 判断当前用户当前评论是否点赞
type IsPraiseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    int64 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`       //用户id
	CommentId int64 `protobuf:"varint,2,opt,name=commentId,proto3" json:"commentId,omitempty"` //评论id
}

func (x *IsPraiseReq) Reset() {
	*x = IsPraiseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsPraiseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsPraiseReq) ProtoMessage() {}

func (x *IsPraiseReq) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsPraiseReq.ProtoReflect.Descriptor instead.
func (*IsPraiseReq) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{11}
}

func (x *IsPraiseReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *IsPraiseReq) GetCommentId() int64 {
	if x != nil {
		return x.CommentId
	}
	return 0
}

type IsPraiseResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 返回点赞id
	PraiseId int64 `protobuf:"varint,1,opt,name=praiseId,proto3" json:"praiseId,omitempty"` //点赞id
}

func (x *IsPraiseResp) Reset() {
	*x = IsPraiseResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsPraiseResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsPraiseResp) ProtoMessage() {}

func (x *IsPraiseResp) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsPraiseResp.ProtoReflect.Descriptor instead.
func (*IsPraiseResp) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{12}
}

func (x *IsPraiseResp) GetPraiseId() int64 {
	if x != nil {
		return x.PraiseId
	}
	return 0
}

// 判断当前用户给的评论id列表是否点赞，返回一个列表
type IsPraiseListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    int64   `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`              //用户id
	CommentId []int64 `protobuf:"varint,2,rep,packed,name=commentId,proto3" json:"commentId,omitempty"` //评论id
}

func (x *IsPraiseListReq) Reset() {
	*x = IsPraiseListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsPraiseListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsPraiseListReq) ProtoMessage() {}

func (x *IsPraiseListReq) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsPraiseListReq.ProtoReflect.Descriptor instead.
func (*IsPraiseListReq) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{13}
}

func (x *IsPraiseListReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *IsPraiseListReq) GetCommentId() []int64 {
	if x != nil {
		return x.CommentId
	}
	return nil
}

type IsPraiseListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PraiseId []int64 `protobuf:"varint,1,rep,packed,name=praiseId,proto3" json:"praiseId,omitempty"` //点赞id
}

func (x *IsPraiseListResp) Reset() {
	*x = IsPraiseListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsPraiseListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsPraiseListResp) ProtoMessage() {}

func (x *IsPraiseListResp) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsPraiseListResp.ProtoReflect.Descriptor instead.
func (*IsPraiseListResp) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{14}
}

func (x *IsPraiseListResp) GetPraiseId() []int64 {
	if x != nil {
		return x.PraiseId
	}
	return nil
}

// 评论点赞/取消点赞
type PraiseCommentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    int64 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`       //用户id
	CommentId int64 `protobuf:"varint,2,opt,name=commentId,proto3" json:"commentId,omitempty"` //评论id
}

func (x *PraiseCommentReq) Reset() {
	*x = PraiseCommentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PraiseCommentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PraiseCommentReq) ProtoMessage() {}

func (x *PraiseCommentReq) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PraiseCommentReq.ProtoReflect.Descriptor instead.
func (*PraiseCommentReq) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{15}
}

func (x *PraiseCommentReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *PraiseCommentReq) GetCommentId() int64 {
	if x != nil {
		return x.CommentId
	}
	return 0
}

type PraiseCommentResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PraiseCommentResp) Reset() {
	*x = PraiseCommentResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PraiseCommentResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PraiseCommentResp) ProtoMessage() {}

func (x *PraiseCommentResp) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PraiseCommentResp.ProtoReflect.Descriptor instead.
func (*PraiseCommentResp) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{16}
}

// 获取评论lastId
type GetCommentLastIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetCommentLastIdReq) Reset() {
	*x = GetCommentLastIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCommentLastIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommentLastIdReq) ProtoMessage() {}

func (x *GetCommentLastIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommentLastIdReq.ProtoReflect.Descriptor instead.
func (*GetCommentLastIdReq) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{17}
}

type GetCommentLastIdResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LastId int64 `protobuf:"varint,1,opt,name=lastId,proto3" json:"lastId,omitempty"` //lastId
}

func (x *GetCommentLastIdResp) Reset() {
	*x = GetCommentLastIdResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCommentLastIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCommentLastIdResp) ProtoMessage() {}

func (x *GetCommentLastIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCommentLastIdResp.ProtoReflect.Descriptor instead.
func (*GetCommentLastIdResp) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{18}
}

func (x *GetCommentLastIdResp) GetLastId() int64 {
	if x != nil {
		return x.LastId
	}
	return 0
}

// 获取当前用户评论列表，以及评论的点赞数，当前用户是否点赞
type GetUserCommentReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId int64 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"` //用户id
}

func (x *GetUserCommentReq) Reset() {
	*x = GetUserCommentReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserCommentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserCommentReq) ProtoMessage() {}

func (x *GetUserCommentReq) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserCommentReq.ProtoReflect.Descriptor instead.
func (*GetUserCommentReq) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{19}
}

func (x *GetUserCommentReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GetUserCommentResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Comment []*Comment `protobuf:"bytes,1,rep,name=comment,proto3" json:"comment,omitempty"` //comment
}

func (x *GetUserCommentResp) Reset() {
	*x = GetUserCommentResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserCommentResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserCommentResp) ProtoMessage() {}

func (x *GetUserCommentResp) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserCommentResp.ProtoReflect.Descriptor instead.
func (*GetUserCommentResp) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{20}
}

func (x *GetUserCommentResp) GetComment() []*Comment {
	if x != nil {
		return x.Comment
	}
	return nil
}

// --------------------------------praise--------------------------------
type Praise struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                 //id
	UserId     int64 `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`         //评论者id
	CommentId  int64 `protobuf:"varint,3,opt,name=commentId,proto3" json:"commentId,omitempty"`   //评论id
	CreateTime int64 `protobuf:"varint,4,opt,name=createTime,proto3" json:"createTime,omitempty"` //createTime
	UpdateTime int64 `protobuf:"varint,5,opt,name=updateTime,proto3" json:"updateTime,omitempty"` //updateTime
}

func (x *Praise) Reset() {
	*x = Praise{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Praise) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Praise) ProtoMessage() {}

func (x *Praise) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Praise.ProtoReflect.Descriptor instead.
func (*Praise) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{21}
}

func (x *Praise) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Praise) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *Praise) GetCommentId() int64 {
	if x != nil {
		return x.CommentId
	}
	return 0
}

func (x *Praise) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *Praise) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

type AddPraiseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    int64 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`       //评论者id
	CommentId int64 `protobuf:"varint,2,opt,name=commentId,proto3" json:"commentId,omitempty"` //评论id
}

func (x *AddPraiseReq) Reset() {
	*x = AddPraiseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddPraiseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPraiseReq) ProtoMessage() {}

func (x *AddPraiseReq) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPraiseReq.ProtoReflect.Descriptor instead.
func (*AddPraiseReq) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{22}
}

func (x *AddPraiseReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AddPraiseReq) GetCommentId() int64 {
	if x != nil {
		return x.CommentId
	}
	return 0
}

type AddPraiseResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddPraiseResp) Reset() {
	*x = AddPraiseResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddPraiseResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPraiseResp) ProtoMessage() {}

func (x *AddPraiseResp) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPraiseResp.ProtoReflect.Descriptor instead.
func (*AddPraiseResp) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{23}
}

type UpdatePraiseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`               //id
	UserId    int64 `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`       //评论者id
	CommentId int64 `protobuf:"varint,3,opt,name=commentId,proto3" json:"commentId,omitempty"` //评论id
}

func (x *UpdatePraiseReq) Reset() {
	*x = UpdatePraiseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePraiseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePraiseReq) ProtoMessage() {}

func (x *UpdatePraiseReq) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePraiseReq.ProtoReflect.Descriptor instead.
func (*UpdatePraiseReq) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{24}
}

func (x *UpdatePraiseReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePraiseReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UpdatePraiseReq) GetCommentId() int64 {
	if x != nil {
		return x.CommentId
	}
	return 0
}

type UpdatePraiseResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdatePraiseResp) Reset() {
	*x = UpdatePraiseResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePraiseResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePraiseResp) ProtoMessage() {}

func (x *UpdatePraiseResp) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePraiseResp.ProtoReflect.Descriptor instead.
func (*UpdatePraiseResp) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{25}
}

type DelPraiseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *DelPraiseReq) Reset() {
	*x = DelPraiseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelPraiseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelPraiseReq) ProtoMessage() {}

func (x *DelPraiseReq) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelPraiseReq.ProtoReflect.Descriptor instead.
func (*DelPraiseReq) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{26}
}

func (x *DelPraiseReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DelPraiseResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DelPraiseResp) Reset() {
	*x = DelPraiseResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelPraiseResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelPraiseResp) ProtoMessage() {}

func (x *DelPraiseResp) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelPraiseResp.ProtoReflect.Descriptor instead.
func (*DelPraiseResp) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{27}
}

type GetPraiseByIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *GetPraiseByIdReq) Reset() {
	*x = GetPraiseByIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPraiseByIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPraiseByIdReq) ProtoMessage() {}

func (x *GetPraiseByIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPraiseByIdReq.ProtoReflect.Descriptor instead.
func (*GetPraiseByIdReq) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{28}
}

func (x *GetPraiseByIdReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetPraiseByIdResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Praise *Praise `protobuf:"bytes,1,opt,name=praise,proto3" json:"praise,omitempty"` //praise
}

func (x *GetPraiseByIdResp) Reset() {
	*x = GetPraiseByIdResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPraiseByIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPraiseByIdResp) ProtoMessage() {}

func (x *GetPraiseByIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPraiseByIdResp.ProtoReflect.Descriptor instead.
func (*GetPraiseByIdResp) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{29}
}

func (x *GetPraiseByIdResp) GetPraise() *Praise {
	if x != nil {
		return x.Praise
	}
	return nil
}

type SearchPraiseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page       int64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`             //page
	Limit      int64 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`           //limit
	Id         int64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`                 //id
	UserId     int64 `protobuf:"varint,4,opt,name=userId,proto3" json:"userId,omitempty"`         //评论者id
	CommentId  int64 `protobuf:"varint,5,opt,name=commentId,proto3" json:"commentId,omitempty"`   //评论id
	CreateTime int64 `protobuf:"varint,6,opt,name=createTime,proto3" json:"createTime,omitempty"` //createTime
	UpdateTime int64 `protobuf:"varint,7,opt,name=updateTime,proto3" json:"updateTime,omitempty"` //updateTime
	LastId     int64 `protobuf:"varint,8,opt,name=lastId,proto3" json:"lastId,omitempty"`         //lastId
}

func (x *SearchPraiseReq) Reset() {
	*x = SearchPraiseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchPraiseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPraiseReq) ProtoMessage() {}

func (x *SearchPraiseReq) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPraiseReq.ProtoReflect.Descriptor instead.
func (*SearchPraiseReq) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{30}
}

func (x *SearchPraiseReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchPraiseReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *SearchPraiseReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SearchPraiseReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *SearchPraiseReq) GetCommentId() int64 {
	if x != nil {
		return x.CommentId
	}
	return 0
}

func (x *SearchPraiseReq) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *SearchPraiseReq) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *SearchPraiseReq) GetLastId() int64 {
	if x != nil {
		return x.LastId
	}
	return 0
}

type SearchPraiseResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Praise []*Praise `protobuf:"bytes,1,rep,name=praise,proto3" json:"praise,omitempty"` //praise
}

func (x *SearchPraiseResp) Reset() {
	*x = SearchPraiseResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchPraiseResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPraiseResp) ProtoMessage() {}

func (x *SearchPraiseResp) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPraiseResp.ProtoReflect.Descriptor instead.
func (*SearchPraiseResp) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{31}
}

func (x *SearchPraiseResp) GetPraise() []*Praise {
	if x != nil {
		return x.Praise
	}
	return nil
}

// --------------------------------others--------------------------------
// 检查用户今日是否给评论点赞
type CheckUserPraiseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId int64 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"` //用户id
}

func (x *CheckUserPraiseReq) Reset() {
	*x = CheckUserPraiseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckUserPraiseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUserPraiseReq) ProtoMessage() {}

func (x *CheckUserPraiseReq) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUserPraiseReq.ProtoReflect.Descriptor instead.
func (*CheckUserPraiseReq) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{32}
}

func (x *CheckUserPraiseReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type CheckUserPraiseResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsPraise int64 `protobuf:"varint,1,opt,name=isPraise,proto3" json:"isPraise,omitempty"` //是否点赞
}

func (x *CheckUserPraiseResp) Reset() {
	*x = CheckUserPraiseResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_comment_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckUserPraiseResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUserPraiseResp) ProtoMessage() {}

func (x *CheckUserPraiseResp) ProtoReflect() protoreflect.Message {
	mi := &file_comment_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUserPraiseResp.ProtoReflect.Descriptor instead.
func (*CheckUserPraiseResp) Descriptor() ([]byte, []int) {
	return file_comment_proto_rawDescGZIP(), []int{33}
}

func (x *CheckUserPraiseResp) GetIsPraise() int64 {
	if x != nil {
		return x.IsPraise
	}
	return 0
}

var File_comment_proto protoreflect.FileDescriptor

var file_comment_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x02, 0x70, 0x62, 0x22, 0x99, 0x02, 0x0a, 0x07, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65,
	0x72, 0x79, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f, 0x74, 0x74,
	0x65, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x69, 0x7a, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x69, 0x7a, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x69, 0x63, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x69, 0x63,
	0x73, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x61, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x72, 0x61, 0x69, 0x73, 0x65, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x73, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x73, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x22,
	0xb3, 0x01, 0x0a, 0x0d, 0x41, 0x64, 0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x74,
	0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x69, 0x7a, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x69, 0x7a,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x69, 0x63, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70,
	0x69, 0x63, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x61, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x72, 0x61, 0x69, 0x73, 0x65,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x10, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x43, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0xc6, 0x01, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x69, 0x7a, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x69, 0x7a, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x69,
	0x63, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x69, 0x63, 0x73, 0x12, 0x20,
	0x0a, 0x0b, 0x70, 0x72, 0x61, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x72, 0x61, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0x13, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x1f, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x43, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x10, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x23, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x43,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x3b, 0x0a,
	0x12, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x79, 0x49, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x62, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0xdc, 0x02, 0x0a, 0x10, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x12,
	0x1c, 0x0a, 0x09, 0x70, 0x72, 0x69, 0x7a, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x69, 0x7a, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x69, 0x63, 0x73, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x69, 0x63, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x70,
	0x72, 0x61, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0b, 0x70, 0x72, 0x61, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x6c, 0x61, 0x73, 0x74, 0x49, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6c,
	0x61, 0x73, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x22, 0x3a, 0x0a, 0x11, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x25,
	0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x70, 0x62, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x43, 0x0a, 0x0b, 0x49, 0x73, 0x50, 0x72, 0x61, 0x69, 0x73,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x2a, 0x0a, 0x0c, 0x49, 0x73,
	0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72,
	0x61, 0x69, 0x73, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x72,
	0x61, 0x69, 0x73, 0x65, 0x49, 0x64, 0x22, 0x47, 0x0a, 0x0f, 0x49, 0x73, 0x50, 0x72, 0x61, 0x69,
	0x73, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22,
	0x2e, 0x0a, 0x10, 0x49, 0x73, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x61, 0x69, 0x73, 0x65, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x70, 0x72, 0x61, 0x69, 0x73, 0x65, 0x49, 0x64, 0x22,
	0x48, 0x0a, 0x10, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x13, 0x0a, 0x11, 0x50, 0x72, 0x61,
	0x69, 0x73, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x15,
	0x0a, 0x13, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x61, 0x73, 0x74,
	0x49, 0x64, 0x52, 0x65, 0x71, 0x22, 0x2e, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x16, 0x0a,
	0x06, 0x6c, 0x61, 0x73, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6c,
	0x61, 0x73, 0x74, 0x49, 0x64, 0x22, 0x2b, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x22, 0x3b, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x62, 0x2e, 0x43,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x22,
	0x8e, 0x01, 0x0a, 0x06, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x22, 0x44, 0x0a, 0x0c, 0x41, 0x64, 0x64, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x52, 0x65, 0x71,
	0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x0f, 0x0a, 0x0d, 0x41, 0x64, 0x64, 0x50, 0x72, 0x61,
	0x69, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x57, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x22, 0x12, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x22, 0x1e, 0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x50, 0x72, 0x61, 0x69, 0x73,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x0f, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x50, 0x72, 0x61, 0x69, 0x73,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x22, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x50, 0x72, 0x61, 0x69,
	0x73, 0x65, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x37, 0x0a, 0x11, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x22,
	0x0a, 0x06, 0x70, 0x72, 0x61, 0x69, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a,
	0x2e, 0x70, 0x62, 0x2e, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x52, 0x06, 0x70, 0x72, 0x61, 0x69,
	0x73, 0x65, 0x22, 0xd9, 0x01, 0x0a, 0x0f, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x61,
	0x69, 0x73, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x61, 0x73, 0x74, 0x49, 0x64,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6c, 0x61, 0x73, 0x74, 0x49, 0x64, 0x22, 0x36,
	0x0a, 0x10, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x22, 0x0a, 0x06, 0x70, 0x72, 0x61, 0x69, 0x73, 0x65, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x70, 0x62, 0x2e, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x52, 0x06,
	0x70, 0x72, 0x61, 0x69, 0x73, 0x65, 0x22, 0x2c, 0x0a, 0x12, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55,
	0x73, 0x65, 0x72, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x22, 0x31, 0x0a, 0x13, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x73, 0x65,
	0x72, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x69,
	0x73, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69,
	0x73, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x32, 0xbc, 0x07, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x0a, 0x41, 0x64, 0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x11, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3c, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x2e, 0x70, 0x62, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x15, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x33, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x11, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c,
	0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x0e, 0x47,
	0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x79, 0x49, 0x64, 0x12, 0x15, 0x2e,
	0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x42, 0x79, 0x49,
	0x64, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3c, 0x0a, 0x0d,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x2e,
	0x70, 0x62, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x43,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2d, 0x0a, 0x08, 0x49, 0x73,
	0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x12, 0x0f, 0x2e, 0x70, 0x62, 0x2e, 0x49, 0x73, 0x50, 0x72,
	0x61, 0x69, 0x73, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x70, 0x62, 0x2e, 0x49, 0x73, 0x50,
	0x72, 0x61, 0x69, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3c, 0x0a, 0x0d, 0x50, 0x72, 0x61,
	0x69, 0x73, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x2e, 0x70, 0x62, 0x2e,
	0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x1a, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x45, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x43, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x49, 0x64, 0x12, 0x17, 0x2e, 0x70, 0x62,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x49,
	0x64, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x39,
	0x0a, 0x0c, 0x49, 0x73, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x13,
	0x2e, 0x70, 0x62, 0x2e, 0x49, 0x73, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x70, 0x62, 0x2e, 0x49, 0x73, 0x50, 0x72, 0x61, 0x69, 0x73,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x0e, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x15, 0x2e, 0x70, 0x62,
	0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x1a, 0x16, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x30, 0x0a, 0x09, 0x41, 0x64,
	0x64, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x12, 0x10, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64,
	0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e, 0x70, 0x62, 0x2e, 0x41,
	0x64, 0x64, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x39, 0x0a, 0x0c,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x12, 0x13, 0x2e, 0x70,
	0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x14, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x61,
	0x69, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x30, 0x0a, 0x09, 0x44, 0x65, 0x6c, 0x50, 0x72,
	0x61, 0x69, 0x73, 0x65, 0x12, 0x10, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c, 0x50, 0x72, 0x61,
	0x69, 0x73, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c, 0x50,
	0x72, 0x61, 0x69, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3c, 0x0a, 0x0d, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x42, 0x79, 0x49, 0x64, 0x12, 0x14, 0x2e, 0x70, 0x62, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71,
	0x1a, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x42,
	0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x39, 0x0a, 0x0c, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x12, 0x13, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x70,
	0x62, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x42, 0x0a, 0x0f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x50,
	0x72, 0x61, 0x69, 0x73, 0x65, 0x12, 0x16, 0x2e, 0x70, 0x62, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x61, 0x69, 0x73, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e,
	0x70, 0x62, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x61, 0x69,
	0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_comment_proto_rawDescOnce sync.Once
	file_comment_proto_rawDescData = file_comment_proto_rawDesc
)

func file_comment_proto_rawDescGZIP() []byte {
	file_comment_proto_rawDescOnce.Do(func() {
		file_comment_proto_rawDescData = protoimpl.X.CompressGZIP(file_comment_proto_rawDescData)
	})
	return file_comment_proto_rawDescData
}

var file_comment_proto_msgTypes = make([]protoimpl.MessageInfo, 34)
var file_comment_proto_goTypes = []interface{}{
	(*Comment)(nil),              // 0: pb.Comment
	(*AddCommentReq)(nil),        // 1: pb.AddCommentReq
	(*AddCommentResp)(nil),       // 2: pb.AddCommentResp
	(*UpdateCommentReq)(nil),     // 3: pb.UpdateCommentReq
	(*UpdateCommentResp)(nil),    // 4: pb.UpdateCommentResp
	(*DelCommentReq)(nil),        // 5: pb.DelCommentReq
	(*DelCommentResp)(nil),       // 6: pb.DelCommentResp
	(*GetCommentByIdReq)(nil),    // 7: pb.GetCommentByIdReq
	(*GetCommentByIdResp)(nil),   // 8: pb.GetCommentByIdResp
	(*SearchCommentReq)(nil),     // 9: pb.SearchCommentReq
	(*SearchCommentResp)(nil),    // 10: pb.SearchCommentResp
	(*IsPraiseReq)(nil),          // 11: pb.IsPraiseReq
	(*IsPraiseResp)(nil),         // 12: pb.IsPraiseResp
	(*IsPraiseListReq)(nil),      // 13: pb.IsPraiseListReq
	(*IsPraiseListResp)(nil),     // 14: pb.IsPraiseListResp
	(*PraiseCommentReq)(nil),     // 15: pb.PraiseCommentReq
	(*PraiseCommentResp)(nil),    // 16: pb.PraiseCommentResp
	(*GetCommentLastIdReq)(nil),  // 17: pb.GetCommentLastIdReq
	(*GetCommentLastIdResp)(nil), // 18: pb.GetCommentLastIdResp
	(*GetUserCommentReq)(nil),    // 19: pb.GetUserCommentReq
	(*GetUserCommentResp)(nil),   // 20: pb.GetUserCommentResp
	(*Praise)(nil),               // 21: pb.Praise
	(*AddPraiseReq)(nil),         // 22: pb.AddPraiseReq
	(*AddPraiseResp)(nil),        // 23: pb.AddPraiseResp
	(*UpdatePraiseReq)(nil),      // 24: pb.UpdatePraiseReq
	(*UpdatePraiseResp)(nil),     // 25: pb.UpdatePraiseResp
	(*DelPraiseReq)(nil),         // 26: pb.DelPraiseReq
	(*DelPraiseResp)(nil),        // 27: pb.DelPraiseResp
	(*GetPraiseByIdReq)(nil),     // 28: pb.GetPraiseByIdReq
	(*GetPraiseByIdResp)(nil),    // 29: pb.GetPraiseByIdResp
	(*SearchPraiseReq)(nil),      // 30: pb.SearchPraiseReq
	(*SearchPraiseResp)(nil),     // 31: pb.SearchPraiseResp
	(*CheckUserPraiseReq)(nil),   // 32: pb.CheckUserPraiseReq
	(*CheckUserPraiseResp)(nil),  // 33: pb.CheckUserPraiseResp
}
var file_comment_proto_depIdxs = []int32{
	0,  // 0: pb.GetCommentByIdResp.comment:type_name -> pb.Comment
	0,  // 1: pb.SearchCommentResp.comment:type_name -> pb.Comment
	0,  // 2: pb.GetUserCommentResp.comment:type_name -> pb.Comment
	21, // 3: pb.GetPraiseByIdResp.praise:type_name -> pb.Praise
	21, // 4: pb.SearchPraiseResp.praise:type_name -> pb.Praise
	1,  // 5: pb.comment.AddComment:input_type -> pb.AddCommentReq
	3,  // 6: pb.comment.UpdateComment:input_type -> pb.UpdateCommentReq
	5,  // 7: pb.comment.DelComment:input_type -> pb.DelCommentReq
	7,  // 8: pb.comment.GetCommentById:input_type -> pb.GetCommentByIdReq
	9,  // 9: pb.comment.SearchComment:input_type -> pb.SearchCommentReq
	11, // 10: pb.comment.IsPraise:input_type -> pb.IsPraiseReq
	15, // 11: pb.comment.PraiseComment:input_type -> pb.PraiseCommentReq
	17, // 12: pb.comment.GetCommentLastId:input_type -> pb.GetCommentLastIdReq
	13, // 13: pb.comment.IsPraiseList:input_type -> pb.IsPraiseListReq
	19, // 14: pb.comment.GetUserComment:input_type -> pb.GetUserCommentReq
	22, // 15: pb.comment.AddPraise:input_type -> pb.AddPraiseReq
	24, // 16: pb.comment.UpdatePraise:input_type -> pb.UpdatePraiseReq
	26, // 17: pb.comment.DelPraise:input_type -> pb.DelPraiseReq
	28, // 18: pb.comment.GetPraiseById:input_type -> pb.GetPraiseByIdReq
	30, // 19: pb.comment.SearchPraise:input_type -> pb.SearchPraiseReq
	32, // 20: pb.comment.CheckUserPraise:input_type -> pb.CheckUserPraiseReq
	2,  // 21: pb.comment.AddComment:output_type -> pb.AddCommentResp
	4,  // 22: pb.comment.UpdateComment:output_type -> pb.UpdateCommentResp
	6,  // 23: pb.comment.DelComment:output_type -> pb.DelCommentResp
	8,  // 24: pb.comment.GetCommentById:output_type -> pb.GetCommentByIdResp
	10, // 25: pb.comment.SearchComment:output_type -> pb.SearchCommentResp
	12, // 26: pb.comment.IsPraise:output_type -> pb.IsPraiseResp
	16, // 27: pb.comment.PraiseComment:output_type -> pb.PraiseCommentResp
	18, // 28: pb.comment.GetCommentLastId:output_type -> pb.GetCommentLastIdResp
	14, // 29: pb.comment.IsPraiseList:output_type -> pb.IsPraiseListResp
	20, // 30: pb.comment.GetUserComment:output_type -> pb.GetUserCommentResp
	23, // 31: pb.comment.AddPraise:output_type -> pb.AddPraiseResp
	25, // 32: pb.comment.UpdatePraise:output_type -> pb.UpdatePraiseResp
	27, // 33: pb.comment.DelPraise:output_type -> pb.DelPraiseResp
	29, // 34: pb.comment.GetPraiseById:output_type -> pb.GetPraiseByIdResp
	31, // 35: pb.comment.SearchPraise:output_type -> pb.SearchPraiseResp
	33, // 36: pb.comment.CheckUserPraise:output_type -> pb.CheckUserPraiseResp
	21, // [21:37] is the sub-list for method output_type
	5,  // [5:21] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_comment_proto_init() }
func file_comment_proto_init() {
	if File_comment_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_comment_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Comment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddCommentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddCommentResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCommentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCommentResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelCommentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelCommentResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCommentByIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCommentByIdResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchCommentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchCommentResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsPraiseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsPraiseResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsPraiseListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsPraiseListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PraiseCommentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PraiseCommentResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCommentLastIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCommentLastIdResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserCommentReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserCommentResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Praise); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddPraiseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddPraiseResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePraiseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePraiseResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelPraiseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelPraiseResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPraiseByIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPraiseByIdResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchPraiseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchPraiseResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckUserPraiseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_comment_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckUserPraiseResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_comment_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   34,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_comment_proto_goTypes,
		DependencyIndexes: file_comment_proto_depIdxs,
		MessageInfos:      file_comment_proto_msgTypes,
	}.Build()
	File_comment_proto = out.File
	file_comment_proto_rawDesc = nil
	file_comment_proto_goTypes = nil
	file_comment_proto_depIdxs = nil
}
