## 热更新
- mac系统支持modd热更新
- window系统不支持，可以使用air热更新
- 暂时手动更新，只更新应用即可，不需要重启整个docker
  ![](https://files.mdnice.com/user/36414/90ca1218-e86f-42b3-9514-a6a31b8c5739.png)

## 查询日志

开发阶段，可以直接查看docker中应用的日志：

点击对应的容器

![](https://files.mdnice.com/user/36414/d07ad0e6-f047-44ac-bb01-405f730851af.png)

点开后就可以看到日志啦，尤其是api和rpc编译出错的日志，在这里是可以快速定位到的。

![](https://files.mdnice.com/user/36414/ff890b85-840d-4886-a49d-3c068ad7e765.png)

### kibana日志

http://127.0.0.1:5601/app/discover

参考doc下的 11-日志收集 配置一些就好了


![](https://files.mdnice.com/user/36414/6533f35e-fc6d-4cc9-97c4-31797cb90215.png)

![](https://files.mdnice.com/user/36414/c346976f-cd77-4e15-9849-93b382aef6dd.png)

### sqlx sqlc

sqlx和sqlc可以看下这个 @黄龙珠 
https://www.bilibili.com/video/BV1644y1N7AU/

## 事务处理

创建抽奖和添加奖品是需要事务处理的

没基础的同学先看视频教程：https://www.bilibili.com/video/BV14u411q7st/

有基础的同学可以直接看我的示例代码：创建抽奖的rpc层代码


![](https://files.mdnice.com/user/36414/b993c532-a289-46ed-b498-741cd9964de8.png)


## todo

### 生成模板

TransInsert

TransUpdate

### 配置alias

mac可以把常用命令配置一下alias apigen="goctl api go -api *.api -dir ../  --style=goZero"

## 使用model的技巧

1. 通过这里的工具生成model

![](https://files.mdnice.com/user/36414/1780e221-26eb-4ee2-9b8f-c4123254dc10.png)

2. 如果是第一次生成没什么问题，如果你的项目中已有使用中的model，因为新增了字段需要重新生成model，那么只复制粘贴 xxxModel_gen.go 到你项目的model目录即可。因为我们自定义的方法会写在xxxModel.go文件中

![](https://files.mdnice.com/user/36414/7c4884f8-ee97-475d-9c6a-951f54a20ab6.png)

3. 复制粘贴过来后，因为包名不一致会报错，统一修改包名为model即可。

![](https://files.mdnice.com/user/36414/8fc2187c-7661-4e0e-972b-fd3b210e6df3.png)

## apifox

注意，我们使用json的方式调试

![](https://files.mdnice.com/user/36414/2197adc9-dd3f-455a-a116-485d35382ede.png)

### swagger

https://github.com/zeromicro/goctl-swagger

安装后再xxx.api目录下执行：
goctl api plugin -plugin goctl-swagger="swagger -filename user.json" -api main.api -dir .

把生成的xxx.json文件导入到apifox中

可以自动生成请求数据：

![](https://files.mdnice.com/user/36414/ffdab135-bbc1-40a1-9bc6-de5d7083300e.png)

### 鉴权

涉及到需要鉴权的接口，在这里添加token进行鉴权

![](https://files.mdnice.com/user/36414/1421da62-6be4-4b13-9e55-dff82593f864.png)


### 云效

除了在控制台合并分支，也可以通用云效合并分支，这样更规范，可以设置规则，专人审核，责任到人。

![](https://files.mdnice.com/user/36414/e5e5840c-fd17-4c9d-8dc4-0afcebdbd426.png)
