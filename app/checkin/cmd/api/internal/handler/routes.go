// Code generated by goctl. DO NOT EDIT.
package handler

import (
	"net/http"

	checkin "looklook/app/checkin/cmd/api/internal/handler/checkin"
	"looklook/app/checkin/cmd/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/checkin/getCheckin",
				Handler: checkin.GetCheckinHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/checkin/getTasks",
				Handler: checkin.GetTasksHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/checkin",
				Handler: checkin.CheckinHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/checkin/claimReward",
				Handler: checkin.ClaimRewardHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/checkin/updateSub",
				Handler: checkin.UpdateSubHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/checkin/getIntegral",
				Handler: checkin.GetIntegralHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.JwtAuth.AccessSecret),
		rest.WithPrefix("/checkin/v1"),
	)
}
