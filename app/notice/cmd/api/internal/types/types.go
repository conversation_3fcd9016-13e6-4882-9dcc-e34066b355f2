// Code generated by goctl. DO NOT EDIT.
package types

type ReceiveEventReq struct {
	// TODO 测试代码
	//Signature string `form:"signature"`
	//Timestamp string `form:"timestamp"`
	//Nonce     string `form:"nonce"`
}

type ReceiveEventResp struct {
}

type VerifyEventReq struct {
	Signature string `form:"signature"`
	Timestamp string `form:"timestamp"`
	Nonce     string `form:"nonce"`
	Echostr   string `form:"echostr"`
}

type VerifyEventResp struct {
}
