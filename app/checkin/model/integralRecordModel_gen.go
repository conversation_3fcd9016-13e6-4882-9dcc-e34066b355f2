// Code generated by goctl. DO NOT EDIT.

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
	"looklook/common/globalkey"
)

var (
	integralRecordFieldNames          = builder.RawFieldNames(&IntegralRecord{})
	integralRecordRows                = strings.Join(integralRecordFieldNames, ",")
	integralRecordRowsExpectAutoSet   = strings.Join(stringx.Remove(integralRecordFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), ",")
	integralRecordRowsWithPlaceHolder = strings.Join(stringx.Remove(integralRecordFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), "=?,") + "=?"

	cacheCheckinIntegralRecordIdPrefix = "cache:checkin:integralRecord:id:"
)

type (
	integralRecordModel interface {
		Insert(ctx context.Context, data *IntegralRecord) (sql.Result, error)
		TransInsert(ctx context.Context, session sqlx.Session, data *IntegralRecord) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*IntegralRecord, error)
		Update(ctx context.Context, data *IntegralRecord) error
		List(ctx context.Context, page, limit int64) ([]*IntegralRecord, error)
		TransUpdate(ctx context.Context, session sqlx.Session, data *IntegralRecord) error
		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		SelectBuilder() squirrel.SelectBuilder
		FindSum(ctx context.Context, sumBuilder squirrel.SelectBuilder, field string) (float64, error)
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder, field string) (int64, error)
		FindAll(ctx context.Context, rowBuilder squirrel.SelectBuilder, orderBy string) ([]*IntegralRecord, error)
		FindPageListByPage(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*IntegralRecord, error)
		FindPageListByPageWithTotal(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*IntegralRecord, int64, error)
		FindPageListByIdDESC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*IntegralRecord, error)
		FindPageListByIdASC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*IntegralRecord, error)
		Delete(ctx context.Context, id int64) error
	}

	defaultIntegralRecordModel struct {
		sqlc.CachedConn
		table string
	}

	IntegralRecord struct {
		Id         int64     `db:"id"`
		UserId     int64     `db:"user_id"`
		Integral   int64     `db:"integral"` // points added or subtracted
		Content    string    `db:"content"`
		CreateTime time.Time `db:"create_time"`
	}
)

func newIntegralRecordModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultIntegralRecordModel {
	return &defaultIntegralRecordModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`integral_record`",
	}
}

func (m *defaultIntegralRecordModel) Delete(ctx context.Context, id int64) error {
	checkinIntegralRecordIdKey := fmt.Sprintf("%s%v", cacheCheckinIntegralRecordIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		return conn.ExecCtx(ctx, query, id)
	}, checkinIntegralRecordIdKey)
	return err
}

func (m *defaultIntegralRecordModel) FindOne(ctx context.Context, id int64) (*IntegralRecord, error) {
	checkinIntegralRecordIdKey := fmt.Sprintf("%s%v", cacheCheckinIntegralRecordIdPrefix, id)
	var resp IntegralRecord
	err := m.QueryRowCtx(ctx, &resp, checkinIntegralRecordIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", integralRecordRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultIntegralRecordModel) Insert(ctx context.Context, data *IntegralRecord) (sql.Result, error) {
	checkinIntegralRecordIdKey := fmt.Sprintf("%s%v", cacheCheckinIntegralRecordIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?)", m.table, integralRecordRowsExpectAutoSet)
		return conn.ExecCtx(ctx, query, data.UserId, data.Integral, data.Content)
	}, checkinIntegralRecordIdKey)
	return ret, err
}

func (m *defaultIntegralRecordModel) TransInsert(ctx context.Context, session sqlx.Session, data *IntegralRecord) (sql.Result, error) {
	checkinIntegralRecordIdKey := fmt.Sprintf("%s%v", cacheCheckinIntegralRecordIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?)", m.table, integralRecordRowsExpectAutoSet)
		return session.ExecCtx(ctx, query, data.UserId, data.Integral, data.Content)
	}, checkinIntegralRecordIdKey)
	return ret, err
}
func (m *defaultIntegralRecordModel) Update(ctx context.Context, data *IntegralRecord) error {
	checkinIntegralRecordIdKey := fmt.Sprintf("%s%v", cacheCheckinIntegralRecordIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, integralRecordRowsWithPlaceHolder)
		return conn.ExecCtx(ctx, query, data.UserId, data.Integral, data.Content, data.Id)
	}, checkinIntegralRecordIdKey)
	return err
}

func (m *defaultIntegralRecordModel) TransUpdate(ctx context.Context, session sqlx.Session, data *IntegralRecord) error {
	checkinIntegralRecordIdKey := fmt.Sprintf("%s%v", cacheCheckinIntegralRecordIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, integralRecordRowsWithPlaceHolder)
		return session.ExecCtx(ctx, query, data.UserId, data.Integral, data.Content, data.Id)
	}, checkinIntegralRecordIdKey)
	return err
}

func (m *defaultIntegralRecordModel) List(ctx context.Context, page, limit int64) ([]*IntegralRecord, error) {
	query := fmt.Sprintf("select %s from %s limit ?,?", integralRecordRows, m.table)
	var resp []*IntegralRecord
	//err := m.conn.QueryRowsCtx(ctx, &resp, query, (page-1)*limit, limit)
	err := m.QueryRowsNoCacheCtx(ctx, &resp, query, (page-1)*limit, limit)
	return resp, err
}

func (m *defaultIntegralRecordModel) Trans(ctx context.Context, fn func(ctx context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, func(ctx context.Context, session sqlx.Session) error {
		return fn(ctx, session)
	})
}

func (m *defaultIntegralRecordModel) FindSum(ctx context.Context, builder squirrel.SelectBuilder, field string) (float64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindSum Least One Field"), "FindSum Least One Field")
	}

	builder = builder.Columns("IFNULL(SUM(" + field + "),0)")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp float64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultIntegralRecordModel) FindCount(ctx context.Context, builder squirrel.SelectBuilder, field string) (int64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindCount Least One Field"), "FindCount Least One Field")
	}

	builder = builder.Columns("COUNT(" + field + ")")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultIntegralRecordModel) FindAll(ctx context.Context, builder squirrel.SelectBuilder, orderBy string) ([]*IntegralRecord, error) {

	builder = builder.Columns(integralRecordRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*IntegralRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultIntegralRecordModel) FindPageListByPage(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*IntegralRecord, error) {

	builder = builder.Columns(integralRecordRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*IntegralRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultIntegralRecordModel) FindPageListByPageWithTotal(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*IntegralRecord, int64, error) {

	total, err := m.FindCount(ctx, builder, "id")
	if err != nil {
		return nil, 0, err
	}

	builder = builder.Columns(integralRecordRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, total, err
	}

	var resp []*IntegralRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, total, nil
	default:
		return nil, total, err
	}
}

func (m *defaultIntegralRecordModel) FindPageListByIdDESC(ctx context.Context, builder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*IntegralRecord, error) {

	builder = builder.Columns(integralRecordRows)

	if preMinId > 0 {
		builder = builder.Where(" id < ? ", preMinId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id DESC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*IntegralRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultIntegralRecordModel) FindPageListByIdASC(ctx context.Context, builder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*IntegralRecord, error) {

	builder = builder.Columns(integralRecordRows)

	if preMaxId > 0 {
		builder = builder.Where(" id > ? ", preMaxId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id ASC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*IntegralRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultIntegralRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select().From(m.table)
}

func (m *defaultIntegralRecordModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheCheckinIntegralRecordIdPrefix, primary)
}

func (m *defaultIntegralRecordModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", integralRecordRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultIntegralRecordModel) tableName() string {
	return m.table
}
