syntax = "v1"

info(
	title: "用户实例"
	desc: "用户实例"
	author: "<PERSON><PERSON><PERSON>"
	email: "<EMAIL>"
)

type User {
	Id       int64  `json:"id"`
	Mobile   string `json:"mobile"`
	Nickname string `json:"nickname"`
	Sex      int64  `json:"sex"`
	Avatar   string `json:"avatar"`
	Info     string `json:"info"`
	IsAdmin  int64  `json:"isAdmin"`
	Signature string `json:"signature"`
	Longitude float64 `json:"longitude"`
	Latitude float64 `json:"latitude"`
    ParticipationCount      int64  `json:"participation_count"`
	CreatedCount      int64  `json:"created_count"`
	WonCount      int64  `json:"won_count"`
	Integral      int64  `json:"integral"`

}

type (
	RegisterReq {
		Mobile   string `json:"mobile"`
		Password string `json:"password"`
	}
	RegisterResp {
		AccessToken  string `json:"accessToken"`
		AccessExpire int64  `json:"accessExpire"`
		RefreshAfter int64  `json:"refreshAfter"`
	}
)

type (
	LoginReq {
		Mobile   string `json:"mobile"`
		Password string `json:"password"`
	}
	LoginResp {
		AccessToken  string `json:"accessToken"`
		AccessExpire int64  `json:"accessExpire"`
		RefreshAfter int64  `json:"refreshAfter"`
	}
)

type (
	WXMiniAuthReq {
		Code          string `json:"code"`
		IV            string `json:"iv"`
		EncryptedData string `json:"encryptedData"`
		Nickname 	  string `json:"nickname, optional"`
		Avatar        string `json:"avatar, optional"`
	}
	WXMiniAuthResp {
		AccessToken  string `json:"accessToken"`
		AccessExpire int64  `json:"accessExpire"`
		RefreshAfter int64  `json:"refreshAfter"`
	}
)

type (
	UserInfoReq {
	}
	UserInfoResp {
		UserInfo User `json:"userInfo"`
	}
)

type (
	UserUpdateReq {
		Nickname string `json:"nickname"`
		Sex      int64  `json:"sex"`
		Avatar   string `json:"avatar"`
		Info     string `json:"info"`
		Signature string `json:"signature"`
		Longitude float64 `json:"longitude"`
		Latitude float64 `json:"latitude"`
	}

	UserUpdateResp {
	}
)

type (
	SetAdminReq {
		Id int64  `json:"id"`
	}
	SetAdminResp {
	}
)


