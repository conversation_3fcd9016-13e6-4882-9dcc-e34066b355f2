// Code generated by goctl. DO NOT EDIT.

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
	"looklook/common/globalkey"
)

var (
	noticeSubscribePreferenceFieldNames          = builder.RawFieldNames(&NoticeSubscribePreference{})
	noticeSubscribePreferenceRows                = strings.Join(noticeSubscribePreferenceFieldNames, ",")
	noticeSubscribePreferenceRowsExpectAutoSet   = strings.Join(stringx.Remove(noticeSubscribePreferenceFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), ",")
	noticeSubscribePreferenceRowsWithPlaceHolder = strings.Join(stringx.Remove(noticeSubscribePreferenceFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), "=?,") + "=?"

	cacheNoticeNoticeSubscribePreferenceIdPrefix                      = "cache:notice:noticeSubscribePreference:id:"
	cacheNoticeNoticeSubscribePreferenceUserOpenidMsgTemplateIdPrefix = "cache:notice:noticeSubscribePreference:userOpenid:msgTemplateId:"
)

type (
	noticeSubscribePreferenceModel interface {
		Insert(ctx context.Context, data *NoticeSubscribePreference) (sql.Result, error)
		TransInsert(ctx context.Context, session sqlx.Session, data *NoticeSubscribePreference) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*NoticeSubscribePreference, error)
		FindOneByUserOpenidMsgTemplateId(ctx context.Context, userOpenid string, msgTemplateId string) (*NoticeSubscribePreference, error)
		Update(ctx context.Context, data *NoticeSubscribePreference) error
		List(ctx context.Context, page, limit int64) ([]*NoticeSubscribePreference, error)
		TransUpdate(ctx context.Context, session sqlx.Session, data *NoticeSubscribePreference) error
		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		SelectBuilder() squirrel.SelectBuilder
		FindSum(ctx context.Context, sumBuilder squirrel.SelectBuilder, field string) (float64, error)
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder, field string) (int64, error)
		FindAll(ctx context.Context, rowBuilder squirrel.SelectBuilder, orderBy string) ([]*NoticeSubscribePreference, error)
		FindPageListByPage(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*NoticeSubscribePreference, error)
		FindPageListByPageWithTotal(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*NoticeSubscribePreference, int64, error)
		FindPageListByIdDESC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*NoticeSubscribePreference, error)
		FindPageListByIdASC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*NoticeSubscribePreference, error)
		Delete(ctx context.Context, id int64) error
	}

	defaultNoticeSubscribePreferenceModel struct {
		sqlc.CachedConn
		table string
	}

	NoticeSubscribePreference struct {
		Id            int64     `db:"id"`
		UserOpenid    string    `db:"user_openid"`     // 微信侧的用户id，即user_auth表的auth_key
		MsgTemplateId string    `db:"msg_template_id"` // 每种消息对应的微信侧模版id
		AcceptCount   int64     `db:"accept_count"`    // 用户订阅<一次性消息>的次数，0 拒绝，>=1 允许接收
		CreateTime    time.Time `db:"create_time"`
		UpdateTime    time.Time `db:"update_time"`
	}
)

func newNoticeSubscribePreferenceModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultNoticeSubscribePreferenceModel {
	return &defaultNoticeSubscribePreferenceModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`notice_subscribe_preference`",
	}
}

func (m *defaultNoticeSubscribePreferenceModel) Delete(ctx context.Context, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	noticeNoticeSubscribePreferenceIdKey := fmt.Sprintf("%s%v", cacheNoticeNoticeSubscribePreferenceIdPrefix, id)
	noticeNoticeSubscribePreferenceUserOpenidMsgTemplateIdKey := fmt.Sprintf("%s%v:%v", cacheNoticeNoticeSubscribePreferenceUserOpenidMsgTemplateIdPrefix, data.UserOpenid, data.MsgTemplateId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		return conn.ExecCtx(ctx, query, id)
	}, noticeNoticeSubscribePreferenceIdKey, noticeNoticeSubscribePreferenceUserOpenidMsgTemplateIdKey)
	return err
}

func (m *defaultNoticeSubscribePreferenceModel) FindOne(ctx context.Context, id int64) (*NoticeSubscribePreference, error) {
	noticeNoticeSubscribePreferenceIdKey := fmt.Sprintf("%s%v", cacheNoticeNoticeSubscribePreferenceIdPrefix, id)
	var resp NoticeSubscribePreference
	err := m.QueryRowCtx(ctx, &resp, noticeNoticeSubscribePreferenceIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", noticeSubscribePreferenceRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultNoticeSubscribePreferenceModel) FindOneByUserOpenidMsgTemplateId(ctx context.Context, userOpenid string, msgTemplateId string) (*NoticeSubscribePreference, error) {
	noticeNoticeSubscribePreferenceUserOpenidMsgTemplateIdKey := fmt.Sprintf("%s%v:%v", cacheNoticeNoticeSubscribePreferenceUserOpenidMsgTemplateIdPrefix, userOpenid, msgTemplateId)
	var resp NoticeSubscribePreference
	err := m.QueryRowIndexCtx(ctx, &resp, noticeNoticeSubscribePreferenceUserOpenidMsgTemplateIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `user_openid` = ? and `msg_template_id` = ? limit 1", noticeSubscribePreferenceRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, userOpenid, msgTemplateId); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultNoticeSubscribePreferenceModel) Insert(ctx context.Context, data *NoticeSubscribePreference) (sql.Result, error) {
	noticeNoticeSubscribePreferenceIdKey := fmt.Sprintf("%s%v", cacheNoticeNoticeSubscribePreferenceIdPrefix, data.Id)
	noticeNoticeSubscribePreferenceUserOpenidMsgTemplateIdKey := fmt.Sprintf("%s%v:%v", cacheNoticeNoticeSubscribePreferenceUserOpenidMsgTemplateIdPrefix, data.UserOpenid, data.MsgTemplateId)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?)", m.table, noticeSubscribePreferenceRowsExpectAutoSet)
		return conn.ExecCtx(ctx, query, data.UserOpenid, data.MsgTemplateId, data.AcceptCount)
	}, noticeNoticeSubscribePreferenceIdKey, noticeNoticeSubscribePreferenceUserOpenidMsgTemplateIdKey)
	return ret, err
}

func (m *defaultNoticeSubscribePreferenceModel) TransInsert(ctx context.Context, session sqlx.Session, data *NoticeSubscribePreference) (sql.Result, error) {
	noticeNoticeSubscribePreferenceIdKey := fmt.Sprintf("%s%v", cacheNoticeNoticeSubscribePreferenceIdPrefix, data.Id)
	noticeNoticeSubscribePreferenceUserOpenidMsgTemplateIdKey := fmt.Sprintf("%s%v:%v", cacheNoticeNoticeSubscribePreferenceUserOpenidMsgTemplateIdPrefix, data.UserOpenid, data.MsgTemplateId)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?)", m.table, noticeSubscribePreferenceRowsExpectAutoSet)
		return session.ExecCtx(ctx, query, data.UserOpenid, data.MsgTemplateId, data.AcceptCount)
	}, noticeNoticeSubscribePreferenceIdKey, noticeNoticeSubscribePreferenceUserOpenidMsgTemplateIdKey)
	return ret, err
}
func (m *defaultNoticeSubscribePreferenceModel) Update(ctx context.Context, newData *NoticeSubscribePreference) error {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return err
	}

	noticeNoticeSubscribePreferenceIdKey := fmt.Sprintf("%s%v", cacheNoticeNoticeSubscribePreferenceIdPrefix, data.Id)
	noticeNoticeSubscribePreferenceUserOpenidMsgTemplateIdKey := fmt.Sprintf("%s%v:%v", cacheNoticeNoticeSubscribePreferenceUserOpenidMsgTemplateIdPrefix, data.UserOpenid, data.MsgTemplateId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, noticeSubscribePreferenceRowsWithPlaceHolder)
		return conn.ExecCtx(ctx, query, newData.UserOpenid, newData.MsgTemplateId, newData.AcceptCount, newData.Id)
	}, noticeNoticeSubscribePreferenceIdKey, noticeNoticeSubscribePreferenceUserOpenidMsgTemplateIdKey)
	return err
}

func (m *defaultNoticeSubscribePreferenceModel) TransUpdate(ctx context.Context, session sqlx.Session, newData *NoticeSubscribePreference) error {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return err
	}

	noticeNoticeSubscribePreferenceIdKey := fmt.Sprintf("%s%v", cacheNoticeNoticeSubscribePreferenceIdPrefix, data.Id)
	noticeNoticeSubscribePreferenceUserOpenidMsgTemplateIdKey := fmt.Sprintf("%s%v:%v", cacheNoticeNoticeSubscribePreferenceUserOpenidMsgTemplateIdPrefix, data.UserOpenid, data.MsgTemplateId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, noticeSubscribePreferenceRowsWithPlaceHolder)
		return session.ExecCtx(ctx, query, newData.UserOpenid, newData.MsgTemplateId, newData.AcceptCount, newData.Id)
	}, noticeNoticeSubscribePreferenceIdKey, noticeNoticeSubscribePreferenceUserOpenidMsgTemplateIdKey)
	return err
}

func (m *defaultNoticeSubscribePreferenceModel) List(ctx context.Context, page, limit int64) ([]*NoticeSubscribePreference, error) {
	query := fmt.Sprintf("select %s from %s limit ?,?", noticeSubscribePreferenceRows, m.table)
	var resp []*NoticeSubscribePreference
	//err := m.conn.QueryRowsCtx(ctx, &resp, query, (page-1)*limit, limit)
	err := m.QueryRowsNoCacheCtx(ctx, &resp, query, (page-1)*limit, limit)
	return resp, err
}

func (m *defaultNoticeSubscribePreferenceModel) Trans(ctx context.Context, fn func(ctx context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, func(ctx context.Context, session sqlx.Session) error {
		return fn(ctx, session)
	})
}

func (m *defaultNoticeSubscribePreferenceModel) FindSum(ctx context.Context, builder squirrel.SelectBuilder, field string) (float64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindSum Least One Field"), "FindSum Least One Field")
	}

	builder = builder.Columns("IFNULL(SUM(" + field + "),0)")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp float64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultNoticeSubscribePreferenceModel) FindCount(ctx context.Context, builder squirrel.SelectBuilder, field string) (int64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindCount Least One Field"), "FindCount Least One Field")
	}

	builder = builder.Columns("COUNT(" + field + ")")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultNoticeSubscribePreferenceModel) FindAll(ctx context.Context, builder squirrel.SelectBuilder, orderBy string) ([]*NoticeSubscribePreference, error) {

	builder = builder.Columns(noticeSubscribePreferenceRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*NoticeSubscribePreference
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultNoticeSubscribePreferenceModel) FindPageListByPage(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*NoticeSubscribePreference, error) {

	builder = builder.Columns(noticeSubscribePreferenceRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*NoticeSubscribePreference
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultNoticeSubscribePreferenceModel) FindPageListByPageWithTotal(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*NoticeSubscribePreference, int64, error) {

	total, err := m.FindCount(ctx, builder, "id")
	if err != nil {
		return nil, 0, err
	}

	builder = builder.Columns(noticeSubscribePreferenceRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, total, err
	}

	var resp []*NoticeSubscribePreference
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, total, nil
	default:
		return nil, total, err
	}
}

func (m *defaultNoticeSubscribePreferenceModel) FindPageListByIdDESC(ctx context.Context, builder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*NoticeSubscribePreference, error) {

	builder = builder.Columns(noticeSubscribePreferenceRows)

	if preMinId > 0 {
		builder = builder.Where(" id < ? ", preMinId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id DESC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*NoticeSubscribePreference
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultNoticeSubscribePreferenceModel) FindPageListByIdASC(ctx context.Context, builder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*NoticeSubscribePreference, error) {

	builder = builder.Columns(noticeSubscribePreferenceRows)

	if preMaxId > 0 {
		builder = builder.Where(" id > ? ", preMaxId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id ASC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*NoticeSubscribePreference
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultNoticeSubscribePreferenceModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select().From(m.table)
}

func (m *defaultNoticeSubscribePreferenceModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheNoticeNoticeSubscribePreferenceIdPrefix, primary)
}

func (m *defaultNoticeSubscribePreferenceModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", noticeSubscribePreferenceRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultNoticeSubscribePreferenceModel) tableName() string {
	return m.table
}
