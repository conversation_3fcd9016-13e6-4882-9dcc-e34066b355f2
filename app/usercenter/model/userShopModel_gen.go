// Code generated by goctl. DO NOT EDIT.

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	userShopFieldNames          = builder.RawFieldNames(&UserShop{})
	userShopRows                = strings.Join(userShopFieldNames, ",")
	userShopRowsExpectAutoSet   = strings.Join(stringx.Remove(userShopFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), ",")
	userShopRowsWithPlaceHolder = strings.Join(stringx.Remove(userShopFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), "=?,") + "=?"

	cacheLooklookUsercenterUserShopIdPrefix = "cache:looklookUsercenter:userShop:id:"
)

type (
	userShopModel interface {
		Insert(ctx context.Context, data *UserShop) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*UserShop, error)
		Update(ctx context.Context, data *UserShop) error
		Delete(ctx context.Context, id int64) error
	}

	defaultUserShopModel struct {
		sqlc.CachedConn
		table string
	}

	UserShop struct {
		Id         int64        `db:"id"`
		UserId     int64        `db:"user_id"`
		Name       string       `db:"name"`
		Location   float64      `db:"location"`
		CreateTime time.Time    `db:"create_time"`
		UpdateTime time.Time    `db:"update_time"`
		DeleteTime sql.NullTime `db:"delete_time"`
	}
)

func newUserShopModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultUserShopModel {
	return &defaultUserShopModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`user_shop`",
	}
}

func (m *defaultUserShopModel) Delete(ctx context.Context, id int64) error {
	looklookUsercenterUserShopIdKey := fmt.Sprintf("%s%v", cacheLooklookUsercenterUserShopIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		return conn.ExecCtx(ctx, query, id)
	}, looklookUsercenterUserShopIdKey)
	return err
}

func (m *defaultUserShopModel) FindOne(ctx context.Context, id int64) (*UserShop, error) {
	looklookUsercenterUserShopIdKey := fmt.Sprintf("%s%v", cacheLooklookUsercenterUserShopIdPrefix, id)
	var resp UserShop
	err := m.QueryRowCtx(ctx, &resp, looklookUsercenterUserShopIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", userShopRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserShopModel) Insert(ctx context.Context, data *UserShop) (sql.Result, error) {
	looklookUsercenterUserShopIdKey := fmt.Sprintf("%s%v", cacheLooklookUsercenterUserShopIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, userShopRowsExpectAutoSet)
		return conn.ExecCtx(ctx, query, data.UserId, data.Name, data.Location, data.DeleteTime)
	}, looklookUsercenterUserShopIdKey)
	return ret, err
}

func (m *defaultUserShopModel) Update(ctx context.Context, data *UserShop) error {
	looklookUsercenterUserShopIdKey := fmt.Sprintf("%s%v", cacheLooklookUsercenterUserShopIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, userShopRowsWithPlaceHolder)
		return conn.ExecCtx(ctx, query, data.UserId, data.Name, data.Location, data.DeleteTime, data.Id)
	}, looklookUsercenterUserShopIdKey)
	return err
}

func (m *defaultUserShopModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheLooklookUsercenterUserShopIdPrefix, primary)
}

func (m *defaultUserShopModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", userShopRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultUserShopModel) tableName() string {
	return m.table
}
