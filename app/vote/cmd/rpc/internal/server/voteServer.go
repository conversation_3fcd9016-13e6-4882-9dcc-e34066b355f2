// Code generated by goctl. DO NOT EDIT.
// Source: vote.proto

package server

import (
	"context"

	"looklook/app/vote/cmd/rpc/internal/logic"
	"looklook/app/vote/cmd/rpc/internal/svc"
	"looklook/app/vote/cmd/rpc/pb"
)

type VoteServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedVoteServer
}

func NewVoteServer(svcCtx *svc.ServiceContext) *VoteServer {
	return &VoteServer{
		svcCtx: svcCtx,
	}
}

// -----------------------投票表-----------------------
func (s *VoteServer) AddVoteConfig(ctx context.Context, in *pb.AddVoteConfigReq) (*pb.AddVoteConfigResp, error) {
	l := logic.NewAddVoteConfigLogic(ctx, s.svcCtx)
	return l.AddVoteConfig(in)
}

func (s *VoteServer) UpdateVoteConfig(ctx context.Context, in *pb.UpdateVoteConfigReq) (*pb.UpdateVoteConfigResp, error) {
	l := logic.NewUpdateVoteConfigLogic(ctx, s.svcCtx)
	return l.UpdateVoteConfig(in)
}

func (s *VoteServer) DelVoteConfig(ctx context.Context, in *pb.DelVoteConfigReq) (*pb.DelVoteConfigResp, error) {
	l := logic.NewDelVoteConfigLogic(ctx, s.svcCtx)
	return l.DelVoteConfig(in)
}

func (s *VoteServer) GetVoteConfigById(ctx context.Context, in *pb.GetVoteConfigByIdReq) (*pb.GetVoteConfigByIdResp, error) {
	l := logic.NewGetVoteConfigByIdLogic(ctx, s.svcCtx)
	return l.GetVoteConfigById(in)
}

func (s *VoteServer) SearchVoteConfig(ctx context.Context, in *pb.SearchVoteConfigReq) (*pb.SearchVoteConfigResp, error) {
	l := logic.NewSearchVoteConfigLogic(ctx, s.svcCtx)
	return l.SearchVoteConfig(in)
}

// -----------------------投票记录表-----------------------
func (s *VoteServer) AddVoteRecord(ctx context.Context, in *pb.AddVoteRecordReq) (*pb.AddVoteRecordResp, error) {
	l := logic.NewAddVoteRecordLogic(ctx, s.svcCtx)
	return l.AddVoteRecord(in)
}

func (s *VoteServer) UpdateVoteRecord(ctx context.Context, in *pb.UpdateVoteRecordReq) (*pb.UpdateVoteRecordResp, error) {
	l := logic.NewUpdateVoteRecordLogic(ctx, s.svcCtx)
	return l.UpdateVoteRecord(in)
}

func (s *VoteServer) DelVoteRecord(ctx context.Context, in *pb.DelVoteRecordReq) (*pb.DelVoteRecordResp, error) {
	l := logic.NewDelVoteRecordLogic(ctx, s.svcCtx)
	return l.DelVoteRecord(in)
}

func (s *VoteServer) GetVoteRecordById(ctx context.Context, in *pb.GetVoteRecordByIdReq) (*pb.GetVoteRecordByIdResp, error) {
	l := logic.NewGetVoteRecordByIdLogic(ctx, s.svcCtx)
	return l.GetVoteRecordById(in)
}

func (s *VoteServer) GetVoteRecordDetail(ctx context.Context, in *pb.GetVoteRecordDetailReq) (*pb.GetVoteRecordDetailResp, error) {
	l := logic.NewGetVoteRecordDetailLogic(ctx, s.svcCtx)
	return l.GetVoteRecordDetail(in)
}

func (s *VoteServer) GetUserVoteRecordDetail(ctx context.Context, in *pb.GetUserVoteRecordDetailReq) (*pb.GetUserVoteRecordDetailResp, error) {
	l := logic.NewGetUserVoteRecordDetailLogic(ctx, s.svcCtx)
	return l.GetUserVoteRecordDetail(in)
}

func (s *VoteServer) SearchVoteRecord(ctx context.Context, in *pb.SearchVoteRecordReq) (*pb.SearchVoteRecordResp, error) {
	l := logic.NewSearchVoteRecordLogic(ctx, s.svcCtx)
	return l.SearchVoteRecord(in)
}
