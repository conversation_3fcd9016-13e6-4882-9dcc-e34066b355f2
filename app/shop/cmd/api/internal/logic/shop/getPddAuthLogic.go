package shop

import (
	"context"
	"fmt"
	"github.com/valyala/fastjson"
	"looklook/app/shop/cmd/api/internal/svc"
	"looklook/app/shop/cmd/api/internal/types"
	"looklook/common/ctxdata"
	"looklook/common/tool"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetPddAuthLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetPddAuthLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPddAuthLogic {
	return &GetPddAuthLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetPddAuthLogic) GetPddAuth(req *types.PddAuthReq) (resp *types.PddAuthResp, err error) {
	// todo: add your logic here and delete this line
	// -1、对入参进行处理，生成sign（按照入参的字母顺序排序，kv拼接，头部及尾部分别拼接client_secret，MD5，大写转换）
	// 0、参数校验，判断当前用户是否备案 调用http请求 pdd.ddk.member.authority.query  clientid client_secret custom_parameters pid timestamp 得到的sign  返回的结果中的bind字段为1表示已备案 0 表示未备案，需要备案
	// 1、没有授权，进行授权接口的调用 pdd.ddk.rp.prom.url.generate 生成备案链接 clientid client_secret custom_parameters pid timestamp 得到的sign 返回的结果中的url即为备案链接，前端跳转到备案链接进行备案
	// 0和1步具有普遍性，可以封装成一个接口给前端调用
	// 1 获取用户id
	userId := ctxdata.GetUidFromCtx(l.ctx)
	logx.Error("Testt-1 userId", userId)

	clientSecret := "4ded09b0a14eb72196f790a93c06eafcfb75ceb8"
	pid := "42316735_301157319"

	// 0、参数校验，判断当前用户是否备案 调用http请求 pdd.ddk.member.authority.query  clientid client_secret custom_parameters pid timestamp 得到的sign  返回的结果中的bind字段为1表示已备案 0 表示未备案，需要备案
	params := map[string]string{
		"custom_parameters": fmt.Sprintf("{\"uid\":%d}", userId),
		"type":              "pdd.ddk.member.authority.query",
		"pid":               pid,
		"client_secret":     clientSecret,
	}
	body, err := tool.SendRequestToDDJB(params, "POST")
	if err != nil {
		return nil, err
	}
	// 打印响应体
	//logx.Errorf("Testt-5 Response body: %s", string(body))

	bind := fastjson.GetInt(body, "authority_query_response", "bind")
	if bind == 0 {
		// 1、没有授权，进行授权接口的调用 pdd.ddk.rp.prom.url.generate 生成备案链接 clientid client_secret custom_parameters pid timestamp 得到的sign 返回的结果中的url即为备案链接，前端跳转到备案链接进行备案
		params := map[string]string{
			"custom_parameters": fmt.Sprintf("{\"uid\":%d}", userId),
			"channel_type":      "10",
			"client_secret":     clientSecret,
			"p_id_list":         fmt.Sprintf("[\"%s\"]", pid),
			"type":              "pdd.ddk.rp.prom.url.generate", // ["42316735_301157319"]
		}

		logx.Errorf("Testt-14 params: %v", params["p_id_list"])

		body1, err := tool.SendRequestToDDJB(params, "POST")
		if err != nil {
			return nil, err
		}

		// 打印响应体
		logx.Errorf("Testt-6 Response body: %v", string(body1))
		return &types.PddAuthResp{
			Bind:      int64(bind),
			MobileURL: string(body1),
		}, nil
	}

	return &types.PddAuthResp{
		Bind: int64(bind),
	}, nil
}
