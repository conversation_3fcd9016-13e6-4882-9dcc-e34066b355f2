syntax = "v1"

info (
	title:   "抽奖服务"
	desc:    "抽奖服务"
	author:  "王中阳"
	email:   "<EMAIL>"
	version: "v1"
)

import (
	"lottery/lottery.api"
	"lotteryParticipation/lotteryParticipation.api"
)

//=====================================> lottery-lottery v1 <=================================
//no need login
@server (
	prefix: lottery/v1
	group:  lottery
)
service lottery {
	@doc "测试Validator"
	@handler TestValidator
	post /lottery/TestValidator (TestReq) returns (TestResp)

	@doc "lottery list"
	@handler lotteryList
	post /lottery/lotteryList (LotteryListReq) returns (LotteryListResp)

	@doc "抽奖人"
	@handler searchParticipation
	post /lottery/participations (SearchLotteryParticipationReq) returns (SearchLotteryParticipationResp)

	@doc "打卡任务类型"
	@handler clockTaskTypeList
	get /lottery/clockTaskTypeList (ClockTaskTypeListReq) returns (ClockTaskTypeListResp)

	@doc "增加概率类型"
	@handler chanceTypeList
	get /lottery/chanceTypeList (ChanceTypeListReq) returns (ChanceTypeListResp)

	@doc "获取当前抽奖中奖者名单"
	@handler getLotteryWinList2
	post /lottery/getLotteryWinnersList (GetLotteryWinList2Req) returns (GetLotteryWinList2Resp)
}

//need login
@server (
	prefix: lottery/v1
	group:  lottery
	jwt:    JwtAuth
)
service lottery {
	@doc "发起抽奖"
	@handler createLottery
	post /lottery/createLottery (CreateLotteryReq) returns (CreateLotteryResp)

	@doc "发布抽奖"
	@handler updateLottery
	post /lottery/updateLottery (UpdateLotteryReq) returns (UpdateLotteryResp)

	@doc "获取当前用户中奖列表"
	@handler getLotteryWinList
	post /lottery/getLotteryWinList (GetLotteryWinListReq) returns (GetLotteryWinListResp)

	@doc "lottery detail"
	@handler lotteryDetail
	post /lottery/lotteryDetail (LotteryDetailReq) returns (LotteryDetailResp)

	@doc "CheckIsParticipated"
	@handler CheckIsParticipated
	post /lottery/CheckIsParticipated (CheckIsParticipatedReq) returns (CheckIsParticipatedResp)

	@doc "参与抽奖"
	@handler addLotteryParticipation
	post /lottery/participation (AddLotteryParticipationReq) returns (AddLotteryParticipationResp)

	@doc "lottery setIsSelected"
	@handler setLotteryIsSelected
	post /lottery/setLotteryIsSelected (SetLotteryIsSelectedReq) returns (SetLotteryIsSelectedResp)

	@doc "登录后获取抽奖列表"
	@handler lotteryListAfterLogin
	post /lottery/lotteryListAfterLogin (LotteryListReq) returns (LotteryListResp)

	@doc "判断当前用户当前抽奖是否中奖"
	@handler checkIsWin
	post /lottery/checkIsWin (CheckIsWinReq) returns (CheckIsWinResp)

	@doc "完成打卡任务"
	@handler createClockTaskRecord
	post /lottery/createClockTaskRecord (CreateClockTaskRecordReq) returns (CreateClockTaskRecordResp)

	@doc "获取当前用户全部/发起/中奖的抽奖列表"
	@handler getLotteryListByUserId
	post /lottery/getLotteryListByUserId (GetLotteryListByUserIdReq) returns (GetLotteryListByUserIdResp)
}

//no need login
//@server (
// prefix: lottery/v1
// group:  lottery
//)
//service lottery {
// @doc "lottery sponsor"
// @handler lotterySponsor
// post /lottery/lotterySponsor (LotterySponsorReq) returns (LotterySponsorResp)
//}
