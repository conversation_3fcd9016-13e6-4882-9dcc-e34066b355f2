// Code generated by goctl. DO NOT EDIT.

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
	"looklook/common/globalkey"
)

var (
	praiseFieldNames          = builder.RawFieldNames(&Praise{})
	praiseRows                = strings.Join(praiseFieldNames, ",")
	praiseRowsExpectAutoSet   = strings.Join(stringx.Remove(praiseFieldNames, "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), ",")
	praiseRowsWithPlaceHolder = strings.Join(stringx.Remove(praiseFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), "=?,") + "=?"

	cacheCommentPraiseIdPrefix        = "cache:comment:praise:id:"
	cacheCommentPraiseCommentIdPrefix = "cache:comment:praise:commentId:"
)

type (
	praiseModel interface {
		Insert(ctx context.Context, data *Praise) (sql.Result, error)
		TransInsert(ctx context.Context, session sqlx.Session, data *Praise) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Praise, error)
		FindOneByCommentId(ctx context.Context, commentId int64) (*Praise, error)
		Update(ctx context.Context, data *Praise) error
		List(ctx context.Context, page, limit int64) ([]*Praise, error)
		TransUpdate(ctx context.Context, session sqlx.Session, data *Praise) error
		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		SelectBuilder() squirrel.SelectBuilder
		FindSum(ctx context.Context, sumBuilder squirrel.SelectBuilder, field string) (float64, error)
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder, field string) (int64, error)
		FindAll(ctx context.Context, rowBuilder squirrel.SelectBuilder, orderBy string) ([]*Praise, error)
		FindPageListByPage(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*Praise, error)
		FindPageListByPageWithTotal(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*Praise, int64, error)
		FindPageListByIdDESC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*Praise, error)
		FindPageListByIdASC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*Praise, error)
		Delete(ctx context.Context, id int64) error
	}

	defaultPraiseModel struct {
		sqlc.CachedConn
		table string
	}

	Praise struct {
		Id         int64        `db:"id"`
		UserId     int64        `db:"user_id"`    // 评论者id
		CommentId  int64        `db:"comment_id"` // 评论id
		CreateTime time.Time    `db:"create_time"`
		UpdateTime time.Time    `db:"update_time"`
		DeleteTime sql.NullTime `db:"delete_time"`
		DelState   int64        `db:"del_state"`
	}
)

func newPraiseModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultPraiseModel {
	return &defaultPraiseModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`praise`",
	}
}

func (m *defaultPraiseModel) Delete(ctx context.Context, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	commentPraiseCommentIdKey := fmt.Sprintf("%s%v", cacheCommentPraiseCommentIdPrefix, data.CommentId)
	commentPraiseIdKey := fmt.Sprintf("%s%v", cacheCommentPraiseIdPrefix, id)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		return conn.ExecCtx(ctx, query, id)
	}, commentPraiseCommentIdKey, commentPraiseIdKey)
	return err
}

func (m *defaultPraiseModel) FindOne(ctx context.Context, id int64) (*Praise, error) {
	commentPraiseIdKey := fmt.Sprintf("%s%v", cacheCommentPraiseIdPrefix, id)
	var resp Praise
	err := m.QueryRowCtx(ctx, &resp, commentPraiseIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", praiseRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPraiseModel) FindOneByCommentId(ctx context.Context, commentId int64) (*Praise, error) {
	commentPraiseCommentIdKey := fmt.Sprintf("%s%v", cacheCommentPraiseCommentIdPrefix, commentId)
	var resp Praise
	err := m.QueryRowIndexCtx(ctx, &resp, commentPraiseCommentIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `comment_id` = ? limit 1", praiseRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, commentId); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPraiseModel) Insert(ctx context.Context, data *Praise) (sql.Result, error) {
	commentPraiseCommentIdKey := fmt.Sprintf("%s%v", cacheCommentPraiseCommentIdPrefix, data.CommentId)
	commentPraiseIdKey := fmt.Sprintf("%s%v", cacheCommentPraiseIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?)", m.table, praiseRowsExpectAutoSet)
		return conn.ExecCtx(ctx, query, data.Id, data.UserId, data.CommentId, data.DeleteTime, data.DelState)
	}, commentPraiseCommentIdKey, commentPraiseIdKey)
	return ret, err
}

func (m *defaultPraiseModel) TransInsert(ctx context.Context, session sqlx.Session, data *Praise) (sql.Result, error) {
	commentPraiseCommentIdKey := fmt.Sprintf("%s%v", cacheCommentPraiseCommentIdPrefix, data.CommentId)
	commentPraiseIdKey := fmt.Sprintf("%s%v", cacheCommentPraiseIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?)", m.table, praiseRowsExpectAutoSet)
		return session.ExecCtx(ctx, query, data.Id, data.UserId, data.CommentId, data.DeleteTime, data.DelState)
	}, commentPraiseCommentIdKey, commentPraiseIdKey)
	return ret, err
}
func (m *defaultPraiseModel) Update(ctx context.Context, newData *Praise) error {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return err
	}

	commentPraiseCommentIdKey := fmt.Sprintf("%s%v", cacheCommentPraiseCommentIdPrefix, data.CommentId)
	commentPraiseIdKey := fmt.Sprintf("%s%v", cacheCommentPraiseIdPrefix, data.Id)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, praiseRowsWithPlaceHolder)
		return conn.ExecCtx(ctx, query, newData.UserId, newData.CommentId, newData.DeleteTime, newData.DelState, newData.Id)
	}, commentPraiseCommentIdKey, commentPraiseIdKey)
	return err
}

func (m *defaultPraiseModel) TransUpdate(ctx context.Context, session sqlx.Session, newData *Praise) error {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return err
	}

	commentPraiseCommentIdKey := fmt.Sprintf("%s%v", cacheCommentPraiseCommentIdPrefix, data.CommentId)
	commentPraiseIdKey := fmt.Sprintf("%s%v", cacheCommentPraiseIdPrefix, data.Id)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, praiseRowsWithPlaceHolder)
		return session.ExecCtx(ctx, query, newData.UserId, newData.CommentId, newData.DeleteTime, newData.DelState, newData.Id)
	}, commentPraiseCommentIdKey, commentPraiseIdKey)
	return err
}

func (m *defaultPraiseModel) List(ctx context.Context, page, limit int64) ([]*Praise, error) {
	query := fmt.Sprintf("select %s from %s limit ?,?", praiseRows, m.table)
	var resp []*Praise
	//err := m.conn.QueryRowsCtx(ctx, &resp, query, (page-1)*limit, limit)
	err := m.QueryRowsNoCacheCtx(ctx, &resp, query, (page-1)*limit, limit)
	return resp, err
}

func (m *defaultPraiseModel) Trans(ctx context.Context, fn func(ctx context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, func(ctx context.Context, session sqlx.Session) error {
		return fn(ctx, session)
	})
}

func (m *defaultPraiseModel) FindSum(ctx context.Context, builder squirrel.SelectBuilder, field string) (float64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindSum Least One Field"), "FindSum Least One Field")
	}

	builder = builder.Columns("IFNULL(SUM(" + field + "),0)")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp float64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultPraiseModel) FindCount(ctx context.Context, builder squirrel.SelectBuilder, field string) (int64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindCount Least One Field"), "FindCount Least One Field")
	}

	builder = builder.Columns("COUNT(" + field + ")")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultPraiseModel) FindAll(ctx context.Context, builder squirrel.SelectBuilder, orderBy string) ([]*Praise, error) {

	builder = builder.Columns(praiseRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*Praise
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultPraiseModel) FindPageListByPage(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*Praise, error) {

	builder = builder.Columns(praiseRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*Praise
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultPraiseModel) FindPageListByPageWithTotal(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*Praise, int64, error) {

	total, err := m.FindCount(ctx, builder, "id")
	if err != nil {
		return nil, 0, err
	}

	builder = builder.Columns(praiseRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, total, err
	}

	var resp []*Praise
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, total, nil
	default:
		return nil, total, err
	}
}

func (m *defaultPraiseModel) FindPageListByIdDESC(ctx context.Context, builder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*Praise, error) {

	builder = builder.Columns(praiseRows)

	if preMinId > 0 {
		builder = builder.Where(" id < ? ", preMinId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id DESC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*Praise
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultPraiseModel) FindPageListByIdASC(ctx context.Context, builder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*Praise, error) {

	builder = builder.Columns(praiseRows)

	if preMaxId > 0 {
		builder = builder.Where(" id > ? ", preMaxId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id ASC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*Praise
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultPraiseModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select().From(m.table)
}

func (m *defaultPraiseModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheCommentPraiseIdPrefix, primary)
}

func (m *defaultPraiseModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", praiseRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultPraiseModel) tableName() string {
	return m.table
}
