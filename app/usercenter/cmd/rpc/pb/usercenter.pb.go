// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v3.19.4
// source: usercenter.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// --------------------------------用户表--------------------------------
type User struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               int64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                              //id
	CreateTime       int64   `protobuf:"varint,2,opt,name=createTime,proto3" json:"createTime,omitempty"`              //createTime
	UpdateTime       int64   `protobuf:"varint,3,opt,name=updateTime,proto3" json:"updateTime,omitempty"`              //updateTime
	Mobile           string  `protobuf:"bytes,4,opt,name=mobile,proto3" json:"mobile,omitempty"`                       //mobile
	Password         string  `protobuf:"bytes,5,opt,name=password,proto3" json:"password,omitempty"`                   //password
	Nickname         string  `protobuf:"bytes,6,opt,name=nickname,proto3" json:"nickname,omitempty"`                   //nickname
	Sex              int64   `protobuf:"varint,7,opt,name=sex,proto3" json:"sex,omitempty"`                            //性别 0:男 1:女
	Avatar           string  `protobuf:"bytes,8,opt,name=avatar,proto3" json:"avatar,omitempty"`                       //avatar
	Info             string  `protobuf:"bytes,9,opt,name=info,proto3" json:"info,omitempty"`                           //info
	IsAdmin          int64   `protobuf:"varint,10,opt,name=isAdmin,proto3" json:"isAdmin,omitempty"`                   //是否管理员 1是 0否
	Signature        string  `protobuf:"bytes,11,opt,name=signature,proto3" json:"signature,omitempty"`                //个性签名
	LocationName     string  `protobuf:"bytes,12,opt,name=locationName,proto3" json:"locationName,omitempty"`          //地址名称
	Longitude        float64 `protobuf:"fixed64,13,opt,name=longitude,proto3" json:"longitude,omitempty"`              //经度
	Latitude         float64 `protobuf:"fixed64,14,opt,name=latitude,proto3" json:"latitude,omitempty"`                //纬度
	TotalPrize       int64   `protobuf:"varint,15,opt,name=totalPrize,proto3" json:"totalPrize,omitempty"`             //累计奖品
	Fans             int64   `protobuf:"varint,16,opt,name=fans,proto3" json:"fans,omitempty"`                         //粉丝数量
	AllLottery       int64   `protobuf:"varint,17,opt,name=allLottery,proto3" json:"allLottery,omitempty"`             //全部抽奖包含我发起的、我中奖的
	InitiationRecord int64   `protobuf:"varint,18,opt,name=initiationRecord,proto3" json:"initiationRecord,omitempty"` //发起抽奖记录
	WinningRecord    int64   `protobuf:"varint,19,opt,name=winningRecord,proto3" json:"winningRecord,omitempty"`       //中奖记录
}

func (x *User) Reset() {
	*x = User{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{0}
}

func (x *User) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *User) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *User) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *User) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *User) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *User) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *User) GetSex() int64 {
	if x != nil {
		return x.Sex
	}
	return 0
}

func (x *User) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *User) GetInfo() string {
	if x != nil {
		return x.Info
	}
	return ""
}

func (x *User) GetIsAdmin() int64 {
	if x != nil {
		return x.IsAdmin
	}
	return 0
}

func (x *User) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

func (x *User) GetLocationName() string {
	if x != nil {
		return x.LocationName
	}
	return ""
}

func (x *User) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *User) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *User) GetTotalPrize() int64 {
	if x != nil {
		return x.TotalPrize
	}
	return 0
}

func (x *User) GetFans() int64 {
	if x != nil {
		return x.Fans
	}
	return 0
}

func (x *User) GetAllLottery() int64 {
	if x != nil {
		return x.AllLottery
	}
	return 0
}

func (x *User) GetInitiationRecord() int64 {
	if x != nil {
		return x.InitiationRecord
	}
	return 0
}

func (x *User) GetWinningRecord() int64 {
	if x != nil {
		return x.WinningRecord
	}
	return 0
}

type AddUserReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mobile           string  `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile,omitempty"`                       //mobile
	Password         string  `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`                   //password
	Nickname         string  `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`                   //nickname
	Sex              int64   `protobuf:"varint,4,opt,name=sex,proto3" json:"sex,omitempty"`                            //性别 0:男 1:女
	Avatar           string  `protobuf:"bytes,5,opt,name=avatar,proto3" json:"avatar,omitempty"`                       //avatar
	Info             string  `protobuf:"bytes,6,opt,name=info,proto3" json:"info,omitempty"`                           //info
	IsAdmin          int64   `protobuf:"varint,7,opt,name=isAdmin,proto3" json:"isAdmin,omitempty"`                    //是否管理员 1是 0否
	Signature        string  `protobuf:"bytes,8,opt,name=signature,proto3" json:"signature,omitempty"`                 //个性签名
	LocationName     string  `protobuf:"bytes,9,opt,name=locationName,proto3" json:"locationName,omitempty"`           //地址名称
	Longitude        float64 `protobuf:"fixed64,10,opt,name=longitude,proto3" json:"longitude,omitempty"`              //经度
	Latitude         float64 `protobuf:"fixed64,11,opt,name=latitude,proto3" json:"latitude,omitempty"`                //纬度
	TotalPrize       int64   `protobuf:"varint,12,opt,name=totalPrize,proto3" json:"totalPrize,omitempty"`             //累计奖品
	Fans             int64   `protobuf:"varint,13,opt,name=fans,proto3" json:"fans,omitempty"`                         //粉丝数量
	AllLottery       int64   `protobuf:"varint,14,opt,name=allLottery,proto3" json:"allLottery,omitempty"`             //全部抽奖包含我发起的、我中奖的
	InitiationRecord int64   `protobuf:"varint,15,opt,name=initiationRecord,proto3" json:"initiationRecord,omitempty"` //发起抽奖记录
	WinningRecord    int64   `protobuf:"varint,16,opt,name=winningRecord,proto3" json:"winningRecord,omitempty"`       //中奖记录
}

func (x *AddUserReq) Reset() {
	*x = AddUserReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddUserReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserReq) ProtoMessage() {}

func (x *AddUserReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserReq.ProtoReflect.Descriptor instead.
func (*AddUserReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{1}
}

func (x *AddUserReq) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *AddUserReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *AddUserReq) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *AddUserReq) GetSex() int64 {
	if x != nil {
		return x.Sex
	}
	return 0
}

func (x *AddUserReq) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *AddUserReq) GetInfo() string {
	if x != nil {
		return x.Info
	}
	return ""
}

func (x *AddUserReq) GetIsAdmin() int64 {
	if x != nil {
		return x.IsAdmin
	}
	return 0
}

func (x *AddUserReq) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

func (x *AddUserReq) GetLocationName() string {
	if x != nil {
		return x.LocationName
	}
	return ""
}

func (x *AddUserReq) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *AddUserReq) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *AddUserReq) GetTotalPrize() int64 {
	if x != nil {
		return x.TotalPrize
	}
	return 0
}

func (x *AddUserReq) GetFans() int64 {
	if x != nil {
		return x.Fans
	}
	return 0
}

func (x *AddUserReq) GetAllLottery() int64 {
	if x != nil {
		return x.AllLottery
	}
	return 0
}

func (x *AddUserReq) GetInitiationRecord() int64 {
	if x != nil {
		return x.InitiationRecord
	}
	return 0
}

func (x *AddUserReq) GetWinningRecord() int64 {
	if x != nil {
		return x.WinningRecord
	}
	return 0
}

type AddUserResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddUserResp) Reset() {
	*x = AddUserResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddUserResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserResp) ProtoMessage() {}

func (x *AddUserResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserResp.ProtoReflect.Descriptor instead.
func (*AddUserResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{2}
}

type UpdateUserReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               int64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                              //id
	Mobile           string  `protobuf:"bytes,2,opt,name=mobile,proto3" json:"mobile,omitempty"`                       //mobile
	Password         string  `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`                   //password
	Nickname         string  `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`                   //nickname
	Sex              int64   `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`                            //性别 0:男 1:女
	Avatar           string  `protobuf:"bytes,6,opt,name=avatar,proto3" json:"avatar,omitempty"`                       //avatar
	Info             string  `protobuf:"bytes,7,opt,name=info,proto3" json:"info,omitempty"`                           //info
	IsAdmin          int64   `protobuf:"varint,8,opt,name=isAdmin,proto3" json:"isAdmin,omitempty"`                    //是否管理员 1是 0否
	Signature        string  `protobuf:"bytes,9,opt,name=signature,proto3" json:"signature,omitempty"`                 //个性签名
	LocationName     string  `protobuf:"bytes,10,opt,name=locationName,proto3" json:"locationName,omitempty"`          //地址名称
	Longitude        float64 `protobuf:"fixed64,11,opt,name=longitude,proto3" json:"longitude,omitempty"`              //经度
	Latitude         float64 `protobuf:"fixed64,12,opt,name=latitude,proto3" json:"latitude,omitempty"`                //纬度
	TotalPrize       int64   `protobuf:"varint,13,opt,name=totalPrize,proto3" json:"totalPrize,omitempty"`             //累计奖品
	Fans             int64   `protobuf:"varint,14,opt,name=fans,proto3" json:"fans,omitempty"`                         //粉丝数量
	AllLottery       int64   `protobuf:"varint,15,opt,name=allLottery,proto3" json:"allLottery,omitempty"`             //全部抽奖包含我发起的、我中奖的
	InitiationRecord int64   `protobuf:"varint,16,opt,name=initiationRecord,proto3" json:"initiationRecord,omitempty"` //发起抽奖记录
	WinningRecord    int64   `protobuf:"varint,17,opt,name=winningRecord,proto3" json:"winningRecord,omitempty"`       //中奖记录
}

func (x *UpdateUserReq) Reset() {
	*x = UpdateUserReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserReq) ProtoMessage() {}

func (x *UpdateUserReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserReq.ProtoReflect.Descriptor instead.
func (*UpdateUserReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateUserReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateUserReq) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *UpdateUserReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *UpdateUserReq) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *UpdateUserReq) GetSex() int64 {
	if x != nil {
		return x.Sex
	}
	return 0
}

func (x *UpdateUserReq) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UpdateUserReq) GetInfo() string {
	if x != nil {
		return x.Info
	}
	return ""
}

func (x *UpdateUserReq) GetIsAdmin() int64 {
	if x != nil {
		return x.IsAdmin
	}
	return 0
}

func (x *UpdateUserReq) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

func (x *UpdateUserReq) GetLocationName() string {
	if x != nil {
		return x.LocationName
	}
	return ""
}

func (x *UpdateUserReq) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *UpdateUserReq) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *UpdateUserReq) GetTotalPrize() int64 {
	if x != nil {
		return x.TotalPrize
	}
	return 0
}

func (x *UpdateUserReq) GetFans() int64 {
	if x != nil {
		return x.Fans
	}
	return 0
}

func (x *UpdateUserReq) GetAllLottery() int64 {
	if x != nil {
		return x.AllLottery
	}
	return 0
}

func (x *UpdateUserReq) GetInitiationRecord() int64 {
	if x != nil {
		return x.InitiationRecord
	}
	return 0
}

func (x *UpdateUserReq) GetWinningRecord() int64 {
	if x != nil {
		return x.WinningRecord
	}
	return 0
}

type UpdateUserResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateUserResp) Reset() {
	*x = UpdateUserResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserResp) ProtoMessage() {}

func (x *UpdateUserResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserResp.ProtoReflect.Descriptor instead.
func (*UpdateUserResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{4}
}

type DelUserReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *DelUserReq) Reset() {
	*x = DelUserReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelUserReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelUserReq) ProtoMessage() {}

func (x *DelUserReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelUserReq.ProtoReflect.Descriptor instead.
func (*DelUserReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{5}
}

func (x *DelUserReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DelUserResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DelUserResp) Reset() {
	*x = DelUserResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelUserResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelUserResp) ProtoMessage() {}

func (x *DelUserResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelUserResp.ProtoReflect.Descriptor instead.
func (*DelUserResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{6}
}

type GetUserByIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *GetUserByIdReq) Reset() {
	*x = GetUserByIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserByIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserByIdReq) ProtoMessage() {}

func (x *GetUserByIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserByIdReq.ProtoReflect.Descriptor instead.
func (*GetUserByIdReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{7}
}

func (x *GetUserByIdReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetUserByIdResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	User *User `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"` //user
}

func (x *GetUserByIdResp) Reset() {
	*x = GetUserByIdResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserByIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserByIdResp) ProtoMessage() {}

func (x *GetUserByIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserByIdResp.ProtoReflect.Descriptor instead.
func (*GetUserByIdResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{8}
}

func (x *GetUserByIdResp) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

type SearchUserReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page             int64   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`                          //page
	Limit            int64   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`                        //limit
	Id               int64   `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`                              //id
	CreateTime       int64   `protobuf:"varint,4,opt,name=createTime,proto3" json:"createTime,omitempty"`              //createTime
	UpdateTime       int64   `protobuf:"varint,5,opt,name=updateTime,proto3" json:"updateTime,omitempty"`              //updateTime
	Mobile           string  `protobuf:"bytes,6,opt,name=mobile,proto3" json:"mobile,omitempty"`                       //mobile
	Password         string  `protobuf:"bytes,7,opt,name=password,proto3" json:"password,omitempty"`                   //password
	Nickname         string  `protobuf:"bytes,8,opt,name=nickname,proto3" json:"nickname,omitempty"`                   //nickname
	Sex              int64   `protobuf:"varint,9,opt,name=sex,proto3" json:"sex,omitempty"`                            //性别 0:男 1:女
	Avatar           string  `protobuf:"bytes,10,opt,name=avatar,proto3" json:"avatar,omitempty"`                      //avatar
	Info             string  `protobuf:"bytes,11,opt,name=info,proto3" json:"info,omitempty"`                          //info
	IsAdmin          int64   `protobuf:"varint,12,opt,name=isAdmin,proto3" json:"isAdmin,omitempty"`                   //是否管理员 1是 0否
	Signature        string  `protobuf:"bytes,13,opt,name=signature,proto3" json:"signature,omitempty"`                //个性签名
	LocationName     string  `protobuf:"bytes,14,opt,name=locationName,proto3" json:"locationName,omitempty"`          //地址名称
	Longitude        float64 `protobuf:"fixed64,15,opt,name=longitude,proto3" json:"longitude,omitempty"`              //经度
	Latitude         float64 `protobuf:"fixed64,16,opt,name=latitude,proto3" json:"latitude,omitempty"`                //纬度
	TotalPrize       int64   `protobuf:"varint,17,opt,name=totalPrize,proto3" json:"totalPrize,omitempty"`             //累计奖品
	Fans             int64   `protobuf:"varint,18,opt,name=fans,proto3" json:"fans,omitempty"`                         //粉丝数量
	AllLottery       int64   `protobuf:"varint,19,opt,name=allLottery,proto3" json:"allLottery,omitempty"`             //全部抽奖包含我发起的、我中奖的
	InitiationRecord int64   `protobuf:"varint,20,opt,name=initiationRecord,proto3" json:"initiationRecord,omitempty"` //发起抽奖记录
	WinningRecord    int64   `protobuf:"varint,21,opt,name=winningRecord,proto3" json:"winningRecord,omitempty"`       //中奖记录
}

func (x *SearchUserReq) Reset() {
	*x = SearchUserReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchUserReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUserReq) ProtoMessage() {}

func (x *SearchUserReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUserReq.ProtoReflect.Descriptor instead.
func (*SearchUserReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{9}
}

func (x *SearchUserReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchUserReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *SearchUserReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SearchUserReq) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *SearchUserReq) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *SearchUserReq) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *SearchUserReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *SearchUserReq) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *SearchUserReq) GetSex() int64 {
	if x != nil {
		return x.Sex
	}
	return 0
}

func (x *SearchUserReq) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *SearchUserReq) GetInfo() string {
	if x != nil {
		return x.Info
	}
	return ""
}

func (x *SearchUserReq) GetIsAdmin() int64 {
	if x != nil {
		return x.IsAdmin
	}
	return 0
}

func (x *SearchUserReq) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

func (x *SearchUserReq) GetLocationName() string {
	if x != nil {
		return x.LocationName
	}
	return ""
}

func (x *SearchUserReq) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *SearchUserReq) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *SearchUserReq) GetTotalPrize() int64 {
	if x != nil {
		return x.TotalPrize
	}
	return 0
}

func (x *SearchUserReq) GetFans() int64 {
	if x != nil {
		return x.Fans
	}
	return 0
}

func (x *SearchUserReq) GetAllLottery() int64 {
	if x != nil {
		return x.AllLottery
	}
	return 0
}

func (x *SearchUserReq) GetInitiationRecord() int64 {
	if x != nil {
		return x.InitiationRecord
	}
	return 0
}

func (x *SearchUserReq) GetWinningRecord() int64 {
	if x != nil {
		return x.WinningRecord
	}
	return 0
}

type SearchUserResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	User []*User `protobuf:"bytes,1,rep,name=user,proto3" json:"user,omitempty"` //user
}

func (x *SearchUserResp) Reset() {
	*x = SearchUserResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchUserResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUserResp) ProtoMessage() {}

func (x *SearchUserResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUserResp.ProtoReflect.Descriptor instead.
func (*SearchUserResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{10}
}

func (x *SearchUserResp) GetUser() []*User {
	if x != nil {
		return x.User
	}
	return nil
}

// --------------------------------用户收货地址表--------------------------------
type UserAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                      //id
	UserId        int64  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`              //用户id
	ContactName   string `protobuf:"bytes,3,opt,name=contactName,proto3" json:"contactName,omitempty"`     //联系人姓名
	ContactMobile string `protobuf:"bytes,4,opt,name=contactMobile,proto3" json:"contactMobile,omitempty"` //联系人手机号码
	District      string `protobuf:"bytes,5,opt,name=district,proto3" json:"district,omitempty"`           //地区信息
	Detail        string `protobuf:"bytes,6,opt,name=detail,proto3" json:"detail,omitempty"`               //详细地址
	Postcode      string `protobuf:"bytes,7,opt,name=postcode,proto3" json:"postcode,omitempty"`           //邮政编码
	IsDefault     int64  `protobuf:"varint,8,opt,name=isDefault,proto3" json:"isDefault,omitempty"`        //是否为默认地址 1是 0否
	CreateTime    int64  `protobuf:"varint,9,opt,name=createTime,proto3" json:"createTime,omitempty"`      //createTime
	UpdateTime    int64  `protobuf:"varint,10,opt,name=updateTime,proto3" json:"updateTime,omitempty"`     //updateTime
}

func (x *UserAddress) Reset() {
	*x = UserAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAddress) ProtoMessage() {}

func (x *UserAddress) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAddress.ProtoReflect.Descriptor instead.
func (*UserAddress) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{11}
}

func (x *UserAddress) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserAddress) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserAddress) GetContactName() string {
	if x != nil {
		return x.ContactName
	}
	return ""
}

func (x *UserAddress) GetContactMobile() string {
	if x != nil {
		return x.ContactMobile
	}
	return ""
}

func (x *UserAddress) GetDistrict() string {
	if x != nil {
		return x.District
	}
	return ""
}

func (x *UserAddress) GetDetail() string {
	if x != nil {
		return x.Detail
	}
	return ""
}

func (x *UserAddress) GetPostcode() string {
	if x != nil {
		return x.Postcode
	}
	return ""
}

func (x *UserAddress) GetIsDefault() int64 {
	if x != nil {
		return x.IsDefault
	}
	return 0
}

func (x *UserAddress) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *UserAddress) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

type AddUserAddressReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId        int64  `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`              //用户id
	ContactName   string `protobuf:"bytes,2,opt,name=contactName,proto3" json:"contactName,omitempty"`     //联系人姓名
	ContactMobile string `protobuf:"bytes,3,opt,name=contactMobile,proto3" json:"contactMobile,omitempty"` //联系人手机号码
	District      string `protobuf:"bytes,4,opt,name=district,proto3" json:"district,omitempty"`           //地区信息
	Detail        string `protobuf:"bytes,5,opt,name=detail,proto3" json:"detail,omitempty"`               //详细地址
	Postcode      string `protobuf:"bytes,6,opt,name=postcode,proto3" json:"postcode,omitempty"`           //邮政编码
	IsDefault     int64  `protobuf:"varint,7,opt,name=isDefault,proto3" json:"isDefault,omitempty"`        //是否为默认地址 1是 0否
}

func (x *AddUserAddressReq) Reset() {
	*x = AddUserAddressReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddUserAddressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserAddressReq) ProtoMessage() {}

func (x *AddUserAddressReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserAddressReq.ProtoReflect.Descriptor instead.
func (*AddUserAddressReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{12}
}

func (x *AddUserAddressReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AddUserAddressReq) GetContactName() string {
	if x != nil {
		return x.ContactName
	}
	return ""
}

func (x *AddUserAddressReq) GetContactMobile() string {
	if x != nil {
		return x.ContactMobile
	}
	return ""
}

func (x *AddUserAddressReq) GetDistrict() string {
	if x != nil {
		return x.District
	}
	return ""
}

func (x *AddUserAddressReq) GetDetail() string {
	if x != nil {
		return x.Detail
	}
	return ""
}

func (x *AddUserAddressReq) GetPostcode() string {
	if x != nil {
		return x.Postcode
	}
	return ""
}

func (x *AddUserAddressReq) GetIsDefault() int64 {
	if x != nil {
		return x.IsDefault
	}
	return 0
}

type AddUserAddressResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`
}

func (x *AddUserAddressResp) Reset() {
	*x = AddUserAddressResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddUserAddressResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserAddressResp) ProtoMessage() {}

func (x *AddUserAddressResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserAddressResp.ProtoReflect.Descriptor instead.
func (*AddUserAddressResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{13}
}

func (x *AddUserAddressResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdateUserAddressReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                      //id
	UserId        int64  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`              //用户id
	ContactName   string `protobuf:"bytes,3,opt,name=contactName,proto3" json:"contactName,omitempty"`     //联系人姓名
	ContactMobile string `protobuf:"bytes,4,opt,name=contactMobile,proto3" json:"contactMobile,omitempty"` //联系人手机号码
	District      string `protobuf:"bytes,5,opt,name=district,proto3" json:"district,omitempty"`           //地区信息
	Detail        string `protobuf:"bytes,6,opt,name=detail,proto3" json:"detail,omitempty"`               //详细地址
	Postcode      string `protobuf:"bytes,7,opt,name=postcode,proto3" json:"postcode,omitempty"`           //邮政编码
	IsDefault     int64  `protobuf:"varint,8,opt,name=isDefault,proto3" json:"isDefault,omitempty"`        //是否为默认地址 1是 0否
}

func (x *UpdateUserAddressReq) Reset() {
	*x = UpdateUserAddressReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserAddressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserAddressReq) ProtoMessage() {}

func (x *UpdateUserAddressReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserAddressReq.ProtoReflect.Descriptor instead.
func (*UpdateUserAddressReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateUserAddressReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateUserAddressReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UpdateUserAddressReq) GetContactName() string {
	if x != nil {
		return x.ContactName
	}
	return ""
}

func (x *UpdateUserAddressReq) GetContactMobile() string {
	if x != nil {
		return x.ContactMobile
	}
	return ""
}

func (x *UpdateUserAddressReq) GetDistrict() string {
	if x != nil {
		return x.District
	}
	return ""
}

func (x *UpdateUserAddressReq) GetDetail() string {
	if x != nil {
		return x.Detail
	}
	return ""
}

func (x *UpdateUserAddressReq) GetPostcode() string {
	if x != nil {
		return x.Postcode
	}
	return ""
}

func (x *UpdateUserAddressReq) GetIsDefault() int64 {
	if x != nil {
		return x.IsDefault
	}
	return 0
}

type UpdateUserAddressResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateUserAddressResp) Reset() {
	*x = UpdateUserAddressResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserAddressResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserAddressResp) ProtoMessage() {}

func (x *UpdateUserAddressResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserAddressResp.ProtoReflect.Descriptor instead.
func (*UpdateUserAddressResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{15}
}

type DelUserAddressReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *DelUserAddressReq) Reset() {
	*x = DelUserAddressReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelUserAddressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelUserAddressReq) ProtoMessage() {}

func (x *DelUserAddressReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelUserAddressReq.ProtoReflect.Descriptor instead.
func (*DelUserAddressReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{16}
}

func (x *DelUserAddressReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DelUserAddressResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DelUserAddressResp) Reset() {
	*x = DelUserAddressResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelUserAddressResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelUserAddressResp) ProtoMessage() {}

func (x *DelUserAddressResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelUserAddressResp.ProtoReflect.Descriptor instead.
func (*DelUserAddressResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{17}
}

type GetUserAddressByIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *GetUserAddressByIdReq) Reset() {
	*x = GetUserAddressByIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserAddressByIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAddressByIdReq) ProtoMessage() {}

func (x *GetUserAddressByIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAddressByIdReq.ProtoReflect.Descriptor instead.
func (*GetUserAddressByIdReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{18}
}

func (x *GetUserAddressByIdReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetUserAddressByIdResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserAddress *UserAddress `protobuf:"bytes,1,opt,name=userAddress,proto3" json:"userAddress,omitempty"` //userAddress
}

func (x *GetUserAddressByIdResp) Reset() {
	*x = GetUserAddressByIdResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserAddressByIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAddressByIdResp) ProtoMessage() {}

func (x *GetUserAddressByIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAddressByIdResp.ProtoReflect.Descriptor instead.
func (*GetUserAddressByIdResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{19}
}

func (x *GetUserAddressByIdResp) GetUserAddress() *UserAddress {
	if x != nil {
		return x.UserAddress
	}
	return nil
}

type SearchUserAddressReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page          int64  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`                  //page
	Limit         int64  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`                //limit
	Id            int64  `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`                      //id
	UserId        int64  `protobuf:"varint,4,opt,name=userId,proto3" json:"userId,omitempty"`              //用户id
	ContactName   string `protobuf:"bytes,5,opt,name=contactName,proto3" json:"contactName,omitempty"`     //联系人姓名
	ContactMobile string `protobuf:"bytes,6,opt,name=contactMobile,proto3" json:"contactMobile,omitempty"` //联系人手机号码
	District      string `protobuf:"bytes,7,opt,name=district,proto3" json:"district,omitempty"`           //地区信息
	Detail        string `protobuf:"bytes,8,opt,name=detail,proto3" json:"detail,omitempty"`               //详细地址
	Postcode      string `protobuf:"bytes,9,opt,name=postcode,proto3" json:"postcode,omitempty"`           //邮政编码
	IsDefault     int64  `protobuf:"varint,10,opt,name=isDefault,proto3" json:"isDefault,omitempty"`       //是否为默认地址 1是 0否
	CreateTime    int64  `protobuf:"varint,11,opt,name=createTime,proto3" json:"createTime,omitempty"`     //createTime
	UpdateTime    int64  `protobuf:"varint,12,opt,name=updateTime,proto3" json:"updateTime,omitempty"`     //updateTime
}

func (x *SearchUserAddressReq) Reset() {
	*x = SearchUserAddressReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchUserAddressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUserAddressReq) ProtoMessage() {}

func (x *SearchUserAddressReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUserAddressReq.ProtoReflect.Descriptor instead.
func (*SearchUserAddressReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{20}
}

func (x *SearchUserAddressReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchUserAddressReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *SearchUserAddressReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SearchUserAddressReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *SearchUserAddressReq) GetContactName() string {
	if x != nil {
		return x.ContactName
	}
	return ""
}

func (x *SearchUserAddressReq) GetContactMobile() string {
	if x != nil {
		return x.ContactMobile
	}
	return ""
}

func (x *SearchUserAddressReq) GetDistrict() string {
	if x != nil {
		return x.District
	}
	return ""
}

func (x *SearchUserAddressReq) GetDetail() string {
	if x != nil {
		return x.Detail
	}
	return ""
}

func (x *SearchUserAddressReq) GetPostcode() string {
	if x != nil {
		return x.Postcode
	}
	return ""
}

func (x *SearchUserAddressReq) GetIsDefault() int64 {
	if x != nil {
		return x.IsDefault
	}
	return 0
}

func (x *SearchUserAddressReq) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *SearchUserAddressReq) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

type SearchUserAddressResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserAddress []*UserAddress `protobuf:"bytes,1,rep,name=userAddress,proto3" json:"userAddress,omitempty"` //userAddress
}

func (x *SearchUserAddressResp) Reset() {
	*x = SearchUserAddressResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchUserAddressResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUserAddressResp) ProtoMessage() {}

func (x *SearchUserAddressResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUserAddressResp.ProtoReflect.Descriptor instead.
func (*SearchUserAddressResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{21}
}

func (x *SearchUserAddressResp) GetUserAddress() []*UserAddress {
	if x != nil {
		return x.UserAddress
	}
	return nil
}

// --------------------------------用户授权表--------------------------------
type UserAuth struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                 //id
	CreateTime int64  `protobuf:"varint,2,opt,name=createTime,proto3" json:"createTime,omitempty"` //createTime
	UpdateTime int64  `protobuf:"varint,3,opt,name=updateTime,proto3" json:"updateTime,omitempty"` //updateTime
	UserId     int64  `protobuf:"varint,4,opt,name=userId,proto3" json:"userId,omitempty"`         //userId
	AuthKey    string `protobuf:"bytes,5,opt,name=authKey,proto3" json:"authKey,omitempty"`        //平台唯一id
	AuthType   string `protobuf:"bytes,6,opt,name=authType,proto3" json:"authType,omitempty"`      //平台类型
}

func (x *UserAuth) Reset() {
	*x = UserAuth{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserAuth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAuth) ProtoMessage() {}

func (x *UserAuth) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAuth.ProtoReflect.Descriptor instead.
func (*UserAuth) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{22}
}

func (x *UserAuth) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserAuth) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *UserAuth) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *UserAuth) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserAuth) GetAuthKey() string {
	if x != nil {
		return x.AuthKey
	}
	return ""
}

func (x *UserAuth) GetAuthType() string {
	if x != nil {
		return x.AuthType
	}
	return ""
}

type AddUserAuthReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId   int64  `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`    //userId
	AuthKey  string `protobuf:"bytes,2,opt,name=authKey,proto3" json:"authKey,omitempty"`   //平台唯一id
	AuthType string `protobuf:"bytes,3,opt,name=authType,proto3" json:"authType,omitempty"` //平台类型
}

func (x *AddUserAuthReq) Reset() {
	*x = AddUserAuthReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddUserAuthReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserAuthReq) ProtoMessage() {}

func (x *AddUserAuthReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserAuthReq.ProtoReflect.Descriptor instead.
func (*AddUserAuthReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{23}
}

func (x *AddUserAuthReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AddUserAuthReq) GetAuthKey() string {
	if x != nil {
		return x.AuthKey
	}
	return ""
}

func (x *AddUserAuthReq) GetAuthType() string {
	if x != nil {
		return x.AuthType
	}
	return ""
}

type AddUserAuthResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddUserAuthResp) Reset() {
	*x = AddUserAuthResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddUserAuthResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserAuthResp) ProtoMessage() {}

func (x *AddUserAuthResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserAuthResp.ProtoReflect.Descriptor instead.
func (*AddUserAuthResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{24}
}

type UpdateUserAuthReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`            //id
	UserId   int64  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`    //userId
	AuthKey  string `protobuf:"bytes,3,opt,name=authKey,proto3" json:"authKey,omitempty"`   //平台唯一id
	AuthType string `protobuf:"bytes,4,opt,name=authType,proto3" json:"authType,omitempty"` //平台类型
}

func (x *UpdateUserAuthReq) Reset() {
	*x = UpdateUserAuthReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserAuthReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserAuthReq) ProtoMessage() {}

func (x *UpdateUserAuthReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserAuthReq.ProtoReflect.Descriptor instead.
func (*UpdateUserAuthReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{25}
}

func (x *UpdateUserAuthReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateUserAuthReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UpdateUserAuthReq) GetAuthKey() string {
	if x != nil {
		return x.AuthKey
	}
	return ""
}

func (x *UpdateUserAuthReq) GetAuthType() string {
	if x != nil {
		return x.AuthType
	}
	return ""
}

type UpdateUserAuthResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateUserAuthResp) Reset() {
	*x = UpdateUserAuthResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserAuthResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserAuthResp) ProtoMessage() {}

func (x *UpdateUserAuthResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserAuthResp.ProtoReflect.Descriptor instead.
func (*UpdateUserAuthResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{26}
}

type DelUserAuthReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *DelUserAuthReq) Reset() {
	*x = DelUserAuthReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelUserAuthReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelUserAuthReq) ProtoMessage() {}

func (x *DelUserAuthReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelUserAuthReq.ProtoReflect.Descriptor instead.
func (*DelUserAuthReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{27}
}

func (x *DelUserAuthReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DelUserAuthResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DelUserAuthResp) Reset() {
	*x = DelUserAuthResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelUserAuthResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelUserAuthResp) ProtoMessage() {}

func (x *DelUserAuthResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelUserAuthResp.ProtoReflect.Descriptor instead.
func (*DelUserAuthResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{28}
}

type GetUserAuthByIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *GetUserAuthByIdReq) Reset() {
	*x = GetUserAuthByIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserAuthByIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAuthByIdReq) ProtoMessage() {}

func (x *GetUserAuthByIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAuthByIdReq.ProtoReflect.Descriptor instead.
func (*GetUserAuthByIdReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{29}
}

func (x *GetUserAuthByIdReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetUserAuthByIdResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserAuth *UserAuth `protobuf:"bytes,1,opt,name=userAuth,proto3" json:"userAuth,omitempty"` //userAuth
}

func (x *GetUserAuthByIdResp) Reset() {
	*x = GetUserAuthByIdResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserAuthByIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAuthByIdResp) ProtoMessage() {}

func (x *GetUserAuthByIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAuthByIdResp.ProtoReflect.Descriptor instead.
func (*GetUserAuthByIdResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{30}
}

func (x *GetUserAuthByIdResp) GetUserAuth() *UserAuth {
	if x != nil {
		return x.UserAuth
	}
	return nil
}

type SearchUserAuthReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page       int64  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`             //page
	Limit      int64  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`           //limit
	Id         int64  `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`                 //id
	CreateTime int64  `protobuf:"varint,4,opt,name=createTime,proto3" json:"createTime,omitempty"` //createTime
	UpdateTime int64  `protobuf:"varint,5,opt,name=updateTime,proto3" json:"updateTime,omitempty"` //updateTime
	UserId     int64  `protobuf:"varint,6,opt,name=userId,proto3" json:"userId,omitempty"`         //userId
	AuthKey    string `protobuf:"bytes,7,opt,name=authKey,proto3" json:"authKey,omitempty"`        //平台唯一id
	AuthType   string `protobuf:"bytes,8,opt,name=authType,proto3" json:"authType,omitempty"`      //平台类型
}

func (x *SearchUserAuthReq) Reset() {
	*x = SearchUserAuthReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchUserAuthReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUserAuthReq) ProtoMessage() {}

func (x *SearchUserAuthReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUserAuthReq.ProtoReflect.Descriptor instead.
func (*SearchUserAuthReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{31}
}

func (x *SearchUserAuthReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchUserAuthReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *SearchUserAuthReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SearchUserAuthReq) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *SearchUserAuthReq) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *SearchUserAuthReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *SearchUserAuthReq) GetAuthKey() string {
	if x != nil {
		return x.AuthKey
	}
	return ""
}

func (x *SearchUserAuthReq) GetAuthType() string {
	if x != nil {
		return x.AuthType
	}
	return ""
}

type SearchUserAuthResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserAuth []*UserAuth `protobuf:"bytes,1,rep,name=userAuth,proto3" json:"userAuth,omitempty"` //userAuth
}

func (x *SearchUserAuthResp) Reset() {
	*x = SearchUserAuthResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchUserAuthResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUserAuthResp) ProtoMessage() {}

func (x *SearchUserAuthResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUserAuthResp.ProtoReflect.Descriptor instead.
func (*SearchUserAuthResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{32}
}

func (x *SearchUserAuthResp) GetUserAuth() []*UserAuth {
	if x != nil {
		return x.UserAuth
	}
	return nil
}

type UserInfoForComment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`            //userId
	Nickname string `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"` //userName
	Avatar   string `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`     //avatar
}

func (x *UserInfoForComment) Reset() {
	*x = UserInfoForComment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfoForComment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoForComment) ProtoMessage() {}

func (x *UserInfoForComment) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoForComment.ProtoReflect.Descriptor instead.
func (*UserInfoForComment) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{33}
}

func (x *UserInfoForComment) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserInfoForComment) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *UserInfoForComment) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

type GetUserInfoByUserIdsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserIds []int64 `protobuf:"varint,1,rep,packed,name=userIds,proto3" json:"userIds,omitempty"` //userIds
}

func (x *GetUserInfoByUserIdsReq) Reset() {
	*x = GetUserInfoByUserIdsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserInfoByUserIdsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoByUserIdsReq) ProtoMessage() {}

func (x *GetUserInfoByUserIdsReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoByUserIdsReq.ProtoReflect.Descriptor instead.
func (*GetUserInfoByUserIdsReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{34}
}

func (x *GetUserInfoByUserIdsReq) GetUserIds() []int64 {
	if x != nil {
		return x.UserIds
	}
	return nil
}

type GetUserInfoByUserIdsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserInfo []*UserInfoForComment `protobuf:"bytes,1,rep,name=userInfo,proto3" json:"userInfo,omitempty"`
}

func (x *GetUserInfoByUserIdsResp) Reset() {
	*x = GetUserInfoByUserIdsResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserInfoByUserIdsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoByUserIdsResp) ProtoMessage() {}

func (x *GetUserInfoByUserIdsResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoByUserIdsResp.ProtoReflect.Descriptor instead.
func (*GetUserInfoByUserIdsResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{35}
}

func (x *GetUserInfoByUserIdsResp) GetUserInfo() []*UserInfoForComment {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

// --------------------------------抽奖发起人联系方式--------------------------------
type UserContact struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                 //id
	UserId     int64  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`         //userId
	Content    string `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`        //content
	Remark     string `protobuf:"bytes,4,opt,name=remark,proto3" json:"remark,omitempty"`          //remark
	CreateTime int64  `protobuf:"varint,5,opt,name=createTime,proto3" json:"createTime,omitempty"` //createTime
	UpdateTime int64  `protobuf:"varint,6,opt,name=updateTime,proto3" json:"updateTime,omitempty"` //updateTime
}

func (x *UserContact) Reset() {
	*x = UserContact{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserContact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserContact) ProtoMessage() {}

func (x *UserContact) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserContact.ProtoReflect.Descriptor instead.
func (*UserContact) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{36}
}

func (x *UserContact) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserContact) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserContact) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *UserContact) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *UserContact) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *UserContact) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

type AddUserContactReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId  int64  `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`  //userId
	Content string `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"` //content
	Remark  string `protobuf:"bytes,4,opt,name=remark,proto3" json:"remark,omitempty"`   //remark
}

func (x *AddUserContactReq) Reset() {
	*x = AddUserContactReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddUserContactReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserContactReq) ProtoMessage() {}

func (x *AddUserContactReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserContactReq.ProtoReflect.Descriptor instead.
func (*AddUserContactReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{37}
}

func (x *AddUserContactReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AddUserContactReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *AddUserContactReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

type AddUserContactResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *AddUserContactResp) Reset() {
	*x = AddUserContactResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddUserContactResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserContactResp) ProtoMessage() {}

func (x *AddUserContactResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserContactResp.ProtoReflect.Descriptor instead.
func (*AddUserContactResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{38}
}

func (x *AddUserContactResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type EditUserContactReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      int64  `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`          //Id
	Content string `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"` //content
	Remark  string `protobuf:"bytes,4,opt,name=remark,proto3" json:"remark,omitempty"`   //remark
}

func (x *EditUserContactReq) Reset() {
	*x = EditUserContactReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditUserContactReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditUserContactReq) ProtoMessage() {}

func (x *EditUserContactReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditUserContactReq.ProtoReflect.Descriptor instead.
func (*EditUserContactReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{39}
}

func (x *EditUserContactReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EditUserContactReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *EditUserContactReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

type EditUserContactResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *EditUserContactResp) Reset() {
	*x = EditUserContactResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditUserContactResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditUserContactResp) ProtoMessage() {}

func (x *EditUserContactResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditUserContactResp.ProtoReflect.Descriptor instead.
func (*EditUserContactResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{40}
}

func (x *EditUserContactResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdateUserContactReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`          //id
	Content string `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"` //content
	Remark  string `protobuf:"bytes,4,opt,name=remark,proto3" json:"remark,omitempty"`   //remark
}

func (x *UpdateUserContactReq) Reset() {
	*x = UpdateUserContactReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserContactReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserContactReq) ProtoMessage() {}

func (x *UpdateUserContactReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserContactReq.ProtoReflect.Descriptor instead.
func (*UpdateUserContactReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{41}
}

func (x *UpdateUserContactReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateUserContactReq) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *UpdateUserContactReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

type UpdateUserContactResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *UpdateUserContactResp) Reset() {
	*x = UpdateUserContactResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserContactResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserContactResp) ProtoMessage() {}

func (x *UpdateUserContactResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserContactResp.ProtoReflect.Descriptor instead.
func (*UpdateUserContactResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{42}
}

func (x *UpdateUserContactResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DelUserContactReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id []int64 `protobuf:"varint,1,rep,packed,name=id,proto3" json:"id,omitempty"` //id
}

func (x *DelUserContactReq) Reset() {
	*x = DelUserContactReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelUserContactReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelUserContactReq) ProtoMessage() {}

func (x *DelUserContactReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelUserContactReq.ProtoReflect.Descriptor instead.
func (*DelUserContactReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{43}
}

func (x *DelUserContactReq) GetId() []int64 {
	if x != nil {
		return x.Id
	}
	return nil
}

type DelUserContactResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DelUserContactResp) Reset() {
	*x = DelUserContactResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelUserContactResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelUserContactResp) ProtoMessage() {}

func (x *DelUserContactResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelUserContactResp.ProtoReflect.Descriptor instead.
func (*DelUserContactResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{44}
}

type GetUserContactByIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *GetUserContactByIdReq) Reset() {
	*x = GetUserContactByIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserContactByIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserContactByIdReq) ProtoMessage() {}

func (x *GetUserContactByIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserContactByIdReq.ProtoReflect.Descriptor instead.
func (*GetUserContactByIdReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{45}
}

func (x *GetUserContactByIdReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetUserContactByIdResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContact *UserContact `protobuf:"bytes,1,opt,name=userContact,proto3" json:"userContact,omitempty"` //userContact
}

func (x *GetUserContactByIdResp) Reset() {
	*x = GetUserContactByIdResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserContactByIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserContactByIdResp) ProtoMessage() {}

func (x *GetUserContactByIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserContactByIdResp.ProtoReflect.Descriptor instead.
func (*GetUserContactByIdResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{46}
}

func (x *GetUserContactByIdResp) GetUserContact() *UserContact {
	if x != nil {
		return x.UserContact
	}
	return nil
}

type SearchUserContactReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page   int64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`     //page
	Limit  int64 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`   //limit
	UserId int64 `protobuf:"varint,4,opt,name=userId,proto3" json:"userId,omitempty"` //userId
}

func (x *SearchUserContactReq) Reset() {
	*x = SearchUserContactReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchUserContactReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUserContactReq) ProtoMessage() {}

func (x *SearchUserContactReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUserContactReq.ProtoReflect.Descriptor instead.
func (*SearchUserContactReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{47}
}

func (x *SearchUserContactReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchUserContactReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *SearchUserContactReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type SearchUserContactResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserContact []*UserContact `protobuf:"bytes,1,rep,name=userContact,proto3" json:"userContact,omitempty"` //userContact
}

func (x *SearchUserContactResp) Reset() {
	*x = SearchUserContactResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchUserContactResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUserContactResp) ProtoMessage() {}

func (x *SearchUserContactResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUserContactResp.ProtoReflect.Descriptor instead.
func (*SearchUserContactResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{48}
}

func (x *SearchUserContactResp) GetUserContact() []*UserContact {
	if x != nil {
		return x.UserContact
	}
	return nil
}

// --------------------------------userShop--------------------------------
type UserShop struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                 //id
	UserId     int64   `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`         //userId
	Name       string  `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`              //name
	Location   float64 `protobuf:"fixed64,4,opt,name=location,proto3" json:"location,omitempty"`    //location
	CreateTime int64   `protobuf:"varint,5,opt,name=createTime,proto3" json:"createTime,omitempty"` //createTime
	UpdateTime int64   `protobuf:"varint,6,opt,name=updateTime,proto3" json:"updateTime,omitempty"` //updateTime
}

func (x *UserShop) Reset() {
	*x = UserShop{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserShop) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserShop) ProtoMessage() {}

func (x *UserShop) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserShop.ProtoReflect.Descriptor instead.
func (*UserShop) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{49}
}

func (x *UserShop) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserShop) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserShop) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserShop) GetLocation() float64 {
	if x != nil {
		return x.Location
	}
	return 0
}

func (x *UserShop) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *UserShop) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

type AddUserShopReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId   int64   `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`      //userId
	Name     string  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`           //name
	Location float64 `protobuf:"fixed64,3,opt,name=location,proto3" json:"location,omitempty"` //location
}

func (x *AddUserShopReq) Reset() {
	*x = AddUserShopReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddUserShopReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserShopReq) ProtoMessage() {}

func (x *AddUserShopReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserShopReq.ProtoReflect.Descriptor instead.
func (*AddUserShopReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{50}
}

func (x *AddUserShopReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AddUserShopReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AddUserShopReq) GetLocation() float64 {
	if x != nil {
		return x.Location
	}
	return 0
}

type AddUserShopResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddUserShopResp) Reset() {
	*x = AddUserShopResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddUserShopResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserShopResp) ProtoMessage() {}

func (x *AddUserShopResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserShopResp.ProtoReflect.Descriptor instead.
func (*AddUserShopResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{51}
}

type UpdateUserShopReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`              //id
	UserId   int64   `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`      //userId
	Name     string  `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`           //name
	Location float64 `protobuf:"fixed64,4,opt,name=location,proto3" json:"location,omitempty"` //location
}

func (x *UpdateUserShopReq) Reset() {
	*x = UpdateUserShopReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserShopReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserShopReq) ProtoMessage() {}

func (x *UpdateUserShopReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserShopReq.ProtoReflect.Descriptor instead.
func (*UpdateUserShopReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{52}
}

func (x *UpdateUserShopReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateUserShopReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UpdateUserShopReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateUserShopReq) GetLocation() float64 {
	if x != nil {
		return x.Location
	}
	return 0
}

type UpdateUserShopResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateUserShopResp) Reset() {
	*x = UpdateUserShopResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserShopResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserShopResp) ProtoMessage() {}

func (x *UpdateUserShopResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserShopResp.ProtoReflect.Descriptor instead.
func (*UpdateUserShopResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{53}
}

type DelUserShopReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *DelUserShopReq) Reset() {
	*x = DelUserShopReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelUserShopReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelUserShopReq) ProtoMessage() {}

func (x *DelUserShopReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelUserShopReq.ProtoReflect.Descriptor instead.
func (*DelUserShopReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{54}
}

func (x *DelUserShopReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DelUserShopResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DelUserShopResp) Reset() {
	*x = DelUserShopResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelUserShopResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelUserShopResp) ProtoMessage() {}

func (x *DelUserShopResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelUserShopResp.ProtoReflect.Descriptor instead.
func (*DelUserShopResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{55}
}

type GetUserShopByIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *GetUserShopByIdReq) Reset() {
	*x = GetUserShopByIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserShopByIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserShopByIdReq) ProtoMessage() {}

func (x *GetUserShopByIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserShopByIdReq.ProtoReflect.Descriptor instead.
func (*GetUserShopByIdReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{56}
}

func (x *GetUserShopByIdReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetUserShopByIdResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserShop *UserShop `protobuf:"bytes,1,opt,name=userShop,proto3" json:"userShop,omitempty"` //userShop
}

func (x *GetUserShopByIdResp) Reset() {
	*x = GetUserShopByIdResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserShopByIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserShopByIdResp) ProtoMessage() {}

func (x *GetUserShopByIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserShopByIdResp.ProtoReflect.Descriptor instead.
func (*GetUserShopByIdResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{57}
}

func (x *GetUserShopByIdResp) GetUserShop() *UserShop {
	if x != nil {
		return x.UserShop
	}
	return nil
}

type SearchUserShopReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page       int64   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`             //page
	Limit      int64   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`           //limit
	Id         int64   `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`                 //id
	UserId     int64   `protobuf:"varint,4,opt,name=userId,proto3" json:"userId,omitempty"`         //userId
	Name       string  `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`              //name
	Location   float64 `protobuf:"fixed64,6,opt,name=location,proto3" json:"location,omitempty"`    //location
	CreateTime int64   `protobuf:"varint,7,opt,name=createTime,proto3" json:"createTime,omitempty"` //createTime
	UpdateTime int64   `protobuf:"varint,8,opt,name=updateTime,proto3" json:"updateTime,omitempty"` //updateTime
}

func (x *SearchUserShopReq) Reset() {
	*x = SearchUserShopReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchUserShopReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUserShopReq) ProtoMessage() {}

func (x *SearchUserShopReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUserShopReq.ProtoReflect.Descriptor instead.
func (*SearchUserShopReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{58}
}

func (x *SearchUserShopReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchUserShopReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *SearchUserShopReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SearchUserShopReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *SearchUserShopReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchUserShopReq) GetLocation() float64 {
	if x != nil {
		return x.Location
	}
	return 0
}

func (x *SearchUserShopReq) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *SearchUserShopReq) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

type SearchUserShopResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserShop []*UserShop `protobuf:"bytes,1,rep,name=userShop,proto3" json:"userShop,omitempty"` //userShop
}

func (x *SearchUserShopResp) Reset() {
	*x = SearchUserShopResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchUserShopResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUserShopResp) ProtoMessage() {}

func (x *SearchUserShopResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUserShopResp.ProtoReflect.Descriptor instead.
func (*SearchUserShopResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{59}
}

func (x *SearchUserShopResp) GetUserShop() []*UserShop {
	if x != nil {
		return x.UserShop
	}
	return nil
}

type CheckIsAdminReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId int64 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
}

func (x *CheckIsAdminReq) Reset() {
	*x = CheckIsAdminReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckIsAdminReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckIsAdminReq) ProtoMessage() {}

func (x *CheckIsAdminReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckIsAdminReq.ProtoReflect.Descriptor instead.
func (*CheckIsAdminReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{60}
}

func (x *CheckIsAdminReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type CheckIsAdminResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsAdmin bool `protobuf:"varint,1,opt,name=isAdmin,proto3" json:"isAdmin,omitempty"`
}

func (x *CheckIsAdminResp) Reset() {
	*x = CheckIsAdminResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckIsAdminResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckIsAdminResp) ProtoMessage() {}

func (x *CheckIsAdminResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckIsAdminResp.ProtoReflect.Descriptor instead.
func (*CheckIsAdminResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{61}
}

func (x *CheckIsAdminResp) GetIsAdmin() bool {
	if x != nil {
		return x.IsAdmin
	}
	return false
}

type WXMiniRegisterReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nickname string `protobuf:"bytes,1,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Avatar   string `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`
	AuthKey  string `protobuf:"bytes,3,opt,name=authKey,proto3" json:"authKey,omitempty"`
	AuthType string `protobuf:"bytes,4,opt,name=authType,proto3" json:"authType,omitempty"`
}

func (x *WXMiniRegisterReq) Reset() {
	*x = WXMiniRegisterReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WXMiniRegisterReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WXMiniRegisterReq) ProtoMessage() {}

func (x *WXMiniRegisterReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WXMiniRegisterReq.ProtoReflect.Descriptor instead.
func (*WXMiniRegisterReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{62}
}

func (x *WXMiniRegisterReq) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *WXMiniRegisterReq) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *WXMiniRegisterReq) GetAuthKey() string {
	if x != nil {
		return x.AuthKey
	}
	return ""
}

func (x *WXMiniRegisterReq) GetAuthType() string {
	if x != nil {
		return x.AuthType
	}
	return ""
}

type WXMiniRegisterResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccessToken  string `protobuf:"bytes,1,opt,name=accessToken,proto3" json:"accessToken,omitempty"`
	AccessExpire int64  `protobuf:"varint,2,opt,name=accessExpire,proto3" json:"accessExpire,omitempty"`
	RefreshAfter int64  `protobuf:"varint,3,opt,name=refreshAfter,proto3" json:"refreshAfter,omitempty"`
}

func (x *WXMiniRegisterResp) Reset() {
	*x = WXMiniRegisterResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WXMiniRegisterResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WXMiniRegisterResp) ProtoMessage() {}

func (x *WXMiniRegisterResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WXMiniRegisterResp.ProtoReflect.Descriptor instead.
func (*WXMiniRegisterResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{63}
}

func (x *WXMiniRegisterResp) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *WXMiniRegisterResp) GetAccessExpire() int64 {
	if x != nil {
		return x.AccessExpire
	}
	return 0
}

func (x *WXMiniRegisterResp) GetRefreshAfter() int64 {
	if x != nil {
		return x.RefreshAfter
	}
	return 0
}

type LoginReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuthType string `protobuf:"bytes,1,opt,name=authType,proto3" json:"authType,omitempty"`
	AuthKey  string `protobuf:"bytes,2,opt,name=authKey,proto3" json:"authKey,omitempty"`
	Password string `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
}

func (x *LoginReq) Reset() {
	*x = LoginReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginReq) ProtoMessage() {}

func (x *LoginReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginReq.ProtoReflect.Descriptor instead.
func (*LoginReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{64}
}

func (x *LoginReq) GetAuthType() string {
	if x != nil {
		return x.AuthType
	}
	return ""
}

func (x *LoginReq) GetAuthKey() string {
	if x != nil {
		return x.AuthKey
	}
	return ""
}

func (x *LoginReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type LoginResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccessToken  string `protobuf:"bytes,1,opt,name=accessToken,proto3" json:"accessToken,omitempty"`
	AccessExpire int64  `protobuf:"varint,2,opt,name=accessExpire,proto3" json:"accessExpire,omitempty"`
	RefreshAfter int64  `protobuf:"varint,3,opt,name=refreshAfter,proto3" json:"refreshAfter,omitempty"`
}

func (x *LoginResp) Reset() {
	*x = LoginResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginResp) ProtoMessage() {}

func (x *LoginResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginResp.ProtoReflect.Descriptor instead.
func (*LoginResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{65}
}

func (x *LoginResp) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *LoginResp) GetAccessExpire() int64 {
	if x != nil {
		return x.AccessExpire
	}
	return 0
}

func (x *LoginResp) GetRefreshAfter() int64 {
	if x != nil {
		return x.RefreshAfter
	}
	return 0
}

type GetUserInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetUserInfoReq) Reset() {
	*x = GetUserInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoReq) ProtoMessage() {}

func (x *GetUserInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoReq.ProtoReflect.Descriptor instead.
func (*GetUserInfoReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{66}
}

func (x *GetUserInfoReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetUserInfoResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	User *User `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
}

func (x *GetUserInfoResp) Reset() {
	*x = GetUserInfoResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoResp) ProtoMessage() {}

func (x *GetUserInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoResp.ProtoReflect.Descriptor instead.
func (*GetUserInfoResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{67}
}

func (x *GetUserInfoResp) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

type GetUserAuthByAuthKeyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuthKey  string `protobuf:"bytes,1,opt,name=authKey,proto3" json:"authKey,omitempty"`
	AuthType string `protobuf:"bytes,2,opt,name=authType,proto3" json:"authType,omitempty"`
}

func (x *GetUserAuthByAuthKeyReq) Reset() {
	*x = GetUserAuthByAuthKeyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserAuthByAuthKeyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAuthByAuthKeyReq) ProtoMessage() {}

func (x *GetUserAuthByAuthKeyReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAuthByAuthKeyReq.ProtoReflect.Descriptor instead.
func (*GetUserAuthByAuthKeyReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{68}
}

func (x *GetUserAuthByAuthKeyReq) GetAuthKey() string {
	if x != nil {
		return x.AuthKey
	}
	return ""
}

func (x *GetUserAuthByAuthKeyReq) GetAuthType() string {
	if x != nil {
		return x.AuthType
	}
	return ""
}

type GetUserAuthByAuthKeyResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserAuth *UserAuth `protobuf:"bytes,1,opt,name=userAuth,proto3" json:"userAuth,omitempty"`
}

func (x *GetUserAuthByAuthKeyResp) Reset() {
	*x = GetUserAuthByAuthKeyResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserAuthByAuthKeyResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAuthByAuthKeyResp) ProtoMessage() {}

func (x *GetUserAuthByAuthKeyResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAuthByAuthKeyResp.ProtoReflect.Descriptor instead.
func (*GetUserAuthByAuthKeyResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{69}
}

func (x *GetUserAuthByAuthKeyResp) GetUserAuth() *UserAuth {
	if x != nil {
		return x.UserAuth
	}
	return nil
}

type GetUserAuthByUserIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId   int64  `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	AuthType string `protobuf:"bytes,2,opt,name=authType,proto3" json:"authType,omitempty"`
}

func (x *GetUserAuthByUserIdReq) Reset() {
	*x = GetUserAuthByUserIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserAuthByUserIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAuthByUserIdReq) ProtoMessage() {}

func (x *GetUserAuthByUserIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAuthByUserIdReq.ProtoReflect.Descriptor instead.
func (*GetUserAuthByUserIdReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{70}
}

func (x *GetUserAuthByUserIdReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetUserAuthByUserIdReq) GetAuthType() string {
	if x != nil {
		return x.AuthType
	}
	return ""
}

type GetUserAuthyUserIdResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserAuth *UserAuth `protobuf:"bytes,1,opt,name=userAuth,proto3" json:"userAuth,omitempty"`
}

func (x *GetUserAuthyUserIdResp) Reset() {
	*x = GetUserAuthyUserIdResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserAuthyUserIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAuthyUserIdResp) ProtoMessage() {}

func (x *GetUserAuthyUserIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAuthyUserIdResp.ProtoReflect.Descriptor instead.
func (*GetUserAuthyUserIdResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{71}
}

func (x *GetUserAuthyUserIdResp) GetUserAuth() *UserAuth {
	if x != nil {
		return x.UserAuth
	}
	return nil
}

type GenerateTokenReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId int64 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
}

func (x *GenerateTokenReq) Reset() {
	*x = GenerateTokenReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateTokenReq) ProtoMessage() {}

func (x *GenerateTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateTokenReq.ProtoReflect.Descriptor instead.
func (*GenerateTokenReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{72}
}

func (x *GenerateTokenReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GenerateTokenResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccessToken  string `protobuf:"bytes,1,opt,name=accessToken,proto3" json:"accessToken,omitempty"`
	AccessExpire int64  `protobuf:"varint,2,opt,name=accessExpire,proto3" json:"accessExpire,omitempty"`
	RefreshAfter int64  `protobuf:"varint,3,opt,name=refreshAfter,proto3" json:"refreshAfter,omitempty"`
}

func (x *GenerateTokenResp) Reset() {
	*x = GenerateTokenResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateTokenResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateTokenResp) ProtoMessage() {}

func (x *GenerateTokenResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateTokenResp.ProtoReflect.Descriptor instead.
func (*GenerateTokenResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{73}
}

func (x *GenerateTokenResp) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *GenerateTokenResp) GetAccessExpire() int64 {
	if x != nil {
		return x.AccessExpire
	}
	return 0
}

func (x *GenerateTokenResp) GetRefreshAfter() int64 {
	if x != nil {
		return x.RefreshAfter
	}
	return 0
}

// req 、resp
type RegisterReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mobile   string `protobuf:"bytes,1,opt,name=mobile,proto3" json:"mobile,omitempty"`
	Nickname string `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Password string `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	AuthKey  string `protobuf:"bytes,4,opt,name=authKey,proto3" json:"authKey,omitempty"`
	AuthType string `protobuf:"bytes,5,opt,name=authType,proto3" json:"authType,omitempty"`
}

func (x *RegisterReq) Reset() {
	*x = RegisterReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegisterReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterReq) ProtoMessage() {}

func (x *RegisterReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterReq.ProtoReflect.Descriptor instead.
func (*RegisterReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{74}
}

func (x *RegisterReq) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *RegisterReq) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *RegisterReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *RegisterReq) GetAuthKey() string {
	if x != nil {
		return x.AuthKey
	}
	return ""
}

func (x *RegisterReq) GetAuthType() string {
	if x != nil {
		return x.AuthType
	}
	return ""
}

type RegisterResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccessToken  string `protobuf:"bytes,1,opt,name=accessToken,proto3" json:"accessToken,omitempty"`
	AccessExpire int64  `protobuf:"varint,2,opt,name=accessExpire,proto3" json:"accessExpire,omitempty"`
	RefreshAfter int64  `protobuf:"varint,3,opt,name=refreshAfter,proto3" json:"refreshAfter,omitempty"`
}

func (x *RegisterResp) Reset() {
	*x = RegisterResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegisterResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterResp) ProtoMessage() {}

func (x *RegisterResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterResp.ProtoReflect.Descriptor instead.
func (*RegisterResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{75}
}

func (x *RegisterResp) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *RegisterResp) GetAccessExpire() int64 {
	if x != nil {
		return x.AccessExpire
	}
	return 0
}

func (x *RegisterResp) GetRefreshAfter() int64 {
	if x != nil {
		return x.RefreshAfter
	}
	return 0
}

type UpdateUserBaseInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Nickname  string  `protobuf:"bytes,2,opt,name=Nickname,proto3" json:"Nickname,omitempty"`
	Sex       int64   `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	Avatar    string  `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Info      string  `protobuf:"bytes,5,opt,name=info,proto3" json:"info,omitempty"`
	Signature string  `protobuf:"bytes,6,opt,name=signature,proto3" json:"signature,omitempty"`
	Longitude float64 `protobuf:"fixed64,7,opt,name=longitude,proto3" json:"longitude,omitempty"`
	Latitude  float64 `protobuf:"fixed64,8,opt,name=latitude,proto3" json:"latitude,omitempty"`
}

func (x *UpdateUserBaseInfoReq) Reset() {
	*x = UpdateUserBaseInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserBaseInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserBaseInfoReq) ProtoMessage() {}

func (x *UpdateUserBaseInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserBaseInfoReq.ProtoReflect.Descriptor instead.
func (*UpdateUserBaseInfoReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{76}
}

func (x *UpdateUserBaseInfoReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateUserBaseInfoReq) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *UpdateUserBaseInfoReq) GetSex() int64 {
	if x != nil {
		return x.Sex
	}
	return 0
}

func (x *UpdateUserBaseInfoReq) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UpdateUserBaseInfoReq) GetInfo() string {
	if x != nil {
		return x.Info
	}
	return ""
}

func (x *UpdateUserBaseInfoReq) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

func (x *UpdateUserBaseInfoReq) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *UpdateUserBaseInfoReq) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

type UpdateUserBaseInfoResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateUserBaseInfoResp) Reset() {
	*x = UpdateUserBaseInfoResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserBaseInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserBaseInfoResp) ProtoMessage() {}

func (x *UpdateUserBaseInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserBaseInfoResp.ProtoReflect.Descriptor instead.
func (*UpdateUserBaseInfoResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{77}
}

type SetAdminReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId int64 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
}

func (x *SetAdminReq) Reset() {
	*x = SetAdminReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetAdminReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetAdminReq) ProtoMessage() {}

func (x *SetAdminReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetAdminReq.ProtoReflect.Descriptor instead.
func (*SetAdminReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{78}
}

func (x *SetAdminReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type SetAdminResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetAdminResp) Reset() {
	*x = SetAdminResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetAdminResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetAdminResp) ProtoMessage() {}

func (x *SetAdminResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetAdminResp.ProtoReflect.Descriptor instead.
func (*SetAdminResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{79}
}

// --------------------------------抽奖发起人联系方式（抽奖赞助商）--------------------------------
type UserSponsor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                  //id
	UserId     int64  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`          //userId
	Type       int64  `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`              //1微信号 2公众号 3小程序 4微信群 5视频号
	AppletType int64  `protobuf:"varint,4,opt,name=appletType,proto3" json:"appletType,omitempty"`  //type=3时该字段才有意义，1小程序链接 2路径跳转 3二维码跳转
	Name       string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`               //名称
	Desc       string `protobuf:"bytes,6,opt,name=desc,proto3" json:"desc,omitempty"`               //描述
	Avatar     string `protobuf:"bytes,7,opt,name=avatar,proto3" json:"avatar,omitempty"`           //avatar
	IsShow     int64  `protobuf:"varint,8,opt,name=isShow,proto3" json:"isShow,omitempty"`          //1显示 2不显示
	QrCode     string `protobuf:"bytes,9,opt,name=qrCode,proto3" json:"qrCode,omitempty"`           //二维码图片地址, type=1 2 3&applet_type=3 4的时候启用
	InputA     string `protobuf:"bytes,10,opt,name=inputA,proto3" json:"inputA,omitempty"`          //type=5 applet_type=2 or applet_type=1 输入框A
	InputB     string `protobuf:"bytes,11,opt,name=inputB,proto3" json:"inputB,omitempty"`          //type=5 applet_type=2输入框B
	CreateTime int64  `protobuf:"varint,12,opt,name=createTime,proto3" json:"createTime,omitempty"` //createTime
	UpdateTime int64  `protobuf:"varint,13,opt,name=updateTime,proto3" json:"updateTime,omitempty"` //updateTime
}

func (x *UserSponsor) Reset() {
	*x = UserSponsor{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserSponsor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSponsor) ProtoMessage() {}

func (x *UserSponsor) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSponsor.ProtoReflect.Descriptor instead.
func (*UserSponsor) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{80}
}

func (x *UserSponsor) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserSponsor) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserSponsor) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *UserSponsor) GetAppletType() int64 {
	if x != nil {
		return x.AppletType
	}
	return 0
}

func (x *UserSponsor) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserSponsor) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *UserSponsor) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UserSponsor) GetIsShow() int64 {
	if x != nil {
		return x.IsShow
	}
	return 0
}

func (x *UserSponsor) GetQrCode() string {
	if x != nil {
		return x.QrCode
	}
	return ""
}

func (x *UserSponsor) GetInputA() string {
	if x != nil {
		return x.InputA
	}
	return ""
}

func (x *UserSponsor) GetInputB() string {
	if x != nil {
		return x.InputB
	}
	return ""
}

func (x *UserSponsor) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *UserSponsor) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

type AddUserSponsorReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId     int64  `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`         //userId
	Type       int64  `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`             //1微信号 2公众号 3小程序 4微信群 5视频号
	AppletType int64  `protobuf:"varint,3,opt,name=appletType,proto3" json:"appletType,omitempty"` //type=3时该字段才有意义，1小程序链接 2路径跳转 3二维码跳转
	Name       string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`              //名称
	Desc       string `protobuf:"bytes,5,opt,name=desc,proto3" json:"desc,omitempty"`              //描述
	Avatar     string `protobuf:"bytes,6,opt,name=avatar,proto3" json:"avatar,omitempty"`          //avatar
	IsShow     int64  `protobuf:"varint,7,opt,name=isShow,proto3" json:"isShow,omitempty"`         //1显示 2不显示
	QrCode     string `protobuf:"bytes,8,opt,name=qrCode,proto3" json:"qrCode,omitempty"`          //二维码图片地址, type=1 2 3&applet_type=3 4的时候启用
	InputA     string `protobuf:"bytes,9,opt,name=inputA,proto3" json:"inputA,omitempty"`          //type=5 applet_type=2 or applet_type=1 输入框A
	InputB     string `protobuf:"bytes,10,opt,name=inputB,proto3" json:"inputB,omitempty"`         //type=5 applet_type=2输入框B
}

func (x *AddUserSponsorReq) Reset() {
	*x = AddUserSponsorReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddUserSponsorReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserSponsorReq) ProtoMessage() {}

func (x *AddUserSponsorReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserSponsorReq.ProtoReflect.Descriptor instead.
func (*AddUserSponsorReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{81}
}

func (x *AddUserSponsorReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AddUserSponsorReq) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *AddUserSponsorReq) GetAppletType() int64 {
	if x != nil {
		return x.AppletType
	}
	return 0
}

func (x *AddUserSponsorReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AddUserSponsorReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *AddUserSponsorReq) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *AddUserSponsorReq) GetIsShow() int64 {
	if x != nil {
		return x.IsShow
	}
	return 0
}

func (x *AddUserSponsorReq) GetQrCode() string {
	if x != nil {
		return x.QrCode
	}
	return ""
}

func (x *AddUserSponsorReq) GetInputA() string {
	if x != nil {
		return x.InputA
	}
	return ""
}

func (x *AddUserSponsorReq) GetInputB() string {
	if x != nil {
		return x.InputB
	}
	return ""
}

type AddUserSponsorResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"` //id
}

func (x *AddUserSponsorResp) Reset() {
	*x = AddUserSponsorResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddUserSponsorResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserSponsorResp) ProtoMessage() {}

func (x *AddUserSponsorResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserSponsorResp.ProtoReflect.Descriptor instead.
func (*AddUserSponsorResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{82}
}

func (x *AddUserSponsorResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdateUserSponsorReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                 //id
	UserId     int64  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`         //userId
	Type       int64  `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`             //1微信号 2公众号 3小程序 4微信群 5视频号
	AppletType int64  `protobuf:"varint,4,opt,name=appletType,proto3" json:"appletType,omitempty"` //type=3时该字段才有意义，1小程序链接 2路径跳转 3二维码跳转
	Name       string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`              //名称
	Desc       string `protobuf:"bytes,6,opt,name=desc,proto3" json:"desc,omitempty"`              //描述
	Avatar     string `protobuf:"bytes,7,opt,name=avatar,proto3" json:"avatar,omitempty"`          //avatar
	IsShow     int64  `protobuf:"varint,8,opt,name=isShow,proto3" json:"isShow,omitempty"`         //1显示 2不显示
	QrCode     string `protobuf:"bytes,9,opt,name=qrCode,proto3" json:"qrCode,omitempty"`          //二维码图片地址, type=1 2 3&applet_type=3 4的时候启用
	InputA     string `protobuf:"bytes,10,opt,name=inputA,proto3" json:"inputA,omitempty"`         //type=5 applet_type=2 or applet_type=1 输入框A
	InputB     string `protobuf:"bytes,11,opt,name=inputB,proto3" json:"inputB,omitempty"`         //type=5 applet_type=2输入框B
}

func (x *UpdateUserSponsorReq) Reset() {
	*x = UpdateUserSponsorReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserSponsorReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserSponsorReq) ProtoMessage() {}

func (x *UpdateUserSponsorReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserSponsorReq.ProtoReflect.Descriptor instead.
func (*UpdateUserSponsorReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{83}
}

func (x *UpdateUserSponsorReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateUserSponsorReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UpdateUserSponsorReq) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *UpdateUserSponsorReq) GetAppletType() int64 {
	if x != nil {
		return x.AppletType
	}
	return 0
}

func (x *UpdateUserSponsorReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateUserSponsorReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *UpdateUserSponsorReq) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UpdateUserSponsorReq) GetIsShow() int64 {
	if x != nil {
		return x.IsShow
	}
	return 0
}

func (x *UpdateUserSponsorReq) GetQrCode() string {
	if x != nil {
		return x.QrCode
	}
	return ""
}

func (x *UpdateUserSponsorReq) GetInputA() string {
	if x != nil {
		return x.InputA
	}
	return ""
}

func (x *UpdateUserSponsorReq) GetInputB() string {
	if x != nil {
		return x.InputB
	}
	return ""
}

type UpdateUserSponsorResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateUserSponsorResp) Reset() {
	*x = UpdateUserSponsorResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserSponsorResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserSponsorResp) ProtoMessage() {}

func (x *UpdateUserSponsorResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserSponsorResp.ProtoReflect.Descriptor instead.
func (*UpdateUserSponsorResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{84}
}

type DelUserSponsorReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *DelUserSponsorReq) Reset() {
	*x = DelUserSponsorReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelUserSponsorReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelUserSponsorReq) ProtoMessage() {}

func (x *DelUserSponsorReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelUserSponsorReq.ProtoReflect.Descriptor instead.
func (*DelUserSponsorReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{85}
}

func (x *DelUserSponsorReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DelUserSponsorResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DelUserSponsorResp) Reset() {
	*x = DelUserSponsorResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[86]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelUserSponsorResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelUserSponsorResp) ProtoMessage() {}

func (x *DelUserSponsorResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[86]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelUserSponsorResp.ProtoReflect.Descriptor instead.
func (*DelUserSponsorResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{86}
}

type GetUserSponsorByIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *GetUserSponsorByIdReq) Reset() {
	*x = GetUserSponsorByIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[87]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserSponsorByIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserSponsorByIdReq) ProtoMessage() {}

func (x *GetUserSponsorByIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[87]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserSponsorByIdReq.ProtoReflect.Descriptor instead.
func (*GetUserSponsorByIdReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{87}
}

func (x *GetUserSponsorByIdReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetUserSponsorByIdResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserSponsor *UserSponsor `protobuf:"bytes,1,opt,name=userSponsor,proto3" json:"userSponsor,omitempty"` //userSponsor
}

func (x *GetUserSponsorByIdResp) Reset() {
	*x = GetUserSponsorByIdResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[88]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserSponsorByIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserSponsorByIdResp) ProtoMessage() {}

func (x *GetUserSponsorByIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[88]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserSponsorByIdResp.ProtoReflect.Descriptor instead.
func (*GetUserSponsorByIdResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{88}
}

func (x *GetUserSponsorByIdResp) GetUserSponsor() *UserSponsor {
	if x != nil {
		return x.UserSponsor
	}
	return nil
}

type SearchUserSponsorReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page       int64  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`              //page
	Limit      int64  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`            //limit
	Id         int64  `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`                  //id
	UserId     int64  `protobuf:"varint,4,opt,name=userId,proto3" json:"userId,omitempty"`          //userId
	Type       int64  `protobuf:"varint,5,opt,name=type,proto3" json:"type,omitempty"`              //1微信号 2公众号 3小程序 4微信群 5视频号
	AppletType int64  `protobuf:"varint,6,opt,name=appletType,proto3" json:"appletType,omitempty"`  //type=3时该字段才有意义，1小程序链接 2路径跳转 3二维码跳转
	Name       string `protobuf:"bytes,7,opt,name=name,proto3" json:"name,omitempty"`               //名称
	Desc       string `protobuf:"bytes,8,opt,name=desc,proto3" json:"desc,omitempty"`               //描述
	Avatar     string `protobuf:"bytes,9,opt,name=avatar,proto3" json:"avatar,omitempty"`           //avatar
	IsShow     int64  `protobuf:"varint,10,opt,name=isShow,proto3" json:"isShow,omitempty"`         //1显示 2不显示
	QrCode     string `protobuf:"bytes,11,opt,name=qrCode,proto3" json:"qrCode,omitempty"`          //二维码图片地址, type=1 2 3&applet_type=3 4的时候启用
	InputA     string `protobuf:"bytes,12,opt,name=inputA,proto3" json:"inputA,omitempty"`          //type=5 applet_type=2 or applet_type=1 输入框A
	InputB     string `protobuf:"bytes,13,opt,name=inputB,proto3" json:"inputB,omitempty"`          //type=5 applet_type=2输入框B
	CreateTime int64  `protobuf:"varint,14,opt,name=createTime,proto3" json:"createTime,omitempty"` //createTime
	UpdateTime int64  `protobuf:"varint,15,opt,name=updateTime,proto3" json:"updateTime,omitempty"` //updateTime
}

func (x *SearchUserSponsorReq) Reset() {
	*x = SearchUserSponsorReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[89]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchUserSponsorReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUserSponsorReq) ProtoMessage() {}

func (x *SearchUserSponsorReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[89]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUserSponsorReq.ProtoReflect.Descriptor instead.
func (*SearchUserSponsorReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{89}
}

func (x *SearchUserSponsorReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchUserSponsorReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *SearchUserSponsorReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SearchUserSponsorReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *SearchUserSponsorReq) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *SearchUserSponsorReq) GetAppletType() int64 {
	if x != nil {
		return x.AppletType
	}
	return 0
}

func (x *SearchUserSponsorReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchUserSponsorReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *SearchUserSponsorReq) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *SearchUserSponsorReq) GetIsShow() int64 {
	if x != nil {
		return x.IsShow
	}
	return 0
}

func (x *SearchUserSponsorReq) GetQrCode() string {
	if x != nil {
		return x.QrCode
	}
	return ""
}

func (x *SearchUserSponsorReq) GetInputA() string {
	if x != nil {
		return x.InputA
	}
	return ""
}

func (x *SearchUserSponsorReq) GetInputB() string {
	if x != nil {
		return x.InputB
	}
	return ""
}

func (x *SearchUserSponsorReq) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *SearchUserSponsorReq) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

type SearchUserSponsorResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserSponsor []*UserSponsor `protobuf:"bytes,1,rep,name=userSponsor,proto3" json:"userSponsor,omitempty"` //userSponsor
}

func (x *SearchUserSponsorResp) Reset() {
	*x = SearchUserSponsorResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[90]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchUserSponsorResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUserSponsorResp) ProtoMessage() {}

func (x *SearchUserSponsorResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[90]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUserSponsorResp.ProtoReflect.Descriptor instead.
func (*SearchUserSponsorResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{90}
}

func (x *SearchUserSponsorResp) GetUserSponsor() []*UserSponsor {
	if x != nil {
		return x.UserSponsor
	}
	return nil
}

type SponsorDetailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *SponsorDetailReq) Reset() {
	*x = SponsorDetailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[91]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SponsorDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SponsorDetailReq) ProtoMessage() {}

func (x *SponsorDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[91]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SponsorDetailReq.ProtoReflect.Descriptor instead.
func (*SponsorDetailReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{91}
}

func (x *SponsorDetailReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type SponsorDetailResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                 //id
	UserId     int64  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`         //userId
	Type       int64  `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`             //1微信号 2公众号 3小程序 4微信群 5视频号
	AppletType int64  `protobuf:"varint,4,opt,name=appletType,proto3" json:"appletType,omitempty"` //type=3时该字段才有意义，1小程序链接 2路径跳转 3二维码跳转
	Name       string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`              //名称
	Desc       string `protobuf:"bytes,6,opt,name=desc,proto3" json:"desc,omitempty"`              //描述
	Avatar     string `protobuf:"bytes,7,opt,name=avatar,proto3" json:"avatar,omitempty"`          //avatar
	IsShow     int64  `protobuf:"varint,8,opt,name=isShow,proto3" json:"isShow,omitempty"`         //1显示 2不显示
	QrCode     string `protobuf:"bytes,9,opt,name=qrCode,proto3" json:"qrCode,omitempty"`          //二维码图片地址, type=1 2 3&applet_type=3 4的时候启用
	InputA     string `protobuf:"bytes,10,opt,name=inputA,proto3" json:"inputA,omitempty"`         //type=5 applet_type=2 or applet_type=1 输入框A
	InputB     string `protobuf:"bytes,11,opt,name=inputB,proto3" json:"inputB,omitempty"`         //type=5 applet_type=2输入框B
}

func (x *SponsorDetailResp) Reset() {
	*x = SponsorDetailResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[92]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SponsorDetailResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SponsorDetailResp) ProtoMessage() {}

func (x *SponsorDetailResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[92]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SponsorDetailResp.ProtoReflect.Descriptor instead.
func (*SponsorDetailResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{92}
}

func (x *SponsorDetailResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SponsorDetailResp) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *SponsorDetailResp) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *SponsorDetailResp) GetAppletType() int64 {
	if x != nil {
		return x.AppletType
	}
	return 0
}

func (x *SponsorDetailResp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SponsorDetailResp) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *SponsorDetailResp) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *SponsorDetailResp) GetIsShow() int64 {
	if x != nil {
		return x.IsShow
	}
	return 0
}

func (x *SponsorDetailResp) GetQrCode() string {
	if x != nil {
		return x.QrCode
	}
	return ""
}

func (x *SponsorDetailResp) GetInputA() string {
	if x != nil {
		return x.InputA
	}
	return ""
}

func (x *SponsorDetailResp) GetInputB() string {
	if x != nil {
		return x.InputB
	}
	return ""
}

// -------------------------------- 用户发布动态 --------------------------------
type UserDynamic struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                 //id
	UserId     int64  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`         //userId
	DynamicUrl string `protobuf:"bytes,3,opt,name=dynamicUrl,proto3" json:"dynamicUrl,omitempty"`  //文件地址链接
	Remark     string `protobuf:"bytes,4,opt,name=remark,proto3" json:"remark,omitempty"`          //备注
	UpdateTime int64  `protobuf:"varint,6,opt,name=updateTime,proto3" json:"updateTime,omitempty"` //updateTime
}

func (x *UserDynamic) Reset() {
	*x = UserDynamic{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[93]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserDynamic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserDynamic) ProtoMessage() {}

func (x *UserDynamic) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[93]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserDynamic.ProtoReflect.Descriptor instead.
func (*UserDynamic) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{93}
}

func (x *UserDynamic) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserDynamic) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserDynamic) GetDynamicUrl() string {
	if x != nil {
		return x.DynamicUrl
	}
	return ""
}

func (x *UserDynamic) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *UserDynamic) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

type AddUserDynamicReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId     int64  `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`        //userId
	DynamicUrl string `protobuf:"bytes,2,opt,name=dynamicUrl,proto3" json:"dynamicUrl,omitempty"` //文件地址链接
	Remark     string `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark,omitempty"`         //备注
}

func (x *AddUserDynamicReq) Reset() {
	*x = AddUserDynamicReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[94]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddUserDynamicReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserDynamicReq) ProtoMessage() {}

func (x *AddUserDynamicReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[94]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserDynamicReq.ProtoReflect.Descriptor instead.
func (*AddUserDynamicReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{94}
}

func (x *AddUserDynamicReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AddUserDynamicReq) GetDynamicUrl() string {
	if x != nil {
		return x.DynamicUrl
	}
	return ""
}

func (x *AddUserDynamicReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

type AddUserDynamicResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"` //id
}

func (x *AddUserDynamicResp) Reset() {
	*x = AddUserDynamicResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[95]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddUserDynamicResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserDynamicResp) ProtoMessage() {}

func (x *AddUserDynamicResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[95]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserDynamicResp.ProtoReflect.Descriptor instead.
func (*AddUserDynamicResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{95}
}

func (x *AddUserDynamicResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdateUserDynamicReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                //id
	UserId     int64  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`        //userId
	DynamicUrl string `protobuf:"bytes,3,opt,name=dynamicUrl,proto3" json:"dynamicUrl,omitempty"` //文件地址链接
	Remark     string `protobuf:"bytes,4,opt,name=remark,proto3" json:"remark,omitempty"`         //备注
}

func (x *UpdateUserDynamicReq) Reset() {
	*x = UpdateUserDynamicReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[96]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserDynamicReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserDynamicReq) ProtoMessage() {}

func (x *UpdateUserDynamicReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[96]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserDynamicReq.ProtoReflect.Descriptor instead.
func (*UpdateUserDynamicReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{96}
}

func (x *UpdateUserDynamicReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateUserDynamicReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UpdateUserDynamicReq) GetDynamicUrl() string {
	if x != nil {
		return x.DynamicUrl
	}
	return ""
}

func (x *UpdateUserDynamicReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

type UpdateUserDynamicResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateUserDynamicResp) Reset() {
	*x = UpdateUserDynamicResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[97]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserDynamicResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserDynamicResp) ProtoMessage() {}

func (x *UpdateUserDynamicResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[97]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserDynamicResp.ProtoReflect.Descriptor instead.
func (*UpdateUserDynamicResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{97}
}

type DelUserDynamicReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`         //id
	UserId int64 `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"` //userId
}

func (x *DelUserDynamicReq) Reset() {
	*x = DelUserDynamicReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[98]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelUserDynamicReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelUserDynamicReq) ProtoMessage() {}

func (x *DelUserDynamicReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[98]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelUserDynamicReq.ProtoReflect.Descriptor instead.
func (*DelUserDynamicReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{98}
}

func (x *DelUserDynamicReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DelUserDynamicReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type DelUserDynamicResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DelUserDynamicResp) Reset() {
	*x = DelUserDynamicResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[99]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelUserDynamicResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelUserDynamicResp) ProtoMessage() {}

func (x *DelUserDynamicResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[99]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelUserDynamicResp.ProtoReflect.Descriptor instead.
func (*DelUserDynamicResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{99}
}

type GetUserDynamicByIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *GetUserDynamicByIdReq) Reset() {
	*x = GetUserDynamicByIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[100]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserDynamicByIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserDynamicByIdReq) ProtoMessage() {}

func (x *GetUserDynamicByIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[100]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserDynamicByIdReq.ProtoReflect.Descriptor instead.
func (*GetUserDynamicByIdReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{100}
}

func (x *GetUserDynamicByIdReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetUserDynamicByIdResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserDynamic *UserDynamic `protobuf:"bytes,1,opt,name=userDynamic,proto3" json:"userDynamic,omitempty"` //userDynamic
}

func (x *GetUserDynamicByIdResp) Reset() {
	*x = GetUserDynamicByIdResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[101]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserDynamicByIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserDynamicByIdResp) ProtoMessage() {}

func (x *GetUserDynamicByIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[101]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserDynamicByIdResp.ProtoReflect.Descriptor instead.
func (*GetUserDynamicByIdResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{101}
}

func (x *GetUserDynamicByIdResp) GetUserDynamic() *UserDynamic {
	if x != nil {
		return x.UserDynamic
	}
	return nil
}

type GetUserDynamicByUserIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId int64 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"` //id
}

func (x *GetUserDynamicByUserIdReq) Reset() {
	*x = GetUserDynamicByUserIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[102]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserDynamicByUserIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserDynamicByUserIdReq) ProtoMessage() {}

func (x *GetUserDynamicByUserIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[102]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserDynamicByUserIdReq.ProtoReflect.Descriptor instead.
func (*GetUserDynamicByUserIdReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{102}
}

func (x *GetUserDynamicByUserIdReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GetUserDynamicByUserIdResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserDynamic *UserDynamic `protobuf:"bytes,1,opt,name=userDynamic,proto3" json:"userDynamic,omitempty"` //userDynamic
}

func (x *GetUserDynamicByUserIdResp) Reset() {
	*x = GetUserDynamicByUserIdResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[103]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserDynamicByUserIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserDynamicByUserIdResp) ProtoMessage() {}

func (x *GetUserDynamicByUserIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[103]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserDynamicByUserIdResp.ProtoReflect.Descriptor instead.
func (*GetUserDynamicByUserIdResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{103}
}

func (x *GetUserDynamicByUserIdResp) GetUserDynamic() *UserDynamic {
	if x != nil {
		return x.UserDynamic
	}
	return nil
}

type SearchUserDynamicReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page       int64  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`             //page
	Limit      int64  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`           //limit
	Id         int64  `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`                 //id
	UserId     int64  `protobuf:"varint,4,opt,name=userId,proto3" json:"userId,omitempty"`         //userId
	DynamicUrl string `protobuf:"bytes,5,opt,name=dynamicUrl,proto3" json:"dynamicUrl,omitempty"`  //文件地址链接
	Remark     string `protobuf:"bytes,6,opt,name=remark,proto3" json:"remark,omitempty"`          //备注
	UpdateTime int64  `protobuf:"varint,8,opt,name=updateTime,proto3" json:"updateTime,omitempty"` //updateTime
}

func (x *SearchUserDynamicReq) Reset() {
	*x = SearchUserDynamicReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[104]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchUserDynamicReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUserDynamicReq) ProtoMessage() {}

func (x *SearchUserDynamicReq) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[104]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUserDynamicReq.ProtoReflect.Descriptor instead.
func (*SearchUserDynamicReq) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{104}
}

func (x *SearchUserDynamicReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchUserDynamicReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *SearchUserDynamicReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SearchUserDynamicReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *SearchUserDynamicReq) GetDynamicUrl() string {
	if x != nil {
		return x.DynamicUrl
	}
	return ""
}

func (x *SearchUserDynamicReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *SearchUserDynamicReq) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

type SearchUserDynamicResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserDynamic []*UserDynamic `protobuf:"bytes,1,rep,name=userDynamic,proto3" json:"userDynamic,omitempty"` //userDynamic
}

func (x *SearchUserDynamicResp) Reset() {
	*x = SearchUserDynamicResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_usercenter_proto_msgTypes[105]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchUserDynamicResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchUserDynamicResp) ProtoMessage() {}

func (x *SearchUserDynamicResp) ProtoReflect() protoreflect.Message {
	mi := &file_usercenter_proto_msgTypes[105]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchUserDynamicResp.ProtoReflect.Descriptor instead.
func (*SearchUserDynamicResp) Descriptor() ([]byte, []int) {
	return file_usercenter_proto_rawDescGZIP(), []int{105}
}

func (x *SearchUserDynamicResp) GetUserDynamic() []*UserDynamic {
	if x != nil {
		return x.UserDynamic
	}
	return nil
}

var File_usercenter_proto protoreflect.FileDescriptor

var file_usercenter_proto_rawDesc = []byte{
	0x0a, 0x10, 0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x02, 0x70, 0x62, 0x22, 0xa0, 0x04, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77,
	0x6f, 0x72, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77,
	0x6f, 0x72, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x73, 0x65, 0x78, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x73, 0x65,
	0x78, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a,
	0x07, 0x69, 0x73, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x69, 0x73, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e,
	0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x6c, 0x6f,
	0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74,
	0x75, 0x64, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74,
	0x75, 0x64, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x7a,
	0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72,
	0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x61, 0x6e, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x04, 0x66, 0x61, 0x6e, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x6c, 0x6c, 0x4c, 0x6f,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x61, 0x6c, 0x6c,
	0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x12, 0x2a, 0x0a, 0x10, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x10, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x77, 0x69, 0x6e, 0x6e,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x22, 0xd6, 0x03, 0x0a, 0x0a, 0x41, 0x64,
	0x64, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69,
	0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x78, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x73, 0x65, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x73, 0x41, 0x64, 0x6d, 0x69,
	0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x69, 0x73, 0x41, 0x64, 0x6d, 0x69, 0x6e,
	0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x22,
	0x0a, 0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x66, 0x61, 0x6e, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x66, 0x61, 0x6e, 0x73,
	0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x6c, 0x6c, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x61, 0x6c, 0x6c, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79,
	0x12, 0x2a, 0x0a, 0x10, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x24, 0x0a, 0x0d,
	0x77, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0d, 0x77, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x22, 0x0d, 0x0a, 0x0b, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x22, 0xe9, 0x03, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70,
	0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x03, 0x73, 0x65, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x12, 0x0a,
	0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6e, 0x66,
	0x6f, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x73, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x69, 0x73, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x73,
	0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c,
	0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x6c,
	0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x50, 0x72, 0x69, 0x7a, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x61, 0x6e, 0x73, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x66, 0x61, 0x6e, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x61,
	0x6c, 0x6c, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x61, 0x6c, 0x6c, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x12, 0x2a, 0x0a, 0x10, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x77, 0x69, 0x6e, 0x6e, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d,
	0x77, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x22, 0x10, 0x0a,
	0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x1c, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x0d, 0x0a,
	0x0b, 0x44, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x22, 0x20, 0x0a, 0x0e,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x2f,
	0x0a, 0x0f, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x1c, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x08, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x22,
	0xd3, 0x04, 0x0a, 0x0d, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d,
	0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x62,
	0x69, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x73,
	0x65, 0x78, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x73, 0x65, 0x78, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x73, 0x41,
	0x64, 0x6d, 0x69, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x69, 0x73, 0x41, 0x64,
	0x6d, 0x69, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x12, 0x22, 0x0a, 0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75,
	0x64, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74,
	0x75, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x66, 0x61, 0x6e, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x66,
	0x61, 0x6e, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x6c, 0x6c, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72,
	0x79, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x61, 0x6c, 0x6c, 0x4c, 0x6f, 0x74, 0x74,
	0x65, 0x72, 0x79, 0x12, 0x2a, 0x0a, 0x10, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12,
	0x24, 0x0a, 0x0d, 0x77, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x77, 0x69, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x22, 0x2e, 0x0a, 0x0e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1c, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x08, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x04, 0x75, 0x73, 0x65, 0x72, 0x22, 0xab, 0x02, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a,
	0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x24, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4d,
	0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73,
	0x74, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6f, 0x73,
	0x74, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x22, 0xe1, 0x01, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x63, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x6f, 0x73, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x6f, 0x73, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x44,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x69, 0x73,
	0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x22, 0x24, 0x0a, 0x12, 0x41, 0x64, 0x64, 0x55, 0x73,
	0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x0e, 0x0a,
	0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x49, 0x64, 0x22, 0xf4, 0x01,
	0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20,
	0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x24, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4d, 0x6f, 0x62, 0x69, 0x6c,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69,
	0x63, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69,
	0x63, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f,
	0x73, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6f,
	0x73, 0x74, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x22, 0x17, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x23, 0x0a,
	0x11, 0x44, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52,
	0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x22, 0x14, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x27, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65,
	0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x22, 0x4b, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x0b, 0x75,
	0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0f, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0xde,
	0x02, 0x0a, 0x14, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x4d, 0x6f, 0x62, 0x69, 0x6c,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x74, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x74, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12,
	0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22,
	0x4a, 0x0a, 0x15, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x70, 0x62, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0b,
	0x75, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0xa8, 0x01, 0x0a, 0x08,
	0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x75, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x75, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x75,
	0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x75,
	0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x22, 0x5e, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65,
	0x72, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x75, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x75, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x75,
	0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x75,
	0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x22, 0x11, 0x0a, 0x0f, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65,
	0x72, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x22, 0x71, 0x0a, 0x11, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x75, 0x74, 0x68, 0x4b, 0x65,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x75, 0x74, 0x68, 0x4b, 0x65, 0x79,
	0x12, 0x1a, 0x0a, 0x08, 0x61, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x22, 0x14, 0x0a, 0x12,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65,
	0x73, 0x70, 0x22, 0x20, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74,
	0x68, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x11, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x41,
	0x75, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x22, 0x24, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x3f, 0x0a,
	0x13, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x42, 0x79, 0x49, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x28, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x41, 0x75, 0x74, 0x68, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x22, 0xdb,
	0x01, 0x0a, 0x11, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74,
	0x68, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1e,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x75, 0x74, 0x68, 0x4b, 0x65,
	0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x75, 0x74, 0x68, 0x4b, 0x65, 0x79,
	0x12, 0x1a, 0x0a, 0x08, 0x61, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x22, 0x3e, 0x0a, 0x12,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x28, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75,
	0x74, 0x68, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x22, 0x58, 0x0a, 0x12,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x6f, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x22, 0x33, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x52, 0x65,
	0x71, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x22, 0x4e, 0x0a, 0x18, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x32, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x62, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x6f, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xa7, 0x01, 0x0a, 0x0b,
	0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72,
	0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x5d, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x6d, 0x61, 0x72, 0x6b, 0x22, 0x24, 0x0a, 0x12, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x56, 0x0a, 0x12, 0x45, 0x64,
	0x69, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71,
	0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x49, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65,
	0x6d, 0x61, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x22, 0x25, 0x0a, 0x13, 0x45, 0x64, 0x69, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f,
	0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x58, 0x0a, 0x14, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d,
	0x61, 0x72, 0x6b, 0x22, 0x27, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x23, 0x0a, 0x11,
	0x44, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x22, 0x14, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x27, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x22, 0x4b, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x0b, 0x75, 0x73,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0f, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x22, 0x58, 0x0a,
	0x14, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x4a, 0x0a, 0x15, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x31, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x22, 0xa2, 0x01, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x53, 0x68, 0x6f, 0x70,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x58, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x55,
	0x73, 0x65, 0x72, 0x53, 0x68, 0x6f, 0x70, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x11, 0x0a, 0x0f, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x53, 0x68, 0x6f,
	0x70, 0x52, 0x65, 0x73, 0x70, 0x22, 0x6b, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x53, 0x68, 0x6f, 0x70, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x14, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x53, 0x68, 0x6f, 0x70, 0x52, 0x65, 0x73, 0x70, 0x22, 0x20, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x55,
	0x73, 0x65, 0x72, 0x53, 0x68, 0x6f, 0x70, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x11, 0x0a, 0x0f, 0x44, 0x65,
	0x6c, 0x55, 0x73, 0x65, 0x72, 0x53, 0x68, 0x6f, 0x70, 0x52, 0x65, 0x73, 0x70, 0x22, 0x24, 0x0a,
	0x12, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x68, 0x6f, 0x70, 0x42, 0x79, 0x49, 0x64,
	0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x22, 0x3f, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x68,
	0x6f, 0x70, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x28, 0x0a, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x53, 0x68, 0x6f, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70,
	0x62, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x68, 0x6f, 0x70, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x53, 0x68, 0x6f, 0x70, 0x22, 0xd5, 0x01, 0x0a, 0x11, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55,
	0x73, 0x65, 0x72, 0x53, 0x68, 0x6f, 0x70, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x3e, 0x0a, 0x12,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x53, 0x68, 0x6f, 0x70, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x28, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x53, 0x68, 0x6f, 0x70, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x68,
	0x6f, 0x70, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x53, 0x68, 0x6f, 0x70, 0x22, 0x29, 0x0a, 0x0f,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x73, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x12,
	0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x2c, 0x0a, 0x10, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x49, 0x73, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x69,
	0x73, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73,
	0x41, 0x64, 0x6d, 0x69, 0x6e, 0x22, 0x7d, 0x0a, 0x11, 0x57, 0x58, 0x4d, 0x69, 0x6e, 0x69, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69,
	0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69,
	0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x18,
	0x0a, 0x07, 0x61, 0x75, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x75, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x75, 0x74, 0x68,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68,
	0x54, 0x79, 0x70, 0x65, 0x22, 0x7e, 0x0a, 0x12, 0x57, 0x58, 0x4d, 0x69, 0x6e, 0x69, 0x52, 0x65,
	0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x22, 0x0a, 0x0c,
	0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x41, 0x66, 0x74, 0x65, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x41,
	0x66, 0x74, 0x65, 0x72, 0x22, 0x5c, 0x0a, 0x08, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71,
	0x12, 0x1a, 0x0a, 0x08, 0x61, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x75, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x75, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x22, 0x75, 0x0a, 0x09, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x20, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x45, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x45,
	0x78, 0x70, 0x69, 0x72, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68,
	0x41, 0x66, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x72, 0x65, 0x66,
	0x72, 0x65, 0x73, 0x68, 0x41, 0x66, 0x74, 0x65, 0x72, 0x22, 0x20, 0x0a, 0x0e, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x2f, 0x0a, 0x0f, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1c,
	0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x08, 0x2e, 0x70,
	0x62, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x22, 0x4f, 0x0a, 0x17,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x42, 0x79, 0x41, 0x75, 0x74,
	0x68, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x75, 0x74, 0x68, 0x4b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x75, 0x74, 0x68, 0x4b, 0x65,
	0x79, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x22, 0x44, 0x0a,
	0x18, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x42, 0x79, 0x41, 0x75,
	0x74, 0x68, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x28, 0x0a, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x41, 0x75, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x62,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x41,
	0x75, 0x74, 0x68, 0x22, 0x4c, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75,
	0x74, 0x68, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x42, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68,
	0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x28, 0x0a, 0x08, 0x75,
	0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e,
	0x70, 0x62, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x52, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x41, 0x75, 0x74, 0x68, 0x22, 0x2a, 0x0a, 0x10, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x22, 0x7d, 0x0a, 0x11, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c,
	0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x12, 0x22, 0x0a, 0x0c,
	0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x41, 0x66, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x41, 0x66, 0x74, 0x65, 0x72,
	0x22, 0x93, 0x01, 0x0a, 0x0b, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x75, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x75, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x75,
	0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x75,
	0x74, 0x68, 0x54, 0x79, 0x70, 0x65, 0x22, 0x78, 0x0a, 0x0c, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c,
	0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x12, 0x22, 0x0a, 0x0c,
	0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x41, 0x66, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x41, 0x66, 0x74, 0x65, 0x72,
	0x22, 0xd9, 0x01, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x42,
	0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x4e, 0x69,
	0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4e, 0x69,
	0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x78, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x03, 0x73, 0x65, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x12, 0x12, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x69, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x22, 0x18, 0x0a, 0x16,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x22, 0x25, 0x0a, 0x0b, 0x53, 0x65, 0x74, 0x41, 0x64, 0x6d,
	0x69, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x0e, 0x0a,
	0x0c, 0x53, 0x65, 0x74, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x22, 0xc9, 0x02,
	0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x70, 0x70,
	0x6c, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x61,
	0x70, 0x70, 0x6c, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73,
	0x63, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x73, 0x53,
	0x68, 0x6f, 0x77, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x69, 0x73, 0x53, 0x68, 0x6f,
	0x77, 0x12, 0x16, 0x0a, 0x06, 0x71, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x71, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6e, 0x70,
	0x75, 0x74, 0x41, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74,
	0x41, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x42, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x42, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xff, 0x01, 0x0a, 0x11, 0x41, 0x64,
	0x64, 0x55, 0x73, 0x65, 0x72, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x12,
	0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61,
	0x70, 0x70, 0x6c, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64,
	0x65, 0x73, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x69,
	0x73, 0x53, 0x68, 0x6f, 0x77, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x69, 0x73, 0x53,
	0x68, 0x6f, 0x77, 0x12, 0x16, 0x0a, 0x06, 0x71, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x71, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x69,
	0x6e, 0x70, 0x75, 0x74, 0x41, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6e, 0x70,
	0x75, 0x74, 0x41, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x42, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x42, 0x22, 0x24, 0x0a, 0x12, 0x41,
	0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x49,
	0x64, 0x22, 0x92, 0x02, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x6c,
	0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65,
	0x73, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x73, 0x53, 0x68, 0x6f, 0x77,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x69, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x12, 0x16,
	0x0a, 0x06, 0x71, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x71, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x41,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x41, 0x12, 0x16,
	0x0a, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x42, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x69, 0x6e, 0x70, 0x75, 0x74, 0x42, 0x22, 0x17, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x23, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f,
	0x72, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x14, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x53,
	0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x22, 0x27, 0x0a, 0x15, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x42, 0x79, 0x49, 0x64,
	0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x22, 0x4b, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x70,
	0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x31, 0x0a,
	0x0b, 0x75, 0x73, 0x65, 0x72, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x70, 0x6f, 0x6e,
	0x73, 0x6f, 0x72, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72,
	0x22, 0xfc, 0x02, 0x0a, 0x14, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x53,
	0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12,
	0x16, 0x0a, 0x06, 0x69, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x69, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x12, 0x16, 0x0a, 0x06, 0x71, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x71, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x41, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x41, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74,
	0x42, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x42, 0x12,
	0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22,
	0x4a, 0x0a, 0x15, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x53, 0x70, 0x6f,
	0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72,
	0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x70, 0x62, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x0b,
	0x75, 0x73, 0x65, 0x72, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x22, 0x22, 0x0a, 0x10, 0x53,
	0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22,
	0x8f, 0x02, 0x0a, 0x11, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x69, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x12, 0x16, 0x0a, 0x06, 0x71, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x71, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x41, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x41, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6e, 0x70,
	0x75, 0x74, 0x42, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74,
	0x42, 0x22, 0x8d, 0x01, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69,
	0x63, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x79, 0x6e,
	0x61, 0x6d, 0x69, 0x63, 0x55, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d,
	0x61, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72,
	0x6b, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x22, 0x63, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e, 0x61,
	0x6d, 0x69, 0x63, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1e,
	0x0a, 0x0a, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x55, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x55, 0x72, 0x6c, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x22, 0x24, 0x0a, 0x12, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65,
	0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x52, 0x65, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x49, 0x64, 0x22, 0x76, 0x0a, 0x14,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69,
	0x63, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a,
	0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x55, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x6d, 0x61, 0x72, 0x6b, 0x22, 0x17, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x52, 0x65, 0x73, 0x70, 0x22, 0x3b, 0x0a,
	0x11, 0x44, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x52,
	0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x14, 0x0a, 0x12, 0x44, 0x65,
	0x6c, 0x55, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x27, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d,
	0x69, 0x63, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x4b, 0x0a, 0x16, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x42, 0x79, 0x49, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d,
	0x69, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x44,
	0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x22, 0x33, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x4f, 0x0a, 0x1a, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x42, 0x79, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x0b, 0x75, 0x73, 0x65,
	0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f,
	0x2e, 0x70, 0x62, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x52,
	0x0b, 0x75, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x22, 0xc0, 0x01, 0x0a,
	0x14, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d,
	0x69, 0x63, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x79, 0x6e, 0x61, 0x6d,
	0x69, 0x63, 0x55, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x79, 0x6e,
	0x61, 0x6d, 0x69, 0x63, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72,
	0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12,
	0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22,
	0x4a, 0x0a, 0x15, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e,
	0x61, 0x6d, 0x69, 0x63, 0x52, 0x65, 0x73, 0x70, 0x12, 0x31, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72,
	0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x70, 0x62, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x52, 0x0b,
	0x75, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x32, 0x91, 0x19, 0x0a, 0x0a,
	0x75, 0x73, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x05, 0x6c, 0x6f,
	0x67, 0x69, 0x6e, 0x12, 0x0c, 0x2e, 0x70, 0x62, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65,
	0x71, 0x1a, 0x0d, 0x2e, 0x70, 0x62, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x2d, 0x0a, 0x08, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x0f, 0x2e, 0x70,
	0x62, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e,
	0x70, 0x62, 0x2e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x36, 0x0a, 0x0b, 0x67, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12,
	0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x71, 0x1a, 0x13, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x12, 0x51, 0x0a, 0x14, 0x67, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x42, 0x79, 0x41, 0x75, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x12,
	0x1b, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68,
	0x42, 0x79, 0x41, 0x75, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x70,
	0x62, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x42, 0x79, 0x41,
	0x75, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4d, 0x0a, 0x13, 0x67, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x1a, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75,
	0x74, 0x68, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e,
	0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x79, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3c, 0x0a, 0x0d, 0x67, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x14, 0x2e, 0x70, 0x62, 0x2e,
	0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71,
	0x1a, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4b, 0x0a, 0x12, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x2e,
	0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x42, 0x61, 0x73,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x2d, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x41, 0x64, 0x6d, 0x69, 0x6e,
	0x12, 0x0f, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x74, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x52, 0x65,
	0x71, 0x1a, 0x10, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x74, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x51, 0x0a, 0x14, 0x67, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x1b, 0x2e, 0x70, 0x62,
	0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2a, 0x0a, 0x07, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65,
	0x72, 0x12, 0x0e, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x1a, 0x0f, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x33, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x12, 0x11, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2a, 0x0a, 0x07, 0x44, 0x65, 0x6c, 0x55, 0x73,
	0x65, 0x72, 0x12, 0x0e, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x1a, 0x0f, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x36, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x79,
	0x49, 0x64, 0x12, 0x12, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42,
	0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x33, 0x0a, 0x0a, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x12, 0x11, 0x2e, 0x70, 0x62, 0x2e, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x70,
	0x62, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x39, 0x0a, 0x0c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x73, 0x41, 0x64, 0x6d, 0x69, 0x6e,
	0x12, 0x13, 0x2e, 0x70, 0x62, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x73, 0x41, 0x64, 0x6d,
	0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x70, 0x62, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x49, 0x73, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x0e, 0x57,
	0x78, 0x4d, 0x69, 0x6e, 0x69, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x12, 0x15, 0x2e,
	0x70, 0x62, 0x2e, 0x57, 0x58, 0x4d, 0x69, 0x6e, 0x69, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x70, 0x62, 0x2e, 0x57, 0x58, 0x4d, 0x69, 0x6e, 0x69,
	0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x0e,
	0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x15,
	0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x55, 0x73,
	0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x48, 0x0a,
	0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x18, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x70,
	0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x55, 0x73,
	0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x44,
	0x65, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x16, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4b, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x79, 0x49, 0x64, 0x12, 0x19,
	0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x70, 0x62, 0x2e, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x79, 0x49,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x48, 0x0a, 0x11, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55,
	0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x18, 0x2e, 0x70, 0x62, 0x2e,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x55, 0x73, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x36, 0x0a, 0x0b, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x12, 0x12,
	0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x52,
	0x65, 0x71, 0x1a, 0x13, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x41,
	0x75, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x12, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71,
	0x1a, 0x16, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x12, 0x36, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x55,
	0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x12, 0x12, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c,
	0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x70, 0x62,
	0x2e, 0x44, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x42, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x42,
	0x79, 0x49, 0x64, 0x12, 0x16, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x41, 0x75, 0x74, 0x68, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x70, 0x62,
	0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x42, 0x79, 0x49, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x0e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73,
	0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x12, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e,
	0x70, 0x62, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x41, 0x75, 0x74,
	0x68, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64,
	0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16,
	0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x42, 0x0a, 0x0f, 0x45, 0x64, 0x69, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x16, 0x2e, 0x70, 0x62, 0x2e, 0x45,
	0x64, 0x69, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x17, 0x2e, 0x70, 0x62, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x48, 0x0a, 0x11, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12,
	0x18, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x70, 0x62, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c, 0x55,
	0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e,
	0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4b, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x79, 0x49, 0x64, 0x12, 0x19, 0x2e, 0x70, 0x62,
	0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42,
	0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x48, 0x0a, 0x11, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x18, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x19, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x36, 0x0a, 0x0b,
	0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x53, 0x68, 0x6f, 0x70, 0x12, 0x12, 0x2e, 0x70, 0x62,
	0x2e, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x53, 0x68, 0x6f, 0x70, 0x52, 0x65, 0x71, 0x1a,
	0x13, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x53, 0x68, 0x6f, 0x70,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x53, 0x68, 0x6f, 0x70, 0x12, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x53, 0x68, 0x6f, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e,
	0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x53, 0x68, 0x6f,
	0x70, 0x52, 0x65, 0x73, 0x70, 0x12, 0x36, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x72,
	0x53, 0x68, 0x6f, 0x70, 0x12, 0x12, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c, 0x55, 0x73, 0x65,
	0x72, 0x53, 0x68, 0x6f, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65,
	0x6c, 0x55, 0x73, 0x65, 0x72, 0x53, 0x68, 0x6f, 0x70, 0x52, 0x65, 0x73, 0x70, 0x12, 0x42, 0x0a,
	0x0f, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x68, 0x6f, 0x70, 0x42, 0x79, 0x49, 0x64,
	0x12, 0x16, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x68, 0x6f,
	0x70, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x68, 0x6f, 0x70, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x3f, 0x0a, 0x0e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x53,
	0x68, 0x6f, 0x70, 0x12, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55,
	0x73, 0x65, 0x72, 0x53, 0x68, 0x6f, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x70, 0x62, 0x2e,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x53, 0x68, 0x6f, 0x70, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x3f, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x53, 0x70, 0x6f,
	0x6e, 0x73, 0x6f, 0x72, 0x12, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65,
	0x72, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x70, 0x62,
	0x2e, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x48, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x12, 0x18, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x52,
	0x65, 0x71, 0x1a, 0x19, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3f, 0x0a,
	0x0e, 0x44, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x12,
	0x15, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x53, 0x70, 0x6f, 0x6e,
	0x73, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c, 0x55,
	0x73, 0x65, 0x72, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4b,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72,
	0x42, 0x79, 0x49, 0x64, 0x12, 0x19, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x1a,
	0x1a, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x70, 0x6f, 0x6e,
	0x73, 0x6f, 0x72, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x48, 0x0a, 0x11, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72,
	0x12, 0x18, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72,
	0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x70, 0x62, 0x2e,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3c, 0x0a, 0x0d, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x14, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x70, 0x6f, 0x6e,
	0x73, 0x6f, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x70,
	0x62, 0x2e, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x44, 0x79,
	0x6e, 0x61, 0x6d, 0x69, 0x63, 0x12, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x55, 0x73,
	0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x70,
	0x62, 0x2e, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x48, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x12, 0x18, 0x2e, 0x70, 0x62, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63,
	0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3f,
	0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63,
	0x12, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e,
	0x61, 0x6d, 0x69, 0x63, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c,
	0x55, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x4b, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69,
	0x63, 0x42, 0x79, 0x49, 0x64, 0x12, 0x19, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71,
	0x1a, 0x1a, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e,
	0x61, 0x6d, 0x69, 0x63, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x48, 0x0a, 0x11,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69,
	0x63, 0x12, 0x18, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65,
	0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x70, 0x62,
	0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d,
	0x69, 0x63, 0x52, 0x65, 0x73, 0x70, 0x12, 0x57, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x1d, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e,
	0x61, 0x6d, 0x69, 0x63, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71, 0x1a,
	0x1e, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x79, 0x6e, 0x61,
	0x6d, 0x69, 0x63, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x42,
	0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_usercenter_proto_rawDescOnce sync.Once
	file_usercenter_proto_rawDescData = file_usercenter_proto_rawDesc
)

func file_usercenter_proto_rawDescGZIP() []byte {
	file_usercenter_proto_rawDescOnce.Do(func() {
		file_usercenter_proto_rawDescData = protoimpl.X.CompressGZIP(file_usercenter_proto_rawDescData)
	})
	return file_usercenter_proto_rawDescData
}

var file_usercenter_proto_msgTypes = make([]protoimpl.MessageInfo, 106)
var file_usercenter_proto_goTypes = []interface{}{
	(*User)(nil),                       // 0: pb.User
	(*AddUserReq)(nil),                 // 1: pb.AddUserReq
	(*AddUserResp)(nil),                // 2: pb.AddUserResp
	(*UpdateUserReq)(nil),              // 3: pb.UpdateUserReq
	(*UpdateUserResp)(nil),             // 4: pb.UpdateUserResp
	(*DelUserReq)(nil),                 // 5: pb.DelUserReq
	(*DelUserResp)(nil),                // 6: pb.DelUserResp
	(*GetUserByIdReq)(nil),             // 7: pb.GetUserByIdReq
	(*GetUserByIdResp)(nil),            // 8: pb.GetUserByIdResp
	(*SearchUserReq)(nil),              // 9: pb.SearchUserReq
	(*SearchUserResp)(nil),             // 10: pb.SearchUserResp
	(*UserAddress)(nil),                // 11: pb.UserAddress
	(*AddUserAddressReq)(nil),          // 12: pb.AddUserAddressReq
	(*AddUserAddressResp)(nil),         // 13: pb.AddUserAddressResp
	(*UpdateUserAddressReq)(nil),       // 14: pb.UpdateUserAddressReq
	(*UpdateUserAddressResp)(nil),      // 15: pb.UpdateUserAddressResp
	(*DelUserAddressReq)(nil),          // 16: pb.DelUserAddressReq
	(*DelUserAddressResp)(nil),         // 17: pb.DelUserAddressResp
	(*GetUserAddressByIdReq)(nil),      // 18: pb.GetUserAddressByIdReq
	(*GetUserAddressByIdResp)(nil),     // 19: pb.GetUserAddressByIdResp
	(*SearchUserAddressReq)(nil),       // 20: pb.SearchUserAddressReq
	(*SearchUserAddressResp)(nil),      // 21: pb.SearchUserAddressResp
	(*UserAuth)(nil),                   // 22: pb.UserAuth
	(*AddUserAuthReq)(nil),             // 23: pb.AddUserAuthReq
	(*AddUserAuthResp)(nil),            // 24: pb.AddUserAuthResp
	(*UpdateUserAuthReq)(nil),          // 25: pb.UpdateUserAuthReq
	(*UpdateUserAuthResp)(nil),         // 26: pb.UpdateUserAuthResp
	(*DelUserAuthReq)(nil),             // 27: pb.DelUserAuthReq
	(*DelUserAuthResp)(nil),            // 28: pb.DelUserAuthResp
	(*GetUserAuthByIdReq)(nil),         // 29: pb.GetUserAuthByIdReq
	(*GetUserAuthByIdResp)(nil),        // 30: pb.GetUserAuthByIdResp
	(*SearchUserAuthReq)(nil),          // 31: pb.SearchUserAuthReq
	(*SearchUserAuthResp)(nil),         // 32: pb.SearchUserAuthResp
	(*UserInfoForComment)(nil),         // 33: pb.UserInfoForComment
	(*GetUserInfoByUserIdsReq)(nil),    // 34: pb.GetUserInfoByUserIdsReq
	(*GetUserInfoByUserIdsResp)(nil),   // 35: pb.GetUserInfoByUserIdsResp
	(*UserContact)(nil),                // 36: pb.UserContact
	(*AddUserContactReq)(nil),          // 37: pb.AddUserContactReq
	(*AddUserContactResp)(nil),         // 38: pb.AddUserContactResp
	(*EditUserContactReq)(nil),         // 39: pb.EditUserContactReq
	(*EditUserContactResp)(nil),        // 40: pb.EditUserContactResp
	(*UpdateUserContactReq)(nil),       // 41: pb.UpdateUserContactReq
	(*UpdateUserContactResp)(nil),      // 42: pb.UpdateUserContactResp
	(*DelUserContactReq)(nil),          // 43: pb.DelUserContactReq
	(*DelUserContactResp)(nil),         // 44: pb.DelUserContactResp
	(*GetUserContactByIdReq)(nil),      // 45: pb.GetUserContactByIdReq
	(*GetUserContactByIdResp)(nil),     // 46: pb.GetUserContactByIdResp
	(*SearchUserContactReq)(nil),       // 47: pb.SearchUserContactReq
	(*SearchUserContactResp)(nil),      // 48: pb.SearchUserContactResp
	(*UserShop)(nil),                   // 49: pb.UserShop
	(*AddUserShopReq)(nil),             // 50: pb.AddUserShopReq
	(*AddUserShopResp)(nil),            // 51: pb.AddUserShopResp
	(*UpdateUserShopReq)(nil),          // 52: pb.UpdateUserShopReq
	(*UpdateUserShopResp)(nil),         // 53: pb.UpdateUserShopResp
	(*DelUserShopReq)(nil),             // 54: pb.DelUserShopReq
	(*DelUserShopResp)(nil),            // 55: pb.DelUserShopResp
	(*GetUserShopByIdReq)(nil),         // 56: pb.GetUserShopByIdReq
	(*GetUserShopByIdResp)(nil),        // 57: pb.GetUserShopByIdResp
	(*SearchUserShopReq)(nil),          // 58: pb.SearchUserShopReq
	(*SearchUserShopResp)(nil),         // 59: pb.SearchUserShopResp
	(*CheckIsAdminReq)(nil),            // 60: pb.CheckIsAdminReq
	(*CheckIsAdminResp)(nil),           // 61: pb.CheckIsAdminResp
	(*WXMiniRegisterReq)(nil),          // 62: pb.WXMiniRegisterReq
	(*WXMiniRegisterResp)(nil),         // 63: pb.WXMiniRegisterResp
	(*LoginReq)(nil),                   // 64: pb.LoginReq
	(*LoginResp)(nil),                  // 65: pb.LoginResp
	(*GetUserInfoReq)(nil),             // 66: pb.GetUserInfoReq
	(*GetUserInfoResp)(nil),            // 67: pb.GetUserInfoResp
	(*GetUserAuthByAuthKeyReq)(nil),    // 68: pb.GetUserAuthByAuthKeyReq
	(*GetUserAuthByAuthKeyResp)(nil),   // 69: pb.GetUserAuthByAuthKeyResp
	(*GetUserAuthByUserIdReq)(nil),     // 70: pb.GetUserAuthByUserIdReq
	(*GetUserAuthyUserIdResp)(nil),     // 71: pb.GetUserAuthyUserIdResp
	(*GenerateTokenReq)(nil),           // 72: pb.GenerateTokenReq
	(*GenerateTokenResp)(nil),          // 73: pb.GenerateTokenResp
	(*RegisterReq)(nil),                // 74: pb.RegisterReq
	(*RegisterResp)(nil),               // 75: pb.RegisterResp
	(*UpdateUserBaseInfoReq)(nil),      // 76: pb.UpdateUserBaseInfoReq
	(*UpdateUserBaseInfoResp)(nil),     // 77: pb.UpdateUserBaseInfoResp
	(*SetAdminReq)(nil),                // 78: pb.SetAdminReq
	(*SetAdminResp)(nil),               // 79: pb.SetAdminResp
	(*UserSponsor)(nil),                // 80: pb.UserSponsor
	(*AddUserSponsorReq)(nil),          // 81: pb.AddUserSponsorReq
	(*AddUserSponsorResp)(nil),         // 82: pb.AddUserSponsorResp
	(*UpdateUserSponsorReq)(nil),       // 83: pb.UpdateUserSponsorReq
	(*UpdateUserSponsorResp)(nil),      // 84: pb.UpdateUserSponsorResp
	(*DelUserSponsorReq)(nil),          // 85: pb.DelUserSponsorReq
	(*DelUserSponsorResp)(nil),         // 86: pb.DelUserSponsorResp
	(*GetUserSponsorByIdReq)(nil),      // 87: pb.GetUserSponsorByIdReq
	(*GetUserSponsorByIdResp)(nil),     // 88: pb.GetUserSponsorByIdResp
	(*SearchUserSponsorReq)(nil),       // 89: pb.SearchUserSponsorReq
	(*SearchUserSponsorResp)(nil),      // 90: pb.SearchUserSponsorResp
	(*SponsorDetailReq)(nil),           // 91: pb.SponsorDetailReq
	(*SponsorDetailResp)(nil),          // 92: pb.SponsorDetailResp
	(*UserDynamic)(nil),                // 93: pb.UserDynamic
	(*AddUserDynamicReq)(nil),          // 94: pb.AddUserDynamicReq
	(*AddUserDynamicResp)(nil),         // 95: pb.AddUserDynamicResp
	(*UpdateUserDynamicReq)(nil),       // 96: pb.UpdateUserDynamicReq
	(*UpdateUserDynamicResp)(nil),      // 97: pb.UpdateUserDynamicResp
	(*DelUserDynamicReq)(nil),          // 98: pb.DelUserDynamicReq
	(*DelUserDynamicResp)(nil),         // 99: pb.DelUserDynamicResp
	(*GetUserDynamicByIdReq)(nil),      // 100: pb.GetUserDynamicByIdReq
	(*GetUserDynamicByIdResp)(nil),     // 101: pb.GetUserDynamicByIdResp
	(*GetUserDynamicByUserIdReq)(nil),  // 102: pb.GetUserDynamicByUserIdReq
	(*GetUserDynamicByUserIdResp)(nil), // 103: pb.GetUserDynamicByUserIdResp
	(*SearchUserDynamicReq)(nil),       // 104: pb.SearchUserDynamicReq
	(*SearchUserDynamicResp)(nil),      // 105: pb.SearchUserDynamicResp
}
var file_usercenter_proto_depIdxs = []int32{
	0,   // 0: pb.GetUserByIdResp.user:type_name -> pb.User
	0,   // 1: pb.SearchUserResp.user:type_name -> pb.User
	11,  // 2: pb.GetUserAddressByIdResp.userAddress:type_name -> pb.UserAddress
	11,  // 3: pb.SearchUserAddressResp.userAddress:type_name -> pb.UserAddress
	22,  // 4: pb.GetUserAuthByIdResp.userAuth:type_name -> pb.UserAuth
	22,  // 5: pb.SearchUserAuthResp.userAuth:type_name -> pb.UserAuth
	33,  // 6: pb.GetUserInfoByUserIdsResp.userInfo:type_name -> pb.UserInfoForComment
	36,  // 7: pb.GetUserContactByIdResp.userContact:type_name -> pb.UserContact
	36,  // 8: pb.SearchUserContactResp.userContact:type_name -> pb.UserContact
	49,  // 9: pb.GetUserShopByIdResp.userShop:type_name -> pb.UserShop
	49,  // 10: pb.SearchUserShopResp.userShop:type_name -> pb.UserShop
	0,   // 11: pb.GetUserInfoResp.user:type_name -> pb.User
	22,  // 12: pb.GetUserAuthByAuthKeyResp.userAuth:type_name -> pb.UserAuth
	22,  // 13: pb.GetUserAuthyUserIdResp.userAuth:type_name -> pb.UserAuth
	80,  // 14: pb.GetUserSponsorByIdResp.userSponsor:type_name -> pb.UserSponsor
	80,  // 15: pb.SearchUserSponsorResp.userSponsor:type_name -> pb.UserSponsor
	93,  // 16: pb.GetUserDynamicByIdResp.userDynamic:type_name -> pb.UserDynamic
	93,  // 17: pb.GetUserDynamicByUserIdResp.userDynamic:type_name -> pb.UserDynamic
	93,  // 18: pb.SearchUserDynamicResp.userDynamic:type_name -> pb.UserDynamic
	64,  // 19: pb.usercenter.login:input_type -> pb.LoginReq
	74,  // 20: pb.usercenter.register:input_type -> pb.RegisterReq
	66,  // 21: pb.usercenter.getUserInfo:input_type -> pb.GetUserInfoReq
	68,  // 22: pb.usercenter.getUserAuthByAuthKey:input_type -> pb.GetUserAuthByAuthKeyReq
	70,  // 23: pb.usercenter.getUserAuthByUserId:input_type -> pb.GetUserAuthByUserIdReq
	72,  // 24: pb.usercenter.generateToken:input_type -> pb.GenerateTokenReq
	76,  // 25: pb.usercenter.updateUserBaseInfo:input_type -> pb.UpdateUserBaseInfoReq
	78,  // 26: pb.usercenter.setAdmin:input_type -> pb.SetAdminReq
	34,  // 27: pb.usercenter.getUserInfoByUserIds:input_type -> pb.GetUserInfoByUserIdsReq
	1,   // 28: pb.usercenter.AddUser:input_type -> pb.AddUserReq
	3,   // 29: pb.usercenter.UpdateUser:input_type -> pb.UpdateUserReq
	5,   // 30: pb.usercenter.DelUser:input_type -> pb.DelUserReq
	7,   // 31: pb.usercenter.GetUserById:input_type -> pb.GetUserByIdReq
	9,   // 32: pb.usercenter.SearchUser:input_type -> pb.SearchUserReq
	60,  // 33: pb.usercenter.CheckIsAdmin:input_type -> pb.CheckIsAdminReq
	62,  // 34: pb.usercenter.WxMiniRegister:input_type -> pb.WXMiniRegisterReq
	12,  // 35: pb.usercenter.AddUserAddress:input_type -> pb.AddUserAddressReq
	14,  // 36: pb.usercenter.UpdateUserAddress:input_type -> pb.UpdateUserAddressReq
	16,  // 37: pb.usercenter.DelUserAddress:input_type -> pb.DelUserAddressReq
	18,  // 38: pb.usercenter.GetUserAddressById:input_type -> pb.GetUserAddressByIdReq
	20,  // 39: pb.usercenter.SearchUserAddress:input_type -> pb.SearchUserAddressReq
	23,  // 40: pb.usercenter.AddUserAuth:input_type -> pb.AddUserAuthReq
	25,  // 41: pb.usercenter.UpdateUserAuth:input_type -> pb.UpdateUserAuthReq
	27,  // 42: pb.usercenter.DelUserAuth:input_type -> pb.DelUserAuthReq
	29,  // 43: pb.usercenter.GetUserAuthById:input_type -> pb.GetUserAuthByIdReq
	31,  // 44: pb.usercenter.SearchUserAuth:input_type -> pb.SearchUserAuthReq
	37,  // 45: pb.usercenter.AddUserContact:input_type -> pb.AddUserContactReq
	39,  // 46: pb.usercenter.EditUserContact:input_type -> pb.EditUserContactReq
	41,  // 47: pb.usercenter.UpdateUserContact:input_type -> pb.UpdateUserContactReq
	43,  // 48: pb.usercenter.DelUserContact:input_type -> pb.DelUserContactReq
	45,  // 49: pb.usercenter.GetUserContactById:input_type -> pb.GetUserContactByIdReq
	47,  // 50: pb.usercenter.SearchUserContact:input_type -> pb.SearchUserContactReq
	50,  // 51: pb.usercenter.AddUserShop:input_type -> pb.AddUserShopReq
	52,  // 52: pb.usercenter.UpdateUserShop:input_type -> pb.UpdateUserShopReq
	54,  // 53: pb.usercenter.DelUserShop:input_type -> pb.DelUserShopReq
	56,  // 54: pb.usercenter.GetUserShopById:input_type -> pb.GetUserShopByIdReq
	58,  // 55: pb.usercenter.SearchUserShop:input_type -> pb.SearchUserShopReq
	81,  // 56: pb.usercenter.AddUserSponsor:input_type -> pb.AddUserSponsorReq
	83,  // 57: pb.usercenter.UpdateUserSponsor:input_type -> pb.UpdateUserSponsorReq
	85,  // 58: pb.usercenter.DelUserSponsor:input_type -> pb.DelUserSponsorReq
	87,  // 59: pb.usercenter.GetUserSponsorById:input_type -> pb.GetUserSponsorByIdReq
	89,  // 60: pb.usercenter.SearchUserSponsor:input_type -> pb.SearchUserSponsorReq
	91,  // 61: pb.usercenter.SponsorDetail:input_type -> pb.SponsorDetailReq
	94,  // 62: pb.usercenter.AddUserDynamic:input_type -> pb.AddUserDynamicReq
	96,  // 63: pb.usercenter.UpdateUserDynamic:input_type -> pb.UpdateUserDynamicReq
	98,  // 64: pb.usercenter.DelUserDynamic:input_type -> pb.DelUserDynamicReq
	100, // 65: pb.usercenter.GetUserDynamicById:input_type -> pb.GetUserDynamicByIdReq
	104, // 66: pb.usercenter.SearchUserDynamic:input_type -> pb.SearchUserDynamicReq
	102, // 67: pb.usercenter.GetUserDynamicByUserId:input_type -> pb.GetUserDynamicByUserIdReq
	65,  // 68: pb.usercenter.login:output_type -> pb.LoginResp
	75,  // 69: pb.usercenter.register:output_type -> pb.RegisterResp
	67,  // 70: pb.usercenter.getUserInfo:output_type -> pb.GetUserInfoResp
	69,  // 71: pb.usercenter.getUserAuthByAuthKey:output_type -> pb.GetUserAuthByAuthKeyResp
	71,  // 72: pb.usercenter.getUserAuthByUserId:output_type -> pb.GetUserAuthyUserIdResp
	73,  // 73: pb.usercenter.generateToken:output_type -> pb.GenerateTokenResp
	77,  // 74: pb.usercenter.updateUserBaseInfo:output_type -> pb.UpdateUserBaseInfoResp
	79,  // 75: pb.usercenter.setAdmin:output_type -> pb.SetAdminResp
	35,  // 76: pb.usercenter.getUserInfoByUserIds:output_type -> pb.GetUserInfoByUserIdsResp
	2,   // 77: pb.usercenter.AddUser:output_type -> pb.AddUserResp
	4,   // 78: pb.usercenter.UpdateUser:output_type -> pb.UpdateUserResp
	6,   // 79: pb.usercenter.DelUser:output_type -> pb.DelUserResp
	8,   // 80: pb.usercenter.GetUserById:output_type -> pb.GetUserByIdResp
	10,  // 81: pb.usercenter.SearchUser:output_type -> pb.SearchUserResp
	61,  // 82: pb.usercenter.CheckIsAdmin:output_type -> pb.CheckIsAdminResp
	63,  // 83: pb.usercenter.WxMiniRegister:output_type -> pb.WXMiniRegisterResp
	13,  // 84: pb.usercenter.AddUserAddress:output_type -> pb.AddUserAddressResp
	15,  // 85: pb.usercenter.UpdateUserAddress:output_type -> pb.UpdateUserAddressResp
	17,  // 86: pb.usercenter.DelUserAddress:output_type -> pb.DelUserAddressResp
	19,  // 87: pb.usercenter.GetUserAddressById:output_type -> pb.GetUserAddressByIdResp
	21,  // 88: pb.usercenter.SearchUserAddress:output_type -> pb.SearchUserAddressResp
	24,  // 89: pb.usercenter.AddUserAuth:output_type -> pb.AddUserAuthResp
	26,  // 90: pb.usercenter.UpdateUserAuth:output_type -> pb.UpdateUserAuthResp
	28,  // 91: pb.usercenter.DelUserAuth:output_type -> pb.DelUserAuthResp
	30,  // 92: pb.usercenter.GetUserAuthById:output_type -> pb.GetUserAuthByIdResp
	32,  // 93: pb.usercenter.SearchUserAuth:output_type -> pb.SearchUserAuthResp
	38,  // 94: pb.usercenter.AddUserContact:output_type -> pb.AddUserContactResp
	40,  // 95: pb.usercenter.EditUserContact:output_type -> pb.EditUserContactResp
	42,  // 96: pb.usercenter.UpdateUserContact:output_type -> pb.UpdateUserContactResp
	44,  // 97: pb.usercenter.DelUserContact:output_type -> pb.DelUserContactResp
	46,  // 98: pb.usercenter.GetUserContactById:output_type -> pb.GetUserContactByIdResp
	48,  // 99: pb.usercenter.SearchUserContact:output_type -> pb.SearchUserContactResp
	51,  // 100: pb.usercenter.AddUserShop:output_type -> pb.AddUserShopResp
	53,  // 101: pb.usercenter.UpdateUserShop:output_type -> pb.UpdateUserShopResp
	55,  // 102: pb.usercenter.DelUserShop:output_type -> pb.DelUserShopResp
	57,  // 103: pb.usercenter.GetUserShopById:output_type -> pb.GetUserShopByIdResp
	59,  // 104: pb.usercenter.SearchUserShop:output_type -> pb.SearchUserShopResp
	82,  // 105: pb.usercenter.AddUserSponsor:output_type -> pb.AddUserSponsorResp
	84,  // 106: pb.usercenter.UpdateUserSponsor:output_type -> pb.UpdateUserSponsorResp
	86,  // 107: pb.usercenter.DelUserSponsor:output_type -> pb.DelUserSponsorResp
	88,  // 108: pb.usercenter.GetUserSponsorById:output_type -> pb.GetUserSponsorByIdResp
	90,  // 109: pb.usercenter.SearchUserSponsor:output_type -> pb.SearchUserSponsorResp
	92,  // 110: pb.usercenter.SponsorDetail:output_type -> pb.SponsorDetailResp
	95,  // 111: pb.usercenter.AddUserDynamic:output_type -> pb.AddUserDynamicResp
	97,  // 112: pb.usercenter.UpdateUserDynamic:output_type -> pb.UpdateUserDynamicResp
	99,  // 113: pb.usercenter.DelUserDynamic:output_type -> pb.DelUserDynamicResp
	101, // 114: pb.usercenter.GetUserDynamicById:output_type -> pb.GetUserDynamicByIdResp
	105, // 115: pb.usercenter.SearchUserDynamic:output_type -> pb.SearchUserDynamicResp
	103, // 116: pb.usercenter.GetUserDynamicByUserId:output_type -> pb.GetUserDynamicByUserIdResp
	68,  // [68:117] is the sub-list for method output_type
	19,  // [19:68] is the sub-list for method input_type
	19,  // [19:19] is the sub-list for extension type_name
	19,  // [19:19] is the sub-list for extension extendee
	0,   // [0:19] is the sub-list for field type_name
}

func init() { file_usercenter_proto_init() }
func file_usercenter_proto_init() {
	if File_usercenter_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_usercenter_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*User); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddUserReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddUserResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelUserReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelUserResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserByIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserByIdResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchUserReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchUserResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddUserAddressReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddUserAddressResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserAddressReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserAddressResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelUserAddressReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelUserAddressResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserAddressByIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserAddressByIdResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchUserAddressReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchUserAddressResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserAuth); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddUserAuthReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddUserAuthResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserAuthReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserAuthResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelUserAuthReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelUserAuthResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserAuthByIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserAuthByIdResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchUserAuthReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchUserAuthResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfoForComment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserInfoByUserIdsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserInfoByUserIdsResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserContact); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddUserContactReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddUserContactResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditUserContactReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditUserContactResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserContactReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserContactResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelUserContactReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelUserContactResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserContactByIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserContactByIdResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchUserContactReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchUserContactResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserShop); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddUserShopReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddUserShopResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserShopReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserShopResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelUserShopReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelUserShopResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserShopByIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserShopByIdResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchUserShopReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchUserShopResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckIsAdminReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckIsAdminResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WXMiniRegisterReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WXMiniRegisterResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserInfoResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserAuthByAuthKeyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserAuthByAuthKeyResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserAuthByUserIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserAuthyUserIdResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateTokenReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateTokenResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegisterReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegisterResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserBaseInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserBaseInfoResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetAdminReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetAdminResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserSponsor); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddUserSponsorReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddUserSponsorResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserSponsorReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserSponsorResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[85].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelUserSponsorReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[86].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelUserSponsorResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[87].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserSponsorByIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[88].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserSponsorByIdResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[89].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchUserSponsorReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[90].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchUserSponsorResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[91].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SponsorDetailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[92].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SponsorDetailResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[93].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserDynamic); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[94].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddUserDynamicReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[95].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddUserDynamicResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[96].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserDynamicReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[97].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserDynamicResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[98].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelUserDynamicReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[99].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelUserDynamicResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[100].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserDynamicByIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[101].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserDynamicByIdResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[102].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserDynamicByUserIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[103].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserDynamicByUserIdResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[104].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchUserDynamicReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_usercenter_proto_msgTypes[105].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchUserDynamicResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_usercenter_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   106,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_usercenter_proto_goTypes,
		DependencyIndexes: file_usercenter_proto_depIdxs,
		MessageInfos:      file_usercenter_proto_msgTypes,
	}.Build()
	File_usercenter_proto = out.File
	file_usercenter_proto_rawDesc = nil
	file_usercenter_proto_goTypes = nil
	file_usercenter_proto_depIdxs = nil
}
