Name: order-mq
Host: 0.0.0.0
Port: 3001
Mode: dev

#监控
Prometheus:
  Host: 0.0.0.0
  Port: 4003
  Path: /metrics

#链路追踪
Telemetry:
  Name: order-mq
  Endpoint: http://jaeger:14268/api/traces
  Sampler: 1.0
  Batcher: jaeger

Log:
  ServiceName: order-mq
  Level: error

Redis:
  Host: redis:6379
  Type: node
  Pass: G62m50oigInC30sf

#kq
PaymentUpdateStatusConf:
  Name: PaymentUpdateStatus
  Brokers:
    - kafka:9092
  Group: payment-update-paystatus-group
  Topic: payment-update-paystatus-topic
  Offset: first
  Consumers: 1
  Processors: 1

#rpc
OrderRpcConf:
  Endpoints:
    - 127.0.0.1:2001
  NonBlock: true

UsercenterRpcConf:
  Endpoints:
    - 127.0.0.1:2004
  NonBlock: true
