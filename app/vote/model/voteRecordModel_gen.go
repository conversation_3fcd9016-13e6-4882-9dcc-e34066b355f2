// Code generated by goctl. DO NOT EDIT.

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
	"looklook/common/globalkey"
)

var (
	voteRecordFieldNames          = builder.RawFieldNames(&VoteRecord{})
	voteRecordRows                = strings.Join(voteRecordFieldNames, ",")
	voteRecordRowsExpectAutoSet   = strings.Join(stringx.Remove(voteRecordFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), ",")
	voteRecordRowsWithPlaceHolder = strings.Join(stringx.Remove(voteRecordFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), "=?,") + "=?"

	cacheVoteVoteRecordIdPrefix = "cache:vote:voteRecord:id:"
)

type (
	voteRecordModel interface {
		Insert(ctx context.Context, data *VoteRecord) (sql.Result, error)
		TransInsert(ctx context.Context, session sqlx.Session, data *VoteRecord) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*VoteRecord, error)
		Update(ctx context.Context, data *VoteRecord) error
		List(ctx context.Context, page, limit int64) ([]*VoteRecord, error)
		TransUpdate(ctx context.Context, session sqlx.Session, data *VoteRecord) error
		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		SelectBuilder() squirrel.SelectBuilder
		FindSum(ctx context.Context, sumBuilder squirrel.SelectBuilder, field string) (float64, error)
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder, field string) (int64, error)
		FindAll(ctx context.Context, rowBuilder squirrel.SelectBuilder, orderBy string) ([]*VoteRecord, error)
		FindPageListByPage(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*VoteRecord, error)
		FindPageListByPageWithTotal(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*VoteRecord, int64, error)
		FindPageListByIdDESC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*VoteRecord, error)
		FindPageListByIdASC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*VoteRecord, error)
		Delete(ctx context.Context, id int64) error
	}

	defaultVoteRecordModel struct {
		sqlc.CachedConn
		table string
	}

	VoteRecord struct {
		Id             int64     `db:"id"`
		LotteryId      int64     `db:"lottery_id"`      // 抽奖ID
		UserId         int64     `db:"user_id"`         // 用户ID
		SelectedOption int64     `db:"selected_option"` // 用户选择的投票选项
		DelState       int64     `db:"del_state"`
		CreatedAt      time.Time `db:"created_at"` // 投票时间
	}
)

func newVoteRecordModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultVoteRecordModel {
	return &defaultVoteRecordModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`vote_record`",
	}
}

func (m *defaultVoteRecordModel) Delete(ctx context.Context, id int64) error {
	voteVoteRecordIdKey := fmt.Sprintf("%s%v", cacheVoteVoteRecordIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		return conn.ExecCtx(ctx, query, id)
	}, voteVoteRecordIdKey)
	return err
}

func (m *defaultVoteRecordModel) FindOne(ctx context.Context, id int64) (*VoteRecord, error) {
	voteVoteRecordIdKey := fmt.Sprintf("%s%v", cacheVoteVoteRecordIdPrefix, id)
	var resp VoteRecord
	err := m.QueryRowCtx(ctx, &resp, voteVoteRecordIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", voteRecordRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVoteRecordModel) Insert(ctx context.Context, data *VoteRecord) (sql.Result, error) {
	voteVoteRecordIdKey := fmt.Sprintf("%s%v", cacheVoteVoteRecordIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, voteRecordRowsExpectAutoSet)
		return conn.ExecCtx(ctx, query, data.LotteryId, data.UserId, data.SelectedOption, data.DelState)
	}, voteVoteRecordIdKey)
	return ret, err
}

func (m *defaultVoteRecordModel) TransInsert(ctx context.Context, session sqlx.Session, data *VoteRecord) (sql.Result, error) {
	voteVoteRecordIdKey := fmt.Sprintf("%s%v", cacheVoteVoteRecordIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, voteRecordRowsExpectAutoSet)
		return session.ExecCtx(ctx, query, data.LotteryId, data.UserId, data.SelectedOption, data.DelState)
	}, voteVoteRecordIdKey)
	return ret, err
}
func (m *defaultVoteRecordModel) Update(ctx context.Context, data *VoteRecord) error {
	voteVoteRecordIdKey := fmt.Sprintf("%s%v", cacheVoteVoteRecordIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, voteRecordRowsWithPlaceHolder)
		return conn.ExecCtx(ctx, query, data.LotteryId, data.UserId, data.SelectedOption, data.DelState, data.Id)
	}, voteVoteRecordIdKey)
	return err
}

func (m *defaultVoteRecordModel) TransUpdate(ctx context.Context, session sqlx.Session, data *VoteRecord) error {
	voteVoteRecordIdKey := fmt.Sprintf("%s%v", cacheVoteVoteRecordIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, voteRecordRowsWithPlaceHolder)
		return session.ExecCtx(ctx, query, data.LotteryId, data.UserId, data.SelectedOption, data.DelState, data.Id)
	}, voteVoteRecordIdKey)
	return err
}

func (m *defaultVoteRecordModel) List(ctx context.Context, page, limit int64) ([]*VoteRecord, error) {
	query := fmt.Sprintf("select %s from %s limit ?,?", voteRecordRows, m.table)
	var resp []*VoteRecord
	//err := m.conn.QueryRowsCtx(ctx, &resp, query, (page-1)*limit, limit)
	err := m.QueryRowsNoCacheCtx(ctx, &resp, query, (page-1)*limit, limit)
	return resp, err
}

func (m *defaultVoteRecordModel) Trans(ctx context.Context, fn func(ctx context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, func(ctx context.Context, session sqlx.Session) error {
		return fn(ctx, session)
	})
}

func (m *defaultVoteRecordModel) FindSum(ctx context.Context, builder squirrel.SelectBuilder, field string) (float64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindSum Least One Field"), "FindSum Least One Field")
	}

	builder = builder.Columns("IFNULL(SUM(" + field + "),0)")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp float64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultVoteRecordModel) FindCount(ctx context.Context, builder squirrel.SelectBuilder, field string) (int64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindCount Least One Field"), "FindCount Least One Field")
	}

	builder = builder.Columns("COUNT(" + field + ")")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultVoteRecordModel) FindAll(ctx context.Context, builder squirrel.SelectBuilder, orderBy string) ([]*VoteRecord, error) {

	builder = builder.Columns(voteRecordRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*VoteRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultVoteRecordModel) FindPageListByPage(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*VoteRecord, error) {

	builder = builder.Columns(voteRecordRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*VoteRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultVoteRecordModel) FindPageListByPageWithTotal(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*VoteRecord, int64, error) {

	total, err := m.FindCount(ctx, builder, "id")
	if err != nil {
		return nil, 0, err
	}

	builder = builder.Columns(voteRecordRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, total, err
	}

	var resp []*VoteRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, total, nil
	default:
		return nil, total, err
	}
}

func (m *defaultVoteRecordModel) FindPageListByIdDESC(ctx context.Context, builder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*VoteRecord, error) {

	builder = builder.Columns(voteRecordRows)

	if preMinId > 0 {
		builder = builder.Where(" id < ? ", preMinId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id DESC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*VoteRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultVoteRecordModel) FindPageListByIdASC(ctx context.Context, builder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*VoteRecord, error) {

	builder = builder.Columns(voteRecordRows)

	if preMaxId > 0 {
		builder = builder.Where(" id > ? ", preMaxId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id ASC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*VoteRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultVoteRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select().From(m.table)
}

func (m *defaultVoteRecordModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheVoteVoteRecordIdPrefix, primary)
}

func (m *defaultVoteRecordModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", voteRecordRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultVoteRecordModel) tableName() string {
	return m.table
}
