Name: payment-rpc
ListenOn: 0.0.0.0:2002
Mode: dev


#监控
Prometheus:
  Host: 0.0.0.0
  Port: 4005
  Path: /metrics

#链路追踪
Telemetry:
  Name: payment-rpc
  Endpoint: http://jaeger:14268/api/traces
  Sampler: 1.0
  Batcher: jaeger

Log:
  ServiceName: payment-rpc
  Level: error

Redis:
  Host: redis:6379
  Type: node
  Pass: G62m50oigInC30sf
  Key: payment-rpc
DB:
  DataSource: root:PXDN93VRKUm8TeE7@tcp(mysql:3306)/lottery?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai
Cache:
  - Host: redis:6379
    Pass: G62m50oigInC30sf

#pay success notify order-mq for kq(kafka pub sub)
KqPaymentUpdatePayStatusConf:
  Brokers:
    - kafka:9092
  Topic: payment-update-paystatus-topic





