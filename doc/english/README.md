# Doumentation

For better experience please follow the documentation in the order of apearance:

- [I-Development environment setup](01-Development-environment-setup.md)
- [II-Nginx gateway](02-Nginx-gateway.md)
- [III-Authenthication services](03-Authenthication-services.md)
- [IV-User service](04-User-service.md)
- [V-Travel service](05-Travel-service.md)
- [VI-Order service](06-Order-service.md)
- [VII-Payment service](07-Payment-service.md)
- [VIII-message delay cron-queue](08-message-delay-cron-queue.md)
- [IX-Distributed transactions](09-Distributed-transactions.md)
- [X-Error handling](10-Error-handling.md)
- [XI-Log collection](11-Log-collection.md)
- [XII-Link tracking](12-Link-tracking.md)
- [XIII-Service monitoring](13-Service-monitoring.md)
- [XIV-Deployment environment setup](14-Deployment-environment-setup.md)
- [XV-Publishing services to Kubernetes](15-Publishing-services-to-k8s.md)
