package config

import (
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/zrpc"
)

type Config struct {
	zrpc.RpcServerConf
	JwtAuth struct {
		AccessSecret string
		AccessExpire int64
	}
	DB struct {
		DataSource string
	}
	Cache cache.CacheConf
	Oss   struct {
		Endpoint        string
		AccessKeyId     string
		AccessKeySecret string
		FilePathPrefix  string
		BucketName      string
	}
}
