// Code generated by goctl. DO NOT EDIT.
// Source: notice.proto

package notice

import (
	"context"

	"looklook/app/notice/cmd/rpc/pb"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	GetNoticeSubscribePreferenceReq   = pb.GetNoticeSubscribePreferenceReq
	GetNoticeSubscribePreferenceResp  = pb.GetNoticeSubscribePreferenceResp
	NoticeLotteryDrawReq              = pb.NoticeLotteryDrawReq
	NoticeLotteryDrawResp             = pb.NoticeLotteryDrawResp
	NoticeLotteryStartReq             = pb.NoticeLotteryStartReq
	NoticeLotteryStartResp            = pb.NoticeLotteryStartResp
	NoticeSubscribePreference         = pb.NoticeSubscribePreference
	NoticeWishSignInReq               = pb.NoticeWishSignInReq
	NoticeWishSignInResp              = pb.NoticeWishSignInResp
	SaveNoticeSubscribePreferenceReq  = pb.SaveNoticeSubscribePreferenceReq
	SaveNoticeSubscribePreferenceResp = pb.SaveNoticeSubscribePreferenceResp

	Notice interface {
		NoticeLotteryDraw(ctx context.Context, in *NoticeLotteryDrawReq, opts ...grpc.CallOption) (*NoticeLotteryDrawResp, error)
		GetNoticeSubscribePreference(ctx context.Context, in *GetNoticeSubscribePreferenceReq, opts ...grpc.CallOption) (*GetNoticeSubscribePreferenceResp, error)
		SaveNoticeSubscribePreference(ctx context.Context, in *SaveNoticeSubscribePreferenceReq, opts ...grpc.CallOption) (*SaveNoticeSubscribePreferenceResp, error)
		NoticeLotteryStart(ctx context.Context, in *NoticeLotteryStartReq, opts ...grpc.CallOption) (*NoticeLotteryStartResp, error)
		NoticeWishSign(ctx context.Context, in *NoticeWishSignInReq, opts ...grpc.CallOption) (*NoticeWishSignInResp, error)
	}

	defaultNotice struct {
		cli zrpc.Client
	}
)

func NewNotice(cli zrpc.Client) Notice {
	return &defaultNotice{
		cli: cli,
	}
}

func (m *defaultNotice) NoticeLotteryDraw(ctx context.Context, in *NoticeLotteryDrawReq, opts ...grpc.CallOption) (*NoticeLotteryDrawResp, error) {
	client := pb.NewNoticeClient(m.cli.Conn())
	return client.NoticeLotteryDraw(ctx, in, opts...)
}

func (m *defaultNotice) GetNoticeSubscribePreference(ctx context.Context, in *GetNoticeSubscribePreferenceReq, opts ...grpc.CallOption) (*GetNoticeSubscribePreferenceResp, error) {
	client := pb.NewNoticeClient(m.cli.Conn())
	return client.GetNoticeSubscribePreference(ctx, in, opts...)
}

func (m *defaultNotice) SaveNoticeSubscribePreference(ctx context.Context, in *SaveNoticeSubscribePreferenceReq, opts ...grpc.CallOption) (*SaveNoticeSubscribePreferenceResp, error) {
	client := pb.NewNoticeClient(m.cli.Conn())
	return client.SaveNoticeSubscribePreference(ctx, in, opts...)
}

func (m *defaultNotice) NoticeLotteryStart(ctx context.Context, in *NoticeLotteryStartReq, opts ...grpc.CallOption) (*NoticeLotteryStartResp, error) {
	client := pb.NewNoticeClient(m.cli.Conn())
	return client.NoticeLotteryStart(ctx, in, opts...)
}

func (m *defaultNotice) NoticeWishSign(ctx context.Context, in *NoticeWishSignInReq, opts ...grpc.CallOption) (*NoticeWishSignInResp, error) {
	client := pb.NewNoticeClient(m.cli.Conn())
	return client.NoticeWishSign(ctx, in, opts...)
}
