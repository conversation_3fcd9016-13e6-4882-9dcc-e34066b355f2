// Code generated by goctl. DO NOT EDIT.

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
)

var (
	userAddressFieldNames          = builder.RawFieldNames(&UserAddress{})
	userAddressRows                = strings.Join(userAddressFieldNames, ",")
	userAddressRowsExpectAutoSet   = strings.Join(stringx.Remove(userAddressFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), ",")
	userAddressRowsWithPlaceHolder = strings.Join(stringx.Remove(userAddressFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), "=?,") + "=?"

	cacheLooklookUsercenterUserAddressIdPrefix = "cache:looklookUsercenter:userAddress:id:"
)

type (
	userAddressModel interface {
		Insert(ctx context.Context, data *UserAddress) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*UserAddress, error)
		Update(ctx context.Context, data *UserAddress) error
		Delete(ctx context.Context, id int64) error
	}

	defaultUserAddressModel struct {
		sqlc.CachedConn
		table string
	}

	UserAddress struct {
		Id            int64     `db:"id"`
		UserId        int64     `db:"user_id"`        // 用户id
		ContactName   string    `db:"contact_name"`   // 联系人姓名
		ContactMobile string    `db:"contact_mobile"` // 联系人手机号码
		District      string    `db:"district"`       // 地区信息
		Detail        string    `db:"detail"`         // 详细地址
		Postcode      string    `db:"postcode"`       // 邮政编码
		IsDefault     int64     `db:"is_default"`     // 是否为默认地址 1是 0否
		CreateTime    time.Time `db:"create_time"`
		UpdateTime    time.Time `db:"update_time"`
	}
)

func newUserAddressModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultUserAddressModel {
	return &defaultUserAddressModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`user_address`",
	}
}

func (m *defaultUserAddressModel) Delete(ctx context.Context, id int64) error {
	looklookUsercenterUserAddressIdKey := fmt.Sprintf("%s%v", cacheLooklookUsercenterUserAddressIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		return conn.ExecCtx(ctx, query, id)
	}, looklookUsercenterUserAddressIdKey)
	return err
}

func (m *defaultUserAddressModel) FindOne(ctx context.Context, id int64) (*UserAddress, error) {
	looklookUsercenterUserAddressIdKey := fmt.Sprintf("%s%v", cacheLooklookUsercenterUserAddressIdPrefix, id)
	var resp UserAddress
	err := m.QueryRowCtx(ctx, &resp, looklookUsercenterUserAddressIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", userAddressRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserAddressModel) Insert(ctx context.Context, data *UserAddress) (sql.Result, error) {
	looklookUsercenterUserAddressIdKey := fmt.Sprintf("%s%v", cacheLooklookUsercenterUserAddressIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?)", m.table, userAddressRowsExpectAutoSet)
		return conn.ExecCtx(ctx, query, data.UserId, data.ContactName, data.ContactMobile, data.District, data.Detail, data.Postcode, data.IsDefault)
	}, looklookUsercenterUserAddressIdKey)
	return ret, err
}

func (m *defaultUserAddressModel) Update(ctx context.Context, data *UserAddress) error {
	looklookUsercenterUserAddressIdKey := fmt.Sprintf("%s%v", cacheLooklookUsercenterUserAddressIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, userAddressRowsWithPlaceHolder)
		return conn.ExecCtx(ctx, query, data.UserId, data.ContactName, data.ContactMobile, data.District, data.Detail, data.Postcode, data.IsDefault, data.Id)
	}, looklookUsercenterUserAddressIdKey)
	return err
}

func (m *defaultUserAddressModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheLooklookUsercenterUserAddressIdPrefix, primary)
}

func (m *defaultUserAddressModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", userAddressRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultUserAddressModel) tableName() string {
	return m.table
}
