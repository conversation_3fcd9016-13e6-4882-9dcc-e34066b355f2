// Code generated by goctl. DO NOT EDIT.
// Source: vote.proto

package vote

import (
	"context"

	"looklook/app/vote/cmd/rpc/pb"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	AddVoteConfigReq            = pb.AddVoteConfigReq
	AddVoteConfigResp           = pb.AddVoteConfigResp
	AddVoteRecordReq            = pb.AddVoteRecordReq
	AddVoteRecordResp           = pb.AddVoteRecordResp
	DelVoteConfigReq            = pb.DelVoteConfigReq
	DelVoteConfigResp           = pb.DelVoteConfigResp
	DelVoteRecordReq            = pb.DelVoteRecordReq
	DelVoteRecordResp           = pb.DelVoteRecordResp
	GetUserVoteRecordDetailReq  = pb.GetUserVoteRecordDetailReq
	GetUserVoteRecordDetailResp = pb.GetUserVoteRecordDetailResp
	GetVoteConfigByIdReq        = pb.GetVoteConfigByIdReq
	GetVoteConfigByIdResp       = pb.GetVoteConfigByIdResp
	GetVoteRecordByIdReq        = pb.GetVoteRecordByIdReq
	GetVoteRecordByIdResp       = pb.GetVoteRecordByIdResp
	GetVoteRecordDetailReq      = pb.GetVoteRecordDetailReq
	GetVoteRecordDetailResp     = pb.GetVoteRecordDetailResp
	SearchVoteConfigReq         = pb.SearchVoteConfigReq
	SearchVoteConfigResp        = pb.SearchVoteConfigResp
	SearchVoteRecordReq         = pb.SearchVoteRecordReq
	SearchVoteRecordResp        = pb.SearchVoteRecordResp
	UpdateVoteConfigReq         = pb.UpdateVoteConfigReq
	UpdateVoteConfigResp        = pb.UpdateVoteConfigResp
	UpdateVoteRecordReq         = pb.UpdateVoteRecordReq
	UpdateVoteRecordResp        = pb.UpdateVoteRecordResp
	UserVoteRecordDetail        = pb.UserVoteRecordDetail
	VoteConfig                  = pb.VoteConfig
	VoteConfigJSONData          = pb.VoteConfigJSONData
	VoteOption                  = pb.VoteOption
	VoteRecord                  = pb.VoteRecord
	VoteRecordDetail            = pb.VoteRecordDetail

	Vote interface {
		// -----------------------投票表-----------------------
		AddVoteConfig(ctx context.Context, in *AddVoteConfigReq, opts ...grpc.CallOption) (*AddVoteConfigResp, error)
		UpdateVoteConfig(ctx context.Context, in *UpdateVoteConfigReq, opts ...grpc.CallOption) (*UpdateVoteConfigResp, error)
		DelVoteConfig(ctx context.Context, in *DelVoteConfigReq, opts ...grpc.CallOption) (*DelVoteConfigResp, error)
		GetVoteConfigById(ctx context.Context, in *GetVoteConfigByIdReq, opts ...grpc.CallOption) (*GetVoteConfigByIdResp, error)
		SearchVoteConfig(ctx context.Context, in *SearchVoteConfigReq, opts ...grpc.CallOption) (*SearchVoteConfigResp, error)
		// -----------------------投票记录表-----------------------
		AddVoteRecord(ctx context.Context, in *AddVoteRecordReq, opts ...grpc.CallOption) (*AddVoteRecordResp, error)
		UpdateVoteRecord(ctx context.Context, in *UpdateVoteRecordReq, opts ...grpc.CallOption) (*UpdateVoteRecordResp, error)
		DelVoteRecord(ctx context.Context, in *DelVoteRecordReq, opts ...grpc.CallOption) (*DelVoteRecordResp, error)
		GetVoteRecordById(ctx context.Context, in *GetVoteRecordByIdReq, opts ...grpc.CallOption) (*GetVoteRecordByIdResp, error)
		GetVoteRecordDetail(ctx context.Context, in *GetVoteRecordDetailReq, opts ...grpc.CallOption) (*GetVoteRecordDetailResp, error)
		GetUserVoteRecordDetail(ctx context.Context, in *GetUserVoteRecordDetailReq, opts ...grpc.CallOption) (*GetUserVoteRecordDetailResp, error)
		SearchVoteRecord(ctx context.Context, in *SearchVoteRecordReq, opts ...grpc.CallOption) (*SearchVoteRecordResp, error)
	}

	defaultVote struct {
		cli zrpc.Client
	}
)

func NewVote(cli zrpc.Client) Vote {
	return &defaultVote{
		cli: cli,
	}
}

// -----------------------投票表-----------------------
func (m *defaultVote) AddVoteConfig(ctx context.Context, in *AddVoteConfigReq, opts ...grpc.CallOption) (*AddVoteConfigResp, error) {
	client := pb.NewVoteClient(m.cli.Conn())
	return client.AddVoteConfig(ctx, in, opts...)
}

func (m *defaultVote) UpdateVoteConfig(ctx context.Context, in *UpdateVoteConfigReq, opts ...grpc.CallOption) (*UpdateVoteConfigResp, error) {
	client := pb.NewVoteClient(m.cli.Conn())
	return client.UpdateVoteConfig(ctx, in, opts...)
}

func (m *defaultVote) DelVoteConfig(ctx context.Context, in *DelVoteConfigReq, opts ...grpc.CallOption) (*DelVoteConfigResp, error) {
	client := pb.NewVoteClient(m.cli.Conn())
	return client.DelVoteConfig(ctx, in, opts...)
}

func (m *defaultVote) GetVoteConfigById(ctx context.Context, in *GetVoteConfigByIdReq, opts ...grpc.CallOption) (*GetVoteConfigByIdResp, error) {
	client := pb.NewVoteClient(m.cli.Conn())
	return client.GetVoteConfigById(ctx, in, opts...)
}

func (m *defaultVote) SearchVoteConfig(ctx context.Context, in *SearchVoteConfigReq, opts ...grpc.CallOption) (*SearchVoteConfigResp, error) {
	client := pb.NewVoteClient(m.cli.Conn())
	return client.SearchVoteConfig(ctx, in, opts...)
}

// -----------------------投票记录表-----------------------
func (m *defaultVote) AddVoteRecord(ctx context.Context, in *AddVoteRecordReq, opts ...grpc.CallOption) (*AddVoteRecordResp, error) {
	client := pb.NewVoteClient(m.cli.Conn())
	return client.AddVoteRecord(ctx, in, opts...)
}

func (m *defaultVote) UpdateVoteRecord(ctx context.Context, in *UpdateVoteRecordReq, opts ...grpc.CallOption) (*UpdateVoteRecordResp, error) {
	client := pb.NewVoteClient(m.cli.Conn())
	return client.UpdateVoteRecord(ctx, in, opts...)
}

func (m *defaultVote) DelVoteRecord(ctx context.Context, in *DelVoteRecordReq, opts ...grpc.CallOption) (*DelVoteRecordResp, error) {
	client := pb.NewVoteClient(m.cli.Conn())
	return client.DelVoteRecord(ctx, in, opts...)
}

func (m *defaultVote) GetVoteRecordById(ctx context.Context, in *GetVoteRecordByIdReq, opts ...grpc.CallOption) (*GetVoteRecordByIdResp, error) {
	client := pb.NewVoteClient(m.cli.Conn())
	return client.GetVoteRecordById(ctx, in, opts...)
}

func (m *defaultVote) GetVoteRecordDetail(ctx context.Context, in *GetVoteRecordDetailReq, opts ...grpc.CallOption) (*GetVoteRecordDetailResp, error) {
	client := pb.NewVoteClient(m.cli.Conn())
	return client.GetVoteRecordDetail(ctx, in, opts...)
}

func (m *defaultVote) GetUserVoteRecordDetail(ctx context.Context, in *GetUserVoteRecordDetailReq, opts ...grpc.CallOption) (*GetUserVoteRecordDetailResp, error) {
	client := pb.NewVoteClient(m.cli.Conn())
	return client.GetUserVoteRecordDetail(ctx, in, opts...)
}

func (m *defaultVote) SearchVoteRecord(ctx context.Context, in *SearchVoteRecordReq, opts ...grpc.CallOption) (*SearchVoteRecordResp, error) {
	client := pb.NewVoteClient(m.cli.Conn())
	return client.SearchVoteRecord(ctx, in, opts...)
}
