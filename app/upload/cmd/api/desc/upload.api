syntax = "v1"

info(
	title: "文件上传服务"
	desc: "文件上传服务"
	author: "j<PERSON><PERSON><PERSON>"
	email: "<EMAIL>"
	version: "v1"
)

type (
	UserUploadReq {
		FileName string `json:"file_name,optional"`
		Ext      string `json:"ext,optional"`
		Size     int64  `json:"size,optional"`
		FileData []byte `json:"file_data,optional"`
	}

	UserUploadRes {
		Url string `json:"url"`
	}
)

// need login
@server (
	prefix: upload/v1
	group:  upload
	jwt:    JwtAuth
)
service upload {
	@doc "文件上传"
	@handler upload
	post /upload/add (UserUploadReq) returns (UserUploadRes)
}