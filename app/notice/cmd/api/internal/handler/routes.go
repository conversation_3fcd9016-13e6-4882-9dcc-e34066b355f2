// Code generated by goctl. DO NOT EDIT.
package handler

import (
	"net/http"

	event "looklook/app/notice/cmd/api/internal/handler/event"
	"looklook/app/notice/cmd/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/event",
				Handler: event.VerifyEventHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/event",
				Handler: event.ReceiveEventHandler(serverCtx),
			},
		},
		rest.WithPrefix("/notice/v1"),
	)
}
