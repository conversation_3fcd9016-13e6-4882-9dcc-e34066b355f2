// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.4
// source: lottery.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Lottery_AddLottery_FullMethodName                         = "/pb.lottery/AddLottery"
	Lottery_UpdateLottery_FullMethodName                      = "/pb.lottery/UpdateLottery"
	Lottery_DelLottery_FullMethodName                         = "/pb.lottery/DelLottery"
	Lottery_GetLotteryById_FullMethodName                     = "/pb.lottery/GetLotteryById"
	Lottery_SearchLottery_FullMethodName                      = "/pb.lottery/SearchLottery"
	Lottery_SetIsSelectedLottery_FullMethodName               = "/pb.lottery/SetIsSelectedLottery"
	Lottery_LotteryDetail_FullMethodName                      = "/pb.lottery/LotteryDetail"
	Lottery_LotterySponsor_FullMethodName                     = "/pb.lottery/LotterySponsor"
	Lottery_AnnounceLottery_FullMethodName                    = "/pb.lottery/AnnounceLottery"
	Lottery_CheckUserCreatedLottery_FullMethodName            = "/pb.lottery/CheckUserCreatedLottery"
	Lottery_CheckUserCreatedLotteryAndToday_FullMethodName    = "/pb.lottery/CheckUserCreatedLotteryAndToday"
	Lottery_CheckUserCreatedLotteryAndThisWeek_FullMethodName = "/pb.lottery/CheckUserCreatedLotteryAndThisWeek"
	Lottery_GetLotteryListAfterLogin_FullMethodName           = "/pb.lottery/GetLotteryListAfterLogin"
	Lottery_GetLotteryStatistic_FullMethodName                = "/pb.lottery/GetLotteryStatistic"
	Lottery_GetLotteryListLastId_FullMethodName               = "/pb.lottery/GetLotteryListLastId"
	Lottery_PublishLottery_FullMethodName                     = "/pb.lottery/PublishLottery"
	Lottery_GetLotteryPrizesListByUserId_FullMethodName       = "/pb.lottery/GetLotteryPrizesListByUserId"
	Lottery_AddPrize_FullMethodName                           = "/pb.lottery/AddPrize"
	Lottery_UpdatePrize_FullMethodName                        = "/pb.lottery/UpdatePrize"
	Lottery_DelPrize_FullMethodName                           = "/pb.lottery/DelPrize"
	Lottery_GetPrizeById_FullMethodName                       = "/pb.lottery/GetPrizeById"
	Lottery_SearchPrize_FullMethodName                        = "/pb.lottery/SearchPrize"
	Lottery_GetPrizeListByLotteryId_FullMethodName            = "/pb.lottery/GetPrizeListByLotteryId"
	Lottery_AddLotteryParticipation_FullMethodName            = "/pb.lottery/AddLotteryParticipation"
	Lottery_SearchLotteryParticipation_FullMethodName         = "/pb.lottery/SearchLotteryParticipation"
	Lottery_GetParticipationUserIdsByLotteryId_FullMethodName = "/pb.lottery/GetParticipationUserIdsByLotteryId"
	Lottery_CheckIsParticipated_FullMethodName                = "/pb.lottery/CheckIsParticipated"
	Lottery_GetSelectedLotteryStatistic_FullMethodName        = "/pb.lottery/GetSelectedLotteryStatistic"
	Lottery_CheckSelectedLotteryParticipated_FullMethodName   = "/pb.lottery/CheckSelectedLotteryParticipated"
	Lottery_CheckUserIsWon_FullMethodName                     = "/pb.lottery/CheckUserIsWon"
	Lottery_GetWonList_FullMethodName                         = "/pb.lottery/GetWonList"
	Lottery_GetWonListCount_FullMethodName                    = "/pb.lottery/GetWonListCount"
	Lottery_GetWonListByLotteryId_FullMethodName              = "/pb.lottery/GetWonListByLotteryId"
	Lottery_AddClockTaskRecord_FullMethodName                 = "/pb.lottery/AddClockTaskRecord"
)

// LotteryClient is the client API for Lottery service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LotteryClient interface {
	// -----------------------抽奖表-----------------------
	AddLottery(ctx context.Context, in *AddLotteryReq, opts ...grpc.CallOption) (*AddLotteryResp, error)
	UpdateLottery(ctx context.Context, in *UpdateLotteryReq, opts ...grpc.CallOption) (*UpdateLotteryResp, error)
	DelLottery(ctx context.Context, in *DelLotteryReq, opts ...grpc.CallOption) (*DelLotteryResp, error)
	GetLotteryById(ctx context.Context, in *GetLotteryByIdReq, opts ...grpc.CallOption) (*GetLotteryByIdResp, error)
	SearchLottery(ctx context.Context, in *SearchLotteryReq, opts ...grpc.CallOption) (*SearchLotteryResp, error)
	SetIsSelectedLottery(ctx context.Context, in *SetIsSelectedLotteryReq, opts ...grpc.CallOption) (*SetIsSelectedLotteryResp, error)
	LotteryDetail(ctx context.Context, in *LotteryDetailReq, opts ...grpc.CallOption) (*LotteryDetailResp, error)
	LotterySponsor(ctx context.Context, in *LotterySponsorReq, opts ...grpc.CallOption) (*LotterySponsorResp, error)
	AnnounceLottery(ctx context.Context, in *AnnounceLotteryReq, opts ...grpc.CallOption) (*AnnounceLotteryResp, error)
	CheckUserCreatedLottery(ctx context.Context, in *CheckUserCreatedLotteryReq, opts ...grpc.CallOption) (*CheckUserCreatedLotteryResp, error)
	CheckUserCreatedLotteryAndToday(ctx context.Context, in *CheckUserCreatedLotteryAndTodayReq, opts ...grpc.CallOption) (*CheckUserCreatedLotteryAndTodayResp, error)
	CheckUserCreatedLotteryAndThisWeek(ctx context.Context, in *CheckUserCreatedLotteryAndThisWeekReq, opts ...grpc.CallOption) (*CheckUserCreatedLotteryAndThisWeekResp, error)
	GetLotteryListAfterLogin(ctx context.Context, in *GetLotteryListAfterLoginReq, opts ...grpc.CallOption) (*GetLotteryListAfterLoginResp, error)
	GetLotteryStatistic(ctx context.Context, in *GetLotteryStatisticReq, opts ...grpc.CallOption) (*GetLotteryStatisticResp, error)
	GetLotteryListLastId(ctx context.Context, in *GetLotteryListLastIdReq, opts ...grpc.CallOption) (*GetLotteryListLastIdResp, error)
	PublishLottery(ctx context.Context, in *PublishLotteryReq, opts ...grpc.CallOption) (*PublishLotteryResp, error)
	GetLotteryPrizesListByUserId(ctx context.Context, in *GetLotteryPrizesListByUserIdReq, opts ...grpc.CallOption) (*GetLotteryPrizesListByUserIdResp, error)
	// -----------------------奖品表-----------------------
	AddPrize(ctx context.Context, in *AddPrizeReq, opts ...grpc.CallOption) (*AddPrizeResp, error)
	UpdatePrize(ctx context.Context, in *UpdatePrizeReq, opts ...grpc.CallOption) (*UpdatePrizeResp, error)
	DelPrize(ctx context.Context, in *DelPrizeReq, opts ...grpc.CallOption) (*DelPrizeResp, error)
	GetPrizeById(ctx context.Context, in *GetPrizeByIdReq, opts ...grpc.CallOption) (*GetPrizeByIdResp, error)
	SearchPrize(ctx context.Context, in *SearchPrizeReq, opts ...grpc.CallOption) (*SearchPrizeResp, error)
	GetPrizeListByLotteryId(ctx context.Context, in *GetPrizeListByLotteryIdReq, opts ...grpc.CallOption) (*GetPrizeListByLotteryIdResp, error)
	// -----------------------参与抽奖-----------------------
	AddLotteryParticipation(ctx context.Context, in *AddLotteryParticipationReq, opts ...grpc.CallOption) (*AddLotteryParticipationResp, error)
	SearchLotteryParticipation(ctx context.Context, in *SearchLotteryParticipationReq, opts ...grpc.CallOption) (*SearchLotteryParticipationResp, error)
	GetParticipationUserIdsByLotteryId(ctx context.Context, in *GetParticipationUserIdsByLotteryIdReq, opts ...grpc.CallOption) (*GetParticipationUserIdsByLotteryIdResp, error)
	CheckIsParticipated(ctx context.Context, in *CheckIsParticipatedReq, opts ...grpc.CallOption) (*CheckIsParticipatedResp, error)
	GetSelectedLotteryStatistic(ctx context.Context, in *GetSelectedLotteryStatisticReq, opts ...grpc.CallOption) (*GetSelectedLotteryStatisticResp, error)
	CheckSelectedLotteryParticipated(ctx context.Context, in *CheckSelectedLotteryParticipatedReq, opts ...grpc.CallOption) (*CheckSelectedLotteryParticipatedResp, error)
	CheckUserIsWon(ctx context.Context, in *CheckUserIsWonReq, opts ...grpc.CallOption) (*CheckUserIsWonResp, error)
	GetWonList(ctx context.Context, in *GetWonListReq, opts ...grpc.CallOption) (*GetWonListResp, error)
	GetWonListCount(ctx context.Context, in *GetWonListCountReq, opts ...grpc.CallOption) (*GetWonListCountResp, error)
	GetWonListByLotteryId(ctx context.Context, in *GetWonListByLotteryIdReq, opts ...grpc.CallOption) (*GetWonListByLotteryIdResp, error)
	// -----------------------完成打卡任务-----------------------
	AddClockTaskRecord(ctx context.Context, in *AddClockTaskRecordReq, opts ...grpc.CallOption) (*AddClockTaskRecordResp, error)
}

type lotteryClient struct {
	cc grpc.ClientConnInterface
}

func NewLotteryClient(cc grpc.ClientConnInterface) LotteryClient {
	return &lotteryClient{cc}
}

func (c *lotteryClient) AddLottery(ctx context.Context, in *AddLotteryReq, opts ...grpc.CallOption) (*AddLotteryResp, error) {
	out := new(AddLotteryResp)
	err := c.cc.Invoke(ctx, Lottery_AddLottery_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) UpdateLottery(ctx context.Context, in *UpdateLotteryReq, opts ...grpc.CallOption) (*UpdateLotteryResp, error) {
	out := new(UpdateLotteryResp)
	err := c.cc.Invoke(ctx, Lottery_UpdateLottery_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) DelLottery(ctx context.Context, in *DelLotteryReq, opts ...grpc.CallOption) (*DelLotteryResp, error) {
	out := new(DelLotteryResp)
	err := c.cc.Invoke(ctx, Lottery_DelLottery_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) GetLotteryById(ctx context.Context, in *GetLotteryByIdReq, opts ...grpc.CallOption) (*GetLotteryByIdResp, error) {
	out := new(GetLotteryByIdResp)
	err := c.cc.Invoke(ctx, Lottery_GetLotteryById_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) SearchLottery(ctx context.Context, in *SearchLotteryReq, opts ...grpc.CallOption) (*SearchLotteryResp, error) {
	out := new(SearchLotteryResp)
	err := c.cc.Invoke(ctx, Lottery_SearchLottery_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) SetIsSelectedLottery(ctx context.Context, in *SetIsSelectedLotteryReq, opts ...grpc.CallOption) (*SetIsSelectedLotteryResp, error) {
	out := new(SetIsSelectedLotteryResp)
	err := c.cc.Invoke(ctx, Lottery_SetIsSelectedLottery_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) LotteryDetail(ctx context.Context, in *LotteryDetailReq, opts ...grpc.CallOption) (*LotteryDetailResp, error) {
	out := new(LotteryDetailResp)
	err := c.cc.Invoke(ctx, Lottery_LotteryDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) LotterySponsor(ctx context.Context, in *LotterySponsorReq, opts ...grpc.CallOption) (*LotterySponsorResp, error) {
	out := new(LotterySponsorResp)
	err := c.cc.Invoke(ctx, Lottery_LotterySponsor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) AnnounceLottery(ctx context.Context, in *AnnounceLotteryReq, opts ...grpc.CallOption) (*AnnounceLotteryResp, error) {
	out := new(AnnounceLotteryResp)
	err := c.cc.Invoke(ctx, Lottery_AnnounceLottery_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) CheckUserCreatedLottery(ctx context.Context, in *CheckUserCreatedLotteryReq, opts ...grpc.CallOption) (*CheckUserCreatedLotteryResp, error) {
	out := new(CheckUserCreatedLotteryResp)
	err := c.cc.Invoke(ctx, Lottery_CheckUserCreatedLottery_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) CheckUserCreatedLotteryAndToday(ctx context.Context, in *CheckUserCreatedLotteryAndTodayReq, opts ...grpc.CallOption) (*CheckUserCreatedLotteryAndTodayResp, error) {
	out := new(CheckUserCreatedLotteryAndTodayResp)
	err := c.cc.Invoke(ctx, Lottery_CheckUserCreatedLotteryAndToday_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) CheckUserCreatedLotteryAndThisWeek(ctx context.Context, in *CheckUserCreatedLotteryAndThisWeekReq, opts ...grpc.CallOption) (*CheckUserCreatedLotteryAndThisWeekResp, error) {
	out := new(CheckUserCreatedLotteryAndThisWeekResp)
	err := c.cc.Invoke(ctx, Lottery_CheckUserCreatedLotteryAndThisWeek_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) GetLotteryListAfterLogin(ctx context.Context, in *GetLotteryListAfterLoginReq, opts ...grpc.CallOption) (*GetLotteryListAfterLoginResp, error) {
	out := new(GetLotteryListAfterLoginResp)
	err := c.cc.Invoke(ctx, Lottery_GetLotteryListAfterLogin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) GetLotteryStatistic(ctx context.Context, in *GetLotteryStatisticReq, opts ...grpc.CallOption) (*GetLotteryStatisticResp, error) {
	out := new(GetLotteryStatisticResp)
	err := c.cc.Invoke(ctx, Lottery_GetLotteryStatistic_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) GetLotteryListLastId(ctx context.Context, in *GetLotteryListLastIdReq, opts ...grpc.CallOption) (*GetLotteryListLastIdResp, error) {
	out := new(GetLotteryListLastIdResp)
	err := c.cc.Invoke(ctx, Lottery_GetLotteryListLastId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) PublishLottery(ctx context.Context, in *PublishLotteryReq, opts ...grpc.CallOption) (*PublishLotteryResp, error) {
	out := new(PublishLotteryResp)
	err := c.cc.Invoke(ctx, Lottery_PublishLottery_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) GetLotteryPrizesListByUserId(ctx context.Context, in *GetLotteryPrizesListByUserIdReq, opts ...grpc.CallOption) (*GetLotteryPrizesListByUserIdResp, error) {
	out := new(GetLotteryPrizesListByUserIdResp)
	err := c.cc.Invoke(ctx, Lottery_GetLotteryPrizesListByUserId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) AddPrize(ctx context.Context, in *AddPrizeReq, opts ...grpc.CallOption) (*AddPrizeResp, error) {
	out := new(AddPrizeResp)
	err := c.cc.Invoke(ctx, Lottery_AddPrize_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) UpdatePrize(ctx context.Context, in *UpdatePrizeReq, opts ...grpc.CallOption) (*UpdatePrizeResp, error) {
	out := new(UpdatePrizeResp)
	err := c.cc.Invoke(ctx, Lottery_UpdatePrize_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) DelPrize(ctx context.Context, in *DelPrizeReq, opts ...grpc.CallOption) (*DelPrizeResp, error) {
	out := new(DelPrizeResp)
	err := c.cc.Invoke(ctx, Lottery_DelPrize_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) GetPrizeById(ctx context.Context, in *GetPrizeByIdReq, opts ...grpc.CallOption) (*GetPrizeByIdResp, error) {
	out := new(GetPrizeByIdResp)
	err := c.cc.Invoke(ctx, Lottery_GetPrizeById_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) SearchPrize(ctx context.Context, in *SearchPrizeReq, opts ...grpc.CallOption) (*SearchPrizeResp, error) {
	out := new(SearchPrizeResp)
	err := c.cc.Invoke(ctx, Lottery_SearchPrize_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) GetPrizeListByLotteryId(ctx context.Context, in *GetPrizeListByLotteryIdReq, opts ...grpc.CallOption) (*GetPrizeListByLotteryIdResp, error) {
	out := new(GetPrizeListByLotteryIdResp)
	err := c.cc.Invoke(ctx, Lottery_GetPrizeListByLotteryId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) AddLotteryParticipation(ctx context.Context, in *AddLotteryParticipationReq, opts ...grpc.CallOption) (*AddLotteryParticipationResp, error) {
	out := new(AddLotteryParticipationResp)
	err := c.cc.Invoke(ctx, Lottery_AddLotteryParticipation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) SearchLotteryParticipation(ctx context.Context, in *SearchLotteryParticipationReq, opts ...grpc.CallOption) (*SearchLotteryParticipationResp, error) {
	out := new(SearchLotteryParticipationResp)
	err := c.cc.Invoke(ctx, Lottery_SearchLotteryParticipation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) GetParticipationUserIdsByLotteryId(ctx context.Context, in *GetParticipationUserIdsByLotteryIdReq, opts ...grpc.CallOption) (*GetParticipationUserIdsByLotteryIdResp, error) {
	out := new(GetParticipationUserIdsByLotteryIdResp)
	err := c.cc.Invoke(ctx, Lottery_GetParticipationUserIdsByLotteryId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) CheckIsParticipated(ctx context.Context, in *CheckIsParticipatedReq, opts ...grpc.CallOption) (*CheckIsParticipatedResp, error) {
	out := new(CheckIsParticipatedResp)
	err := c.cc.Invoke(ctx, Lottery_CheckIsParticipated_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) GetSelectedLotteryStatistic(ctx context.Context, in *GetSelectedLotteryStatisticReq, opts ...grpc.CallOption) (*GetSelectedLotteryStatisticResp, error) {
	out := new(GetSelectedLotteryStatisticResp)
	err := c.cc.Invoke(ctx, Lottery_GetSelectedLotteryStatistic_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) CheckSelectedLotteryParticipated(ctx context.Context, in *CheckSelectedLotteryParticipatedReq, opts ...grpc.CallOption) (*CheckSelectedLotteryParticipatedResp, error) {
	out := new(CheckSelectedLotteryParticipatedResp)
	err := c.cc.Invoke(ctx, Lottery_CheckSelectedLotteryParticipated_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) CheckUserIsWon(ctx context.Context, in *CheckUserIsWonReq, opts ...grpc.CallOption) (*CheckUserIsWonResp, error) {
	out := new(CheckUserIsWonResp)
	err := c.cc.Invoke(ctx, Lottery_CheckUserIsWon_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) GetWonList(ctx context.Context, in *GetWonListReq, opts ...grpc.CallOption) (*GetWonListResp, error) {
	out := new(GetWonListResp)
	err := c.cc.Invoke(ctx, Lottery_GetWonList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) GetWonListCount(ctx context.Context, in *GetWonListCountReq, opts ...grpc.CallOption) (*GetWonListCountResp, error) {
	out := new(GetWonListCountResp)
	err := c.cc.Invoke(ctx, Lottery_GetWonListCount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) GetWonListByLotteryId(ctx context.Context, in *GetWonListByLotteryIdReq, opts ...grpc.CallOption) (*GetWonListByLotteryIdResp, error) {
	out := new(GetWonListByLotteryIdResp)
	err := c.cc.Invoke(ctx, Lottery_GetWonListByLotteryId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lotteryClient) AddClockTaskRecord(ctx context.Context, in *AddClockTaskRecordReq, opts ...grpc.CallOption) (*AddClockTaskRecordResp, error) {
	out := new(AddClockTaskRecordResp)
	err := c.cc.Invoke(ctx, Lottery_AddClockTaskRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LotteryServer is the server API for Lottery service.
// All implementations must embed UnimplementedLotteryServer
// for forward compatibility
type LotteryServer interface {
	// -----------------------抽奖表-----------------------
	AddLottery(context.Context, *AddLotteryReq) (*AddLotteryResp, error)
	UpdateLottery(context.Context, *UpdateLotteryReq) (*UpdateLotteryResp, error)
	DelLottery(context.Context, *DelLotteryReq) (*DelLotteryResp, error)
	GetLotteryById(context.Context, *GetLotteryByIdReq) (*GetLotteryByIdResp, error)
	SearchLottery(context.Context, *SearchLotteryReq) (*SearchLotteryResp, error)
	SetIsSelectedLottery(context.Context, *SetIsSelectedLotteryReq) (*SetIsSelectedLotteryResp, error)
	LotteryDetail(context.Context, *LotteryDetailReq) (*LotteryDetailResp, error)
	LotterySponsor(context.Context, *LotterySponsorReq) (*LotterySponsorResp, error)
	AnnounceLottery(context.Context, *AnnounceLotteryReq) (*AnnounceLotteryResp, error)
	CheckUserCreatedLottery(context.Context, *CheckUserCreatedLotteryReq) (*CheckUserCreatedLotteryResp, error)
	CheckUserCreatedLotteryAndToday(context.Context, *CheckUserCreatedLotteryAndTodayReq) (*CheckUserCreatedLotteryAndTodayResp, error)
	CheckUserCreatedLotteryAndThisWeek(context.Context, *CheckUserCreatedLotteryAndThisWeekReq) (*CheckUserCreatedLotteryAndThisWeekResp, error)
	GetLotteryListAfterLogin(context.Context, *GetLotteryListAfterLoginReq) (*GetLotteryListAfterLoginResp, error)
	GetLotteryStatistic(context.Context, *GetLotteryStatisticReq) (*GetLotteryStatisticResp, error)
	GetLotteryListLastId(context.Context, *GetLotteryListLastIdReq) (*GetLotteryListLastIdResp, error)
	PublishLottery(context.Context, *PublishLotteryReq) (*PublishLotteryResp, error)
	GetLotteryPrizesListByUserId(context.Context, *GetLotteryPrizesListByUserIdReq) (*GetLotteryPrizesListByUserIdResp, error)
	// -----------------------奖品表-----------------------
	AddPrize(context.Context, *AddPrizeReq) (*AddPrizeResp, error)
	UpdatePrize(context.Context, *UpdatePrizeReq) (*UpdatePrizeResp, error)
	DelPrize(context.Context, *DelPrizeReq) (*DelPrizeResp, error)
	GetPrizeById(context.Context, *GetPrizeByIdReq) (*GetPrizeByIdResp, error)
	SearchPrize(context.Context, *SearchPrizeReq) (*SearchPrizeResp, error)
	GetPrizeListByLotteryId(context.Context, *GetPrizeListByLotteryIdReq) (*GetPrizeListByLotteryIdResp, error)
	// -----------------------参与抽奖-----------------------
	AddLotteryParticipation(context.Context, *AddLotteryParticipationReq) (*AddLotteryParticipationResp, error)
	SearchLotteryParticipation(context.Context, *SearchLotteryParticipationReq) (*SearchLotteryParticipationResp, error)
	GetParticipationUserIdsByLotteryId(context.Context, *GetParticipationUserIdsByLotteryIdReq) (*GetParticipationUserIdsByLotteryIdResp, error)
	CheckIsParticipated(context.Context, *CheckIsParticipatedReq) (*CheckIsParticipatedResp, error)
	GetSelectedLotteryStatistic(context.Context, *GetSelectedLotteryStatisticReq) (*GetSelectedLotteryStatisticResp, error)
	CheckSelectedLotteryParticipated(context.Context, *CheckSelectedLotteryParticipatedReq) (*CheckSelectedLotteryParticipatedResp, error)
	CheckUserIsWon(context.Context, *CheckUserIsWonReq) (*CheckUserIsWonResp, error)
	GetWonList(context.Context, *GetWonListReq) (*GetWonListResp, error)
	GetWonListCount(context.Context, *GetWonListCountReq) (*GetWonListCountResp, error)
	GetWonListByLotteryId(context.Context, *GetWonListByLotteryIdReq) (*GetWonListByLotteryIdResp, error)
	// -----------------------完成打卡任务-----------------------
	AddClockTaskRecord(context.Context, *AddClockTaskRecordReq) (*AddClockTaskRecordResp, error)
	mustEmbedUnimplementedLotteryServer()
}

// UnimplementedLotteryServer must be embedded to have forward compatible implementations.
type UnimplementedLotteryServer struct {
}

func (UnimplementedLotteryServer) AddLottery(context.Context, *AddLotteryReq) (*AddLotteryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddLottery not implemented")
}
func (UnimplementedLotteryServer) UpdateLottery(context.Context, *UpdateLotteryReq) (*UpdateLotteryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateLottery not implemented")
}
func (UnimplementedLotteryServer) DelLottery(context.Context, *DelLotteryReq) (*DelLotteryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelLottery not implemented")
}
func (UnimplementedLotteryServer) GetLotteryById(context.Context, *GetLotteryByIdReq) (*GetLotteryByIdResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLotteryById not implemented")
}
func (UnimplementedLotteryServer) SearchLottery(context.Context, *SearchLotteryReq) (*SearchLotteryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchLottery not implemented")
}
func (UnimplementedLotteryServer) SetIsSelectedLottery(context.Context, *SetIsSelectedLotteryReq) (*SetIsSelectedLotteryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetIsSelectedLottery not implemented")
}
func (UnimplementedLotteryServer) LotteryDetail(context.Context, *LotteryDetailReq) (*LotteryDetailResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LotteryDetail not implemented")
}
func (UnimplementedLotteryServer) LotterySponsor(context.Context, *LotterySponsorReq) (*LotterySponsorResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LotterySponsor not implemented")
}
func (UnimplementedLotteryServer) AnnounceLottery(context.Context, *AnnounceLotteryReq) (*AnnounceLotteryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AnnounceLottery not implemented")
}
func (UnimplementedLotteryServer) CheckUserCreatedLottery(context.Context, *CheckUserCreatedLotteryReq) (*CheckUserCreatedLotteryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckUserCreatedLottery not implemented")
}
func (UnimplementedLotteryServer) CheckUserCreatedLotteryAndToday(context.Context, *CheckUserCreatedLotteryAndTodayReq) (*CheckUserCreatedLotteryAndTodayResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckUserCreatedLotteryAndToday not implemented")
}
func (UnimplementedLotteryServer) CheckUserCreatedLotteryAndThisWeek(context.Context, *CheckUserCreatedLotteryAndThisWeekReq) (*CheckUserCreatedLotteryAndThisWeekResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckUserCreatedLotteryAndThisWeek not implemented")
}
func (UnimplementedLotteryServer) GetLotteryListAfterLogin(context.Context, *GetLotteryListAfterLoginReq) (*GetLotteryListAfterLoginResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLotteryListAfterLogin not implemented")
}
func (UnimplementedLotteryServer) GetLotteryStatistic(context.Context, *GetLotteryStatisticReq) (*GetLotteryStatisticResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLotteryStatistic not implemented")
}
func (UnimplementedLotteryServer) GetLotteryListLastId(context.Context, *GetLotteryListLastIdReq) (*GetLotteryListLastIdResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLotteryListLastId not implemented")
}
func (UnimplementedLotteryServer) PublishLottery(context.Context, *PublishLotteryReq) (*PublishLotteryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishLottery not implemented")
}
func (UnimplementedLotteryServer) GetLotteryPrizesListByUserId(context.Context, *GetLotteryPrizesListByUserIdReq) (*GetLotteryPrizesListByUserIdResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLotteryPrizesListByUserId not implemented")
}
func (UnimplementedLotteryServer) AddPrize(context.Context, *AddPrizeReq) (*AddPrizeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddPrize not implemented")
}
func (UnimplementedLotteryServer) UpdatePrize(context.Context, *UpdatePrizeReq) (*UpdatePrizeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePrize not implemented")
}
func (UnimplementedLotteryServer) DelPrize(context.Context, *DelPrizeReq) (*DelPrizeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelPrize not implemented")
}
func (UnimplementedLotteryServer) GetPrizeById(context.Context, *GetPrizeByIdReq) (*GetPrizeByIdResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPrizeById not implemented")
}
func (UnimplementedLotteryServer) SearchPrize(context.Context, *SearchPrizeReq) (*SearchPrizeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchPrize not implemented")
}
func (UnimplementedLotteryServer) GetPrizeListByLotteryId(context.Context, *GetPrizeListByLotteryIdReq) (*GetPrizeListByLotteryIdResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPrizeListByLotteryId not implemented")
}
func (UnimplementedLotteryServer) AddLotteryParticipation(context.Context, *AddLotteryParticipationReq) (*AddLotteryParticipationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddLotteryParticipation not implemented")
}
func (UnimplementedLotteryServer) SearchLotteryParticipation(context.Context, *SearchLotteryParticipationReq) (*SearchLotteryParticipationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchLotteryParticipation not implemented")
}
func (UnimplementedLotteryServer) GetParticipationUserIdsByLotteryId(context.Context, *GetParticipationUserIdsByLotteryIdReq) (*GetParticipationUserIdsByLotteryIdResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetParticipationUserIdsByLotteryId not implemented")
}
func (UnimplementedLotteryServer) CheckIsParticipated(context.Context, *CheckIsParticipatedReq) (*CheckIsParticipatedResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckIsParticipated not implemented")
}
func (UnimplementedLotteryServer) GetSelectedLotteryStatistic(context.Context, *GetSelectedLotteryStatisticReq) (*GetSelectedLotteryStatisticResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSelectedLotteryStatistic not implemented")
}
func (UnimplementedLotteryServer) CheckSelectedLotteryParticipated(context.Context, *CheckSelectedLotteryParticipatedReq) (*CheckSelectedLotteryParticipatedResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckSelectedLotteryParticipated not implemented")
}
func (UnimplementedLotteryServer) CheckUserIsWon(context.Context, *CheckUserIsWonReq) (*CheckUserIsWonResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckUserIsWon not implemented")
}
func (UnimplementedLotteryServer) GetWonList(context.Context, *GetWonListReq) (*GetWonListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWonList not implemented")
}
func (UnimplementedLotteryServer) GetWonListCount(context.Context, *GetWonListCountReq) (*GetWonListCountResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWonListCount not implemented")
}
func (UnimplementedLotteryServer) GetWonListByLotteryId(context.Context, *GetWonListByLotteryIdReq) (*GetWonListByLotteryIdResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWonListByLotteryId not implemented")
}
func (UnimplementedLotteryServer) AddClockTaskRecord(context.Context, *AddClockTaskRecordReq) (*AddClockTaskRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddClockTaskRecord not implemented")
}
func (UnimplementedLotteryServer) mustEmbedUnimplementedLotteryServer() {}

// UnsafeLotteryServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LotteryServer will
// result in compilation errors.
type UnsafeLotteryServer interface {
	mustEmbedUnimplementedLotteryServer()
}

func RegisterLotteryServer(s grpc.ServiceRegistrar, srv LotteryServer) {
	s.RegisterService(&Lottery_ServiceDesc, srv)
}

func _Lottery_AddLottery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddLotteryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).AddLottery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_AddLottery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).AddLottery(ctx, req.(*AddLotteryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_UpdateLottery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLotteryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).UpdateLottery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_UpdateLottery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).UpdateLottery(ctx, req.(*UpdateLotteryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_DelLottery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelLotteryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).DelLottery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_DelLottery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).DelLottery(ctx, req.(*DelLotteryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_GetLotteryById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLotteryByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).GetLotteryById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_GetLotteryById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).GetLotteryById(ctx, req.(*GetLotteryByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_SearchLottery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchLotteryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).SearchLottery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_SearchLottery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).SearchLottery(ctx, req.(*SearchLotteryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_SetIsSelectedLottery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetIsSelectedLotteryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).SetIsSelectedLottery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_SetIsSelectedLottery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).SetIsSelectedLottery(ctx, req.(*SetIsSelectedLotteryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_LotteryDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LotteryDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).LotteryDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_LotteryDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).LotteryDetail(ctx, req.(*LotteryDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_LotterySponsor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LotterySponsorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).LotterySponsor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_LotterySponsor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).LotterySponsor(ctx, req.(*LotterySponsorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_AnnounceLottery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AnnounceLotteryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).AnnounceLottery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_AnnounceLottery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).AnnounceLottery(ctx, req.(*AnnounceLotteryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_CheckUserCreatedLottery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserCreatedLotteryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).CheckUserCreatedLottery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_CheckUserCreatedLottery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).CheckUserCreatedLottery(ctx, req.(*CheckUserCreatedLotteryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_CheckUserCreatedLotteryAndToday_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserCreatedLotteryAndTodayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).CheckUserCreatedLotteryAndToday(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_CheckUserCreatedLotteryAndToday_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).CheckUserCreatedLotteryAndToday(ctx, req.(*CheckUserCreatedLotteryAndTodayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_CheckUserCreatedLotteryAndThisWeek_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserCreatedLotteryAndThisWeekReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).CheckUserCreatedLotteryAndThisWeek(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_CheckUserCreatedLotteryAndThisWeek_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).CheckUserCreatedLotteryAndThisWeek(ctx, req.(*CheckUserCreatedLotteryAndThisWeekReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_GetLotteryListAfterLogin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLotteryListAfterLoginReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).GetLotteryListAfterLogin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_GetLotteryListAfterLogin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).GetLotteryListAfterLogin(ctx, req.(*GetLotteryListAfterLoginReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_GetLotteryStatistic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLotteryStatisticReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).GetLotteryStatistic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_GetLotteryStatistic_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).GetLotteryStatistic(ctx, req.(*GetLotteryStatisticReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_GetLotteryListLastId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLotteryListLastIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).GetLotteryListLastId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_GetLotteryListLastId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).GetLotteryListLastId(ctx, req.(*GetLotteryListLastIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_PublishLottery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PublishLotteryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).PublishLottery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_PublishLottery_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).PublishLottery(ctx, req.(*PublishLotteryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_GetLotteryPrizesListByUserId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLotteryPrizesListByUserIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).GetLotteryPrizesListByUserId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_GetLotteryPrizesListByUserId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).GetLotteryPrizesListByUserId(ctx, req.(*GetLotteryPrizesListByUserIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_AddPrize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPrizeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).AddPrize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_AddPrize_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).AddPrize(ctx, req.(*AddPrizeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_UpdatePrize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePrizeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).UpdatePrize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_UpdatePrize_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).UpdatePrize(ctx, req.(*UpdatePrizeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_DelPrize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPrizeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).DelPrize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_DelPrize_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).DelPrize(ctx, req.(*DelPrizeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_GetPrizeById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPrizeByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).GetPrizeById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_GetPrizeById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).GetPrizeById(ctx, req.(*GetPrizeByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_SearchPrize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchPrizeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).SearchPrize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_SearchPrize_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).SearchPrize(ctx, req.(*SearchPrizeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_GetPrizeListByLotteryId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPrizeListByLotteryIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).GetPrizeListByLotteryId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_GetPrizeListByLotteryId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).GetPrizeListByLotteryId(ctx, req.(*GetPrizeListByLotteryIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_AddLotteryParticipation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddLotteryParticipationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).AddLotteryParticipation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_AddLotteryParticipation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).AddLotteryParticipation(ctx, req.(*AddLotteryParticipationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_SearchLotteryParticipation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchLotteryParticipationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).SearchLotteryParticipation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_SearchLotteryParticipation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).SearchLotteryParticipation(ctx, req.(*SearchLotteryParticipationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_GetParticipationUserIdsByLotteryId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetParticipationUserIdsByLotteryIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).GetParticipationUserIdsByLotteryId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_GetParticipationUserIdsByLotteryId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).GetParticipationUserIdsByLotteryId(ctx, req.(*GetParticipationUserIdsByLotteryIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_CheckIsParticipated_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckIsParticipatedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).CheckIsParticipated(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_CheckIsParticipated_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).CheckIsParticipated(ctx, req.(*CheckIsParticipatedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_GetSelectedLotteryStatistic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSelectedLotteryStatisticReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).GetSelectedLotteryStatistic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_GetSelectedLotteryStatistic_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).GetSelectedLotteryStatistic(ctx, req.(*GetSelectedLotteryStatisticReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_CheckSelectedLotteryParticipated_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckSelectedLotteryParticipatedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).CheckSelectedLotteryParticipated(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_CheckSelectedLotteryParticipated_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).CheckSelectedLotteryParticipated(ctx, req.(*CheckSelectedLotteryParticipatedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_CheckUserIsWon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserIsWonReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).CheckUserIsWon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_CheckUserIsWon_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).CheckUserIsWon(ctx, req.(*CheckUserIsWonReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_GetWonList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWonListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).GetWonList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_GetWonList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).GetWonList(ctx, req.(*GetWonListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_GetWonListCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWonListCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).GetWonListCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_GetWonListCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).GetWonListCount(ctx, req.(*GetWonListCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_GetWonListByLotteryId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWonListByLotteryIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).GetWonListByLotteryId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_GetWonListByLotteryId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).GetWonListByLotteryId(ctx, req.(*GetWonListByLotteryIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lottery_AddClockTaskRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddClockTaskRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LotteryServer).AddClockTaskRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lottery_AddClockTaskRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LotteryServer).AddClockTaskRecord(ctx, req.(*AddClockTaskRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Lottery_ServiceDesc is the grpc.ServiceDesc for Lottery service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Lottery_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.lottery",
	HandlerType: (*LotteryServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddLottery",
			Handler:    _Lottery_AddLottery_Handler,
		},
		{
			MethodName: "UpdateLottery",
			Handler:    _Lottery_UpdateLottery_Handler,
		},
		{
			MethodName: "DelLottery",
			Handler:    _Lottery_DelLottery_Handler,
		},
		{
			MethodName: "GetLotteryById",
			Handler:    _Lottery_GetLotteryById_Handler,
		},
		{
			MethodName: "SearchLottery",
			Handler:    _Lottery_SearchLottery_Handler,
		},
		{
			MethodName: "SetIsSelectedLottery",
			Handler:    _Lottery_SetIsSelectedLottery_Handler,
		},
		{
			MethodName: "LotteryDetail",
			Handler:    _Lottery_LotteryDetail_Handler,
		},
		{
			MethodName: "LotterySponsor",
			Handler:    _Lottery_LotterySponsor_Handler,
		},
		{
			MethodName: "AnnounceLottery",
			Handler:    _Lottery_AnnounceLottery_Handler,
		},
		{
			MethodName: "CheckUserCreatedLottery",
			Handler:    _Lottery_CheckUserCreatedLottery_Handler,
		},
		{
			MethodName: "CheckUserCreatedLotteryAndToday",
			Handler:    _Lottery_CheckUserCreatedLotteryAndToday_Handler,
		},
		{
			MethodName: "CheckUserCreatedLotteryAndThisWeek",
			Handler:    _Lottery_CheckUserCreatedLotteryAndThisWeek_Handler,
		},
		{
			MethodName: "GetLotteryListAfterLogin",
			Handler:    _Lottery_GetLotteryListAfterLogin_Handler,
		},
		{
			MethodName: "GetLotteryStatistic",
			Handler:    _Lottery_GetLotteryStatistic_Handler,
		},
		{
			MethodName: "GetLotteryListLastId",
			Handler:    _Lottery_GetLotteryListLastId_Handler,
		},
		{
			MethodName: "PublishLottery",
			Handler:    _Lottery_PublishLottery_Handler,
		},
		{
			MethodName: "GetLotteryPrizesListByUserId",
			Handler:    _Lottery_GetLotteryPrizesListByUserId_Handler,
		},
		{
			MethodName: "AddPrize",
			Handler:    _Lottery_AddPrize_Handler,
		},
		{
			MethodName: "UpdatePrize",
			Handler:    _Lottery_UpdatePrize_Handler,
		},
		{
			MethodName: "DelPrize",
			Handler:    _Lottery_DelPrize_Handler,
		},
		{
			MethodName: "GetPrizeById",
			Handler:    _Lottery_GetPrizeById_Handler,
		},
		{
			MethodName: "SearchPrize",
			Handler:    _Lottery_SearchPrize_Handler,
		},
		{
			MethodName: "GetPrizeListByLotteryId",
			Handler:    _Lottery_GetPrizeListByLotteryId_Handler,
		},
		{
			MethodName: "AddLotteryParticipation",
			Handler:    _Lottery_AddLotteryParticipation_Handler,
		},
		{
			MethodName: "SearchLotteryParticipation",
			Handler:    _Lottery_SearchLotteryParticipation_Handler,
		},
		{
			MethodName: "GetParticipationUserIdsByLotteryId",
			Handler:    _Lottery_GetParticipationUserIdsByLotteryId_Handler,
		},
		{
			MethodName: "CheckIsParticipated",
			Handler:    _Lottery_CheckIsParticipated_Handler,
		},
		{
			MethodName: "GetSelectedLotteryStatistic",
			Handler:    _Lottery_GetSelectedLotteryStatistic_Handler,
		},
		{
			MethodName: "CheckSelectedLotteryParticipated",
			Handler:    _Lottery_CheckSelectedLotteryParticipated_Handler,
		},
		{
			MethodName: "CheckUserIsWon",
			Handler:    _Lottery_CheckUserIsWon_Handler,
		},
		{
			MethodName: "GetWonList",
			Handler:    _Lottery_GetWonList_Handler,
		},
		{
			MethodName: "GetWonListCount",
			Handler:    _Lottery_GetWonListCount_Handler,
		},
		{
			MethodName: "GetWonListByLotteryId",
			Handler:    _Lottery_GetWonListByLotteryId_Handler,
		},
		{
			MethodName: "AddClockTaskRecord",
			Handler:    _Lottery_AddClockTaskRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "lottery.proto",
}
