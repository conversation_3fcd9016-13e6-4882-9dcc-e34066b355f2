syntax = "v1"

info(
    title: "用户投票记录"
    desc: "用户投票记录"
    author: "mike"
    email: "<EMAIL>"
    version: "v1"
)

type (
    CreateVoteRecordReq {
        LotteryId int64 `json:"lotteryId" validate:"required"`                                //抽奖id
        SelectedOption int64 `json:"selectedOption"`                                          //用户选择的投票选项的序号
    }
    CreateVoteRecordResp {
        Id int64 `json:"id"`
    }

    ViewVoteRecordDetailReq {
        LotteryId int64 `json:"lotteryId" validate:"required"` //抽奖id
    }
    VoteRecordDetail {
        OptionText string `json:"optionText"`        //投票选项名称
        SelectedOption int64 `json:"selectedOption"` //投票选项序号
        OptionImage string `json:"optionImage"`      //投票选项图片,默认为空【表示文字投票选项】
        VoteCount int64 `json:"voteCount"`           //选项已获投票数量
    }
    ViewVoteRecordDetailResp {
        VoteRecordDetails []*VoteRecordDetail `json:"voteRecordDetails"`
    }

    ViewUserVoteRecordDetailReq {
        LotteryId int64 `json:"lotteryId"` //抽奖id
    }
    VoteUserRecordDetail {
        LotteryId int64 `json:"lotteryId"`                                //抽奖id
        SelectedOption int64 `json:"selectedOption"`                      //用户选择的投票选项
    }
    ViewUserVoteRecordDetailResp {
        VoteUserRecordDetails []*VoteUserRecordDetail `json:"voteUserRecordDetails"`
    }
)
