{"swagger": "2.0", "info": {"title": "文件上传服务", "description": "文件上传服务", "version": "v1"}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/upload/v1/upload/add": {"post": {"summary": "文件上传", "operationId": "upload", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/UserUploadRes"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UserUploadReq"}}], "tags": ["upload"], "security": [{"apiKey": []}]}}}, "definitions": {"UserUploadReq": {"type": "object", "properties": {"file_name": {"type": "string"}, "ext": {"type": "string"}, "size": {"type": "integer", "format": "int64"}, "file_data": {"type": "array", "items": {"$ref": "#/definitions/byte"}}}, "title": "UserUploadReq"}, "UserUploadRes": {"type": "object", "properties": {"url": {"type": "string"}}, "title": "UserUploadRes", "required": ["url"]}}, "securityDefinitions": {"apiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Enter JWT Bearer token **_only_**", "name": "Authorization", "in": "header"}}}