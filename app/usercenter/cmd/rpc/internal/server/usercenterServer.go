// Code generated by goctl. DO NOT EDIT.
// Source: usercenter.proto

package server

import (
	"context"

	"looklook/app/usercenter/cmd/rpc/internal/logic"
	"looklook/app/usercenter/cmd/rpc/internal/svc"
	"looklook/app/usercenter/cmd/rpc/pb"
)

type UsercenterServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedUsercenterServer
}

func NewUsercenterServer(svcCtx *svc.ServiceContext) *UsercenterServer {
	return &UsercenterServer{
		svcCtx: svcCtx,
	}
}

// 自定义的服务
func (s *UsercenterServer) Login(ctx context.Context, in *pb.LoginReq) (*pb.LoginResp, error) {
	l := logic.NewLoginLogic(ctx, s.svcCtx)
	return l.Login(in)
}

func (s *UsercenterServer) Register(ctx context.Context, in *pb.RegisterReq) (*pb.RegisterResp, error) {
	l := logic.NewRegisterLogic(ctx, s.svcCtx)
	return l.Register(in)
}

func (s *UsercenterServer) GetUserInfo(ctx context.Context, in *pb.GetUserInfoReq) (*pb.GetUserInfoResp, error) {
	l := logic.NewGetUserInfoLogic(ctx, s.svcCtx)
	return l.GetUserInfo(in)
}

func (s *UsercenterServer) GetUserAuthByAuthKey(ctx context.Context, in *pb.GetUserAuthByAuthKeyReq) (*pb.GetUserAuthByAuthKeyResp, error) {
	l := logic.NewGetUserAuthByAuthKeyLogic(ctx, s.svcCtx)
	return l.GetUserAuthByAuthKey(in)
}

func (s *UsercenterServer) GetUserAuthByUserId(ctx context.Context, in *pb.GetUserAuthByUserIdReq) (*pb.GetUserAuthyUserIdResp, error) {
	l := logic.NewGetUserAuthByUserIdLogic(ctx, s.svcCtx)
	return l.GetUserAuthByUserId(in)
}

func (s *UsercenterServer) GenerateToken(ctx context.Context, in *pb.GenerateTokenReq) (*pb.GenerateTokenResp, error) {
	l := logic.NewGenerateTokenLogic(ctx, s.svcCtx)
	return l.GenerateToken(in)
}

func (s *UsercenterServer) UpdateUserBaseInfo(ctx context.Context, in *pb.UpdateUserBaseInfoReq) (*pb.UpdateUserBaseInfoResp, error) {
	l := logic.NewUpdateUserBaseInfoLogic(ctx, s.svcCtx)
	return l.UpdateUserBaseInfo(in)
}

func (s *UsercenterServer) SetAdmin(ctx context.Context, in *pb.SetAdminReq) (*pb.SetAdminResp, error) {
	l := logic.NewSetAdminLogic(ctx, s.svcCtx)
	return l.SetAdmin(in)
}

// -----------------------用户表-----------------------
func (s *UsercenterServer) GetUserInfoByUserIds(ctx context.Context, in *pb.GetUserInfoByUserIdsReq) (*pb.GetUserInfoByUserIdsResp, error) {
	l := logic.NewGetUserInfoByUserIdsLogic(ctx, s.svcCtx)
	return l.GetUserInfoByUserIds(in)
}

func (s *UsercenterServer) AddUser(ctx context.Context, in *pb.AddUserReq) (*pb.AddUserResp, error) {
	l := logic.NewAddUserLogic(ctx, s.svcCtx)
	return l.AddUser(in)
}

func (s *UsercenterServer) UpdateUser(ctx context.Context, in *pb.UpdateUserReq) (*pb.UpdateUserResp, error) {
	l := logic.NewUpdateUserLogic(ctx, s.svcCtx)
	return l.UpdateUser(in)
}

func (s *UsercenterServer) DelUser(ctx context.Context, in *pb.DelUserReq) (*pb.DelUserResp, error) {
	l := logic.NewDelUserLogic(ctx, s.svcCtx)
	return l.DelUser(in)
}

func (s *UsercenterServer) GetUserById(ctx context.Context, in *pb.GetUserByIdReq) (*pb.GetUserByIdResp, error) {
	l := logic.NewGetUserByIdLogic(ctx, s.svcCtx)
	return l.GetUserById(in)
}

func (s *UsercenterServer) SearchUser(ctx context.Context, in *pb.SearchUserReq) (*pb.SearchUserResp, error) {
	l := logic.NewSearchUserLogic(ctx, s.svcCtx)
	return l.SearchUser(in)
}

func (s *UsercenterServer) CheckIsAdmin(ctx context.Context, in *pb.CheckIsAdminReq) (*pb.CheckIsAdminResp, error) {
	l := logic.NewCheckIsAdminLogic(ctx, s.svcCtx)
	return l.CheckIsAdmin(in)
}

func (s *UsercenterServer) WxMiniRegister(ctx context.Context, in *pb.WXMiniRegisterReq) (*pb.WXMiniRegisterResp, error) {
	l := logic.NewWxMiniRegisterLogic(ctx, s.svcCtx)
	return l.WxMiniRegister(in)
}

// -----------------------用户收货地址表-----------------------
func (s *UsercenterServer) AddUserAddress(ctx context.Context, in *pb.AddUserAddressReq) (*pb.AddUserAddressResp, error) {
	l := logic.NewAddUserAddressLogic(ctx, s.svcCtx)
	return l.AddUserAddress(in)
}

func (s *UsercenterServer) UpdateUserAddress(ctx context.Context, in *pb.UpdateUserAddressReq) (*pb.UpdateUserAddressResp, error) {
	l := logic.NewUpdateUserAddressLogic(ctx, s.svcCtx)
	return l.UpdateUserAddress(in)
}

func (s *UsercenterServer) DelUserAddress(ctx context.Context, in *pb.DelUserAddressReq) (*pb.DelUserAddressResp, error) {
	l := logic.NewDelUserAddressLogic(ctx, s.svcCtx)
	return l.DelUserAddress(in)
}

func (s *UsercenterServer) GetUserAddressById(ctx context.Context, in *pb.GetUserAddressByIdReq) (*pb.GetUserAddressByIdResp, error) {
	l := logic.NewGetUserAddressByIdLogic(ctx, s.svcCtx)
	return l.GetUserAddressById(in)
}

func (s *UsercenterServer) SearchUserAddress(ctx context.Context, in *pb.SearchUserAddressReq) (*pb.SearchUserAddressResp, error) {
	l := logic.NewSearchUserAddressLogic(ctx, s.svcCtx)
	return l.SearchUserAddress(in)
}

// -----------------------用户授权表-----------------------
func (s *UsercenterServer) AddUserAuth(ctx context.Context, in *pb.AddUserAuthReq) (*pb.AddUserAuthResp, error) {
	l := logic.NewAddUserAuthLogic(ctx, s.svcCtx)
	return l.AddUserAuth(in)
}

func (s *UsercenterServer) UpdateUserAuth(ctx context.Context, in *pb.UpdateUserAuthReq) (*pb.UpdateUserAuthResp, error) {
	l := logic.NewUpdateUserAuthLogic(ctx, s.svcCtx)
	return l.UpdateUserAuth(in)
}

func (s *UsercenterServer) DelUserAuth(ctx context.Context, in *pb.DelUserAuthReq) (*pb.DelUserAuthResp, error) {
	l := logic.NewDelUserAuthLogic(ctx, s.svcCtx)
	return l.DelUserAuth(in)
}

func (s *UsercenterServer) GetUserAuthById(ctx context.Context, in *pb.GetUserAuthByIdReq) (*pb.GetUserAuthByIdResp, error) {
	l := logic.NewGetUserAuthByIdLogic(ctx, s.svcCtx)
	return l.GetUserAuthById(in)
}

func (s *UsercenterServer) SearchUserAuth(ctx context.Context, in *pb.SearchUserAuthReq) (*pb.SearchUserAuthResp, error) {
	l := logic.NewSearchUserAuthLogic(ctx, s.svcCtx)
	return l.SearchUserAuth(in)
}

// -----------------------抽奖发起人联系方式-----------------------
func (s *UsercenterServer) AddUserContact(ctx context.Context, in *pb.AddUserContactReq) (*pb.AddUserContactResp, error) {
	l := logic.NewAddUserContactLogic(ctx, s.svcCtx)
	return l.AddUserContact(in)
}

func (s *UsercenterServer) EditUserContact(ctx context.Context, in *pb.EditUserContactReq) (*pb.EditUserContactResp, error) {
	l := logic.NewEditUserContactLogic(ctx, s.svcCtx)
	return l.EditUserContact(in)
}

func (s *UsercenterServer) UpdateUserContact(ctx context.Context, in *pb.UpdateUserContactReq) (*pb.UpdateUserContactResp, error) {
	l := logic.NewUpdateUserContactLogic(ctx, s.svcCtx)
	return l.UpdateUserContact(in)
}

func (s *UsercenterServer) DelUserContact(ctx context.Context, in *pb.DelUserContactReq) (*pb.DelUserContactResp, error) {
	l := logic.NewDelUserContactLogic(ctx, s.svcCtx)
	return l.DelUserContact(in)
}

func (s *UsercenterServer) GetUserContactById(ctx context.Context, in *pb.GetUserContactByIdReq) (*pb.GetUserContactByIdResp, error) {
	l := logic.NewGetUserContactByIdLogic(ctx, s.svcCtx)
	return l.GetUserContactById(in)
}

func (s *UsercenterServer) SearchUserContact(ctx context.Context, in *pb.SearchUserContactReq) (*pb.SearchUserContactResp, error) {
	l := logic.NewSearchUserContactLogic(ctx, s.svcCtx)
	return l.SearchUserContact(in)
}

// -----------------------userShop-----------------------
func (s *UsercenterServer) AddUserShop(ctx context.Context, in *pb.AddUserShopReq) (*pb.AddUserShopResp, error) {
	l := logic.NewAddUserShopLogic(ctx, s.svcCtx)
	return l.AddUserShop(in)
}

func (s *UsercenterServer) UpdateUserShop(ctx context.Context, in *pb.UpdateUserShopReq) (*pb.UpdateUserShopResp, error) {
	l := logic.NewUpdateUserShopLogic(ctx, s.svcCtx)
	return l.UpdateUserShop(in)
}

func (s *UsercenterServer) DelUserShop(ctx context.Context, in *pb.DelUserShopReq) (*pb.DelUserShopResp, error) {
	l := logic.NewDelUserShopLogic(ctx, s.svcCtx)
	return l.DelUserShop(in)
}

func (s *UsercenterServer) GetUserShopById(ctx context.Context, in *pb.GetUserShopByIdReq) (*pb.GetUserShopByIdResp, error) {
	l := logic.NewGetUserShopByIdLogic(ctx, s.svcCtx)
	return l.GetUserShopById(in)
}

func (s *UsercenterServer) SearchUserShop(ctx context.Context, in *pb.SearchUserShopReq) (*pb.SearchUserShopResp, error) {
	l := logic.NewSearchUserShopLogic(ctx, s.svcCtx)
	return l.SearchUserShop(in)
}

func (s *UsercenterServer) AddUserSponsor(ctx context.Context, in *pb.AddUserSponsorReq) (*pb.AddUserSponsorResp, error) {
	l := logic.NewAddUserSponsorLogic(ctx, s.svcCtx)
	return l.AddUserSponsor(in)
}

func (s *UsercenterServer) UpdateUserSponsor(ctx context.Context, in *pb.UpdateUserSponsorReq) (*pb.UpdateUserSponsorResp, error) {
	l := logic.NewUpdateUserSponsorLogic(ctx, s.svcCtx)
	return l.UpdateUserSponsor(in)
}

func (s *UsercenterServer) DelUserSponsor(ctx context.Context, in *pb.DelUserSponsorReq) (*pb.DelUserSponsorResp, error) {
	l := logic.NewDelUserSponsorLogic(ctx, s.svcCtx)
	return l.DelUserSponsor(in)
}

func (s *UsercenterServer) GetUserSponsorById(ctx context.Context, in *pb.GetUserSponsorByIdReq) (*pb.GetUserSponsorByIdResp, error) {
	l := logic.NewGetUserSponsorByIdLogic(ctx, s.svcCtx)
	return l.GetUserSponsorById(in)
}

func (s *UsercenterServer) SearchUserSponsor(ctx context.Context, in *pb.SearchUserSponsorReq) (*pb.SearchUserSponsorResp, error) {
	l := logic.NewSearchUserSponsorLogic(ctx, s.svcCtx)
	return l.SearchUserSponsor(in)
}

func (s *UsercenterServer) SponsorDetail(ctx context.Context, in *pb.SponsorDetailReq) (*pb.SponsorDetailResp, error) {
	l := logic.NewSponsorDetailLogic(ctx, s.svcCtx)
	return l.SponsorDetail(in)
}

func (s *UsercenterServer) AddUserDynamic(ctx context.Context, in *pb.AddUserDynamicReq) (*pb.AddUserDynamicResp, error) {
	l := logic.NewAddUserDynamicLogic(ctx, s.svcCtx)
	return l.AddUserDynamic(in)
}

func (s *UsercenterServer) UpdateUserDynamic(ctx context.Context, in *pb.UpdateUserDynamicReq) (*pb.UpdateUserDynamicResp, error) {
	l := logic.NewUpdateUserDynamicLogic(ctx, s.svcCtx)
	return l.UpdateUserDynamic(in)
}

func (s *UsercenterServer) DelUserDynamic(ctx context.Context, in *pb.DelUserDynamicReq) (*pb.DelUserDynamicResp, error) {
	l := logic.NewDelUserDynamicLogic(ctx, s.svcCtx)
	return l.DelUserDynamic(in)
}

func (s *UsercenterServer) GetUserDynamicById(ctx context.Context, in *pb.GetUserDynamicByIdReq) (*pb.GetUserDynamicByIdResp, error) {
	l := logic.NewGetUserDynamicByIdLogic(ctx, s.svcCtx)
	return l.GetUserDynamicById(in)
}

func (s *UsercenterServer) SearchUserDynamic(ctx context.Context, in *pb.SearchUserDynamicReq) (*pb.SearchUserDynamicResp, error) {
	l := logic.NewSearchUserDynamicLogic(ctx, s.svcCtx)
	return l.SearchUserDynamic(in)
}

func (s *UsercenterServer) GetUserDynamicByUserId(ctx context.Context, in *pb.GetUserDynamicByUserIdReq) (*pb.GetUserDynamicByUserIdResp, error) {
	l := logic.NewGetUserDynamicByUserIdLogic(ctx, s.svcCtx)
	return l.GetUserDynamicByUserId(in)
}
