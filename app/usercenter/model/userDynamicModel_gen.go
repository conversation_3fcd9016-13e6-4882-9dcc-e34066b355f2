// Code generated by goctl. DO NOT EDIT.

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
	"looklook/common/globalkey"
)

var (
	userDynamicFieldNames          = builder.RawFieldNames(&UserDynamic{})
	userDynamicRows                = strings.Join(userDynamicFieldNames, ",")
	userDynamicRowsExpectAutoSet   = strings.Join(stringx.Remove(userDynamicFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), ",")
	userDynamicRowsWithPlaceHolder = strings.Join(stringx.Remove(userDynamicFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), "=?,") + "=?"

	cacheLooklookUsercenterUserDynamicIdPrefix = "cache:looklookUsercenter:userWinDynamicComment:id:"
)

type (
	userDynamicModel interface {
		Insert(ctx context.Context, data *UserDynamic) (sql.Result, error)
		TransInsert(ctx context.Context, session sqlx.Session, data *UserDynamic) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*UserDynamic, error)
		Update(ctx context.Context, data *UserDynamic) error
		List(ctx context.Context, page, limit int64) ([]*UserDynamic, error)
		TransUpdate(ctx context.Context, session sqlx.Session, data *UserDynamic) error
		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		SelectBuilder() squirrel.SelectBuilder
		FindSum(ctx context.Context, sumBuilder squirrel.SelectBuilder, field string) (float64, error)
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder, field string) (int64, error)
		FindAll(ctx context.Context, rowBuilder squirrel.SelectBuilder, orderBy string) ([]*UserDynamic, error)
		FindPageListByPage(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*UserDynamic, error)
		FindPageListByPageWithTotal(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*UserDynamic, int64, error)
		FindPageListByIdDESC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*UserDynamic, error)
		FindPageListByIdASC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*UserDynamic, error)
		Delete(ctx context.Context, id int64) error
	}

	defaultUserDynamicModel struct {
		sqlc.CachedConn
		table string
	}

	UserDynamic struct {
		Id         int64        `db:"id"`
		UserId     int64        `db:"user_id"`     // 发布动态用户id
		DynamicUrl string       `db:"dynamic_url"` // 发布动态地址
		Remark     string       `db:"remark"`      // 动态描述
		CreateTime time.Time    `db:"create_time"`
		UpdateTime time.Time    `db:"update_time"`
		DeleteTime sql.NullTime `db:"delete_time"`
	}
)

func newUserDynamicModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultUserDynamicModel {
	return &defaultUserDynamicModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`user_dynamic`",
	}
}

func (m *defaultUserDynamicModel) Delete(ctx context.Context, id int64) error {
	looklookUsercenterUserDynamicIdKey := fmt.Sprintf("%s%v", cacheLooklookUsercenterUserDynamicIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		return conn.ExecCtx(ctx, query, id)
	}, looklookUsercenterUserDynamicIdKey)
	return err
}

func (m *defaultUserDynamicModel) FindOne(ctx context.Context, id int64) (*UserDynamic, error) {
	looklookUsercenterUserDynamicIdKey := fmt.Sprintf("%s%v", cacheLooklookUsercenterUserDynamicIdPrefix, id)
	var resp UserDynamic
	err := m.QueryRowCtx(ctx, &resp, looklookUsercenterUserDynamicIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", userDynamicRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserDynamicModel) Insert(ctx context.Context, data *UserDynamic) (sql.Result, error) {
	looklookUsercenterUserDynamicIdKey := fmt.Sprintf("%s%v", cacheLooklookUsercenterUserDynamicIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, userDynamicRowsExpectAutoSet)
		return conn.ExecCtx(ctx, query, data.UserId, data.DynamicUrl, data.Remark, data.DeleteTime)
	}, looklookUsercenterUserDynamicIdKey)
	return ret, err
}

func (m *defaultUserDynamicModel) TransInsert(ctx context.Context, session sqlx.Session, data *UserDynamic) (sql.Result, error) {
	looklookUsercenterUserDynamicIdKey := fmt.Sprintf("%s%v", cacheLooklookUsercenterUserDynamicIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, userDynamicRowsExpectAutoSet)
		return session.ExecCtx(ctx, query, data.UserId, data.DynamicUrl, data.Remark, data.DeleteTime)
	}, looklookUsercenterUserDynamicIdKey)
	return ret, err
}
func (m *defaultUserDynamicModel) Update(ctx context.Context, data *UserDynamic) error {
	looklookUsercenterUserDynamicIdKey := fmt.Sprintf("%s%v", cacheLooklookUsercenterUserDynamicIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, userDynamicRowsWithPlaceHolder)
		return conn.ExecCtx(ctx, query, data.UserId, data.DynamicUrl, data.Remark, data.DeleteTime, data.Id)
	}, looklookUsercenterUserDynamicIdKey)
	return err
}

func (m *defaultUserDynamicModel) TransUpdate(ctx context.Context, session sqlx.Session, data *UserDynamic) error {
	looklookUsercenterUserDynamicIdKey := fmt.Sprintf("%s%v", cacheLooklookUsercenterUserDynamicIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, userDynamicRowsWithPlaceHolder)
		return session.ExecCtx(ctx, query, data.UserId, data.DynamicUrl, data.Remark, data.DeleteTime, data.Id)
	}, looklookUsercenterUserDynamicIdKey)
	return err
}

func (m *defaultUserDynamicModel) List(ctx context.Context, page, limit int64) ([]*UserDynamic, error) {
	query := fmt.Sprintf("select %s from %s limit ?,?", userDynamicRows, m.table)
	var resp []*UserDynamic
	//err := m.conn.QueryRowsCtx(ctx, &resp, query, (page-1)*limit, limit)
	err := m.QueryRowsNoCacheCtx(ctx, &resp, query, (page-1)*limit, limit)
	return resp, err
}

func (m *defaultUserDynamicModel) Trans(ctx context.Context, fn func(ctx context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, func(ctx context.Context, session sqlx.Session) error {
		return fn(ctx, session)
	})
}

func (m *defaultUserDynamicModel) FindSum(ctx context.Context, builder squirrel.SelectBuilder, field string) (float64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindSum Least One Field"), "FindSum Least One Field")
	}

	builder = builder.Columns("IFNULL(SUM(" + field + "),0)")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp float64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultUserDynamicModel) FindCount(ctx context.Context, builder squirrel.SelectBuilder, field string) (int64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindCount Least One Field"), "FindCount Least One Field")
	}

	builder = builder.Columns("COUNT(" + field + ")")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultUserDynamicModel) FindAll(ctx context.Context, builder squirrel.SelectBuilder, orderBy string) ([]*UserDynamic, error) {

	builder = builder.Columns(userDynamicRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*UserDynamic
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultUserDynamicModel) FindPageListByPage(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*UserDynamic, error) {

	builder = builder.Columns(userDynamicRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*UserDynamic
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultUserDynamicModel) FindPageListByPageWithTotal(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*UserDynamic, int64, error) {

	total, err := m.FindCount(ctx, builder, "id")
	if err != nil {
		return nil, 0, err
	}

	builder = builder.Columns(userDynamicRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, total, err
	}

	var resp []*UserDynamic
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, total, nil
	default:
		return nil, total, err
	}
}

func (m *defaultUserDynamicModel) FindPageListByIdDESC(ctx context.Context, builder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*UserDynamic, error) {

	builder = builder.Columns(userDynamicRows)

	if preMinId > 0 {
		builder = builder.Where(" id < ? ", preMinId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id DESC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*UserDynamic
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultUserDynamicModel) FindPageListByIdASC(ctx context.Context, builder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*UserDynamic, error) {

	builder = builder.Columns(userDynamicRows)

	if preMaxId > 0 {
		builder = builder.Where(" id > ? ", preMaxId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id ASC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*UserDynamic
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultUserDynamicModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select().From(m.table)
}

func (m *defaultUserDynamicModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheLooklookUsercenterUserDynamicIdPrefix, primary)
}

func (m *defaultUserDynamicModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", userDynamicRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultUserDynamicModel) tableName() string {
	return m.table
}
