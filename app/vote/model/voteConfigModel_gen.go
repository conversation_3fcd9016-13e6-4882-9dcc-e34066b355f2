// Code generated by goctl. DO NOT EDIT.

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
	"looklook/common/globalkey"
)

var (
	voteConfigFieldNames          = builder.RawFieldNames(&VoteConfig{})
	voteConfigRows                = strings.Join(voteConfigFieldNames, ",")
	voteConfigRowsExpectAutoSet   = strings.Join(stringx.Remove(voteConfigFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), ",")
	voteConfigRowsWithPlaceHolder = strings.Join(stringx.Remove(voteConfigFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), "=?,") + "=?"

	cacheVoteVoteConfigIdPrefix = "cache:vote:voteConfig:id:"
)

type (
	voteConfigModel interface {
		Insert(ctx context.Context, data *VoteConfig) (sql.Result, error)
		TransInsert(ctx context.Context, session sqlx.Session, data *VoteConfig) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*VoteConfig, error)
		Update(ctx context.Context, data *VoteConfig) error
		List(ctx context.Context, page, limit int64) ([]*VoteConfig, error)
		TransUpdate(ctx context.Context, session sqlx.Session, data *VoteConfig) error
		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		SelectBuilder() squirrel.SelectBuilder
		FindSum(ctx context.Context, sumBuilder squirrel.SelectBuilder, field string) (float64, error)
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder, field string) (int64, error)
		FindAll(ctx context.Context, rowBuilder squirrel.SelectBuilder, orderBy string) ([]*VoteConfig, error)
		FindPageListByPage(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*VoteConfig, error)
		FindPageListByPageWithTotal(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*VoteConfig, int64, error)
		FindPageListByIdDESC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*VoteConfig, error)
		FindPageListByIdASC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*VoteConfig, error)
		Delete(ctx context.Context, id int64) error
	}

	defaultVoteConfigModel struct {
		sqlc.CachedConn
		table string
	}

	VoteConfig struct {
		Id         int64          `db:"id"`
		UserId     int64          `db:"user_id"`     // 用户id
		LotteryId  int64          `db:"lottery_id"`  // 抽奖ID
		EnableVote int64          `db:"enable_vote"` // 是否启用投票功能 1是 0否
		VoteConfig sql.NullString `db:"vote_config"` // 投票配置字段说明: {"title": "投票标题", "description": "投票描述【非必填】", "winner_selection": "中奖者设置：1从所有投票者中抽取 2从票数最多的一方中抽取", "type": "投票类型：1单选 2多选", "min_votes": "最小投票范围", "max_votes": "最大投票范围", "options": [{"text": "张三", "image": "path/to/zhangsan.jpg"}, {"text": "李四", "image": "path/to/lisi.jpg"}, {"text": "王五", "image": "path/to/wangwu.jpg"}]}
		DelState   int64          `db:"del_state"`
		CreateTime time.Time      `db:"create_time"`
		UpdateTime time.Time      `db:"update_time"`
	}
)

func newVoteConfigModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultVoteConfigModel {
	return &defaultVoteConfigModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`vote_config`",
	}
}

func (m *defaultVoteConfigModel) Delete(ctx context.Context, id int64) error {
	voteVoteConfigIdKey := fmt.Sprintf("%s%v", cacheVoteVoteConfigIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		return conn.ExecCtx(ctx, query, id)
	}, voteVoteConfigIdKey)
	return err
}

func (m *defaultVoteConfigModel) FindOne(ctx context.Context, id int64) (*VoteConfig, error) {
	voteVoteConfigIdKey := fmt.Sprintf("%s%v", cacheVoteVoteConfigIdPrefix, id)
	var resp VoteConfig
	err := m.QueryRowCtx(ctx, &resp, voteVoteConfigIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", voteConfigRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultVoteConfigModel) Insert(ctx context.Context, data *VoteConfig) (sql.Result, error) {
	voteVoteConfigIdKey := fmt.Sprintf("%s%v", cacheVoteVoteConfigIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?)", m.table, voteConfigRowsExpectAutoSet)
		return conn.ExecCtx(ctx, query, data.UserId, data.LotteryId, data.EnableVote, data.VoteConfig, data.DelState)
	}, voteVoteConfigIdKey)
	return ret, err
}

func (m *defaultVoteConfigModel) TransInsert(ctx context.Context, session sqlx.Session, data *VoteConfig) (sql.Result, error) {
	voteVoteConfigIdKey := fmt.Sprintf("%s%v", cacheVoteVoteConfigIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?)", m.table, voteConfigRowsExpectAutoSet)
		return session.ExecCtx(ctx, query, data.UserId, data.LotteryId, data.EnableVote, data.VoteConfig, data.DelState)
	}, voteVoteConfigIdKey)
	return ret, err
}
func (m *defaultVoteConfigModel) Update(ctx context.Context, data *VoteConfig) error {
	voteVoteConfigIdKey := fmt.Sprintf("%s%v", cacheVoteVoteConfigIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, voteConfigRowsWithPlaceHolder)
		return conn.ExecCtx(ctx, query, data.UserId, data.LotteryId, data.EnableVote, data.VoteConfig, data.DelState, data.Id)
	}, voteVoteConfigIdKey)
	return err
}

func (m *defaultVoteConfigModel) TransUpdate(ctx context.Context, session sqlx.Session, data *VoteConfig) error {
	voteVoteConfigIdKey := fmt.Sprintf("%s%v", cacheVoteVoteConfigIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, voteConfigRowsWithPlaceHolder)
		return session.ExecCtx(ctx, query, data.UserId, data.LotteryId, data.EnableVote, data.VoteConfig, data.DelState, data.Id)
	}, voteVoteConfigIdKey)
	return err
}

func (m *defaultVoteConfigModel) List(ctx context.Context, page, limit int64) ([]*VoteConfig, error) {
	query := fmt.Sprintf("select %s from %s limit ?,?", voteConfigRows, m.table)
	var resp []*VoteConfig
	//err := m.conn.QueryRowsCtx(ctx, &resp, query, (page-1)*limit, limit)
	err := m.QueryRowsNoCacheCtx(ctx, &resp, query, (page-1)*limit, limit)
	return resp, err
}

func (m *defaultVoteConfigModel) Trans(ctx context.Context, fn func(ctx context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, func(ctx context.Context, session sqlx.Session) error {
		return fn(ctx, session)
	})
}

func (m *defaultVoteConfigModel) FindSum(ctx context.Context, builder squirrel.SelectBuilder, field string) (float64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindSum Least One Field"), "FindSum Least One Field")
	}

	builder = builder.Columns("IFNULL(SUM(" + field + "),0)")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp float64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultVoteConfigModel) FindCount(ctx context.Context, builder squirrel.SelectBuilder, field string) (int64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindCount Least One Field"), "FindCount Least One Field")
	}

	builder = builder.Columns("COUNT(" + field + ")")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultVoteConfigModel) FindAll(ctx context.Context, builder squirrel.SelectBuilder, orderBy string) ([]*VoteConfig, error) {

	builder = builder.Columns(voteConfigRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*VoteConfig
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultVoteConfigModel) FindPageListByPage(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*VoteConfig, error) {

	builder = builder.Columns(voteConfigRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*VoteConfig
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultVoteConfigModel) FindPageListByPageWithTotal(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*VoteConfig, int64, error) {

	total, err := m.FindCount(ctx, builder, "id")
	if err != nil {
		return nil, 0, err
	}

	builder = builder.Columns(voteConfigRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, total, err
	}

	var resp []*VoteConfig
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, total, nil
	default:
		return nil, total, err
	}
}

func (m *defaultVoteConfigModel) FindPageListByIdDESC(ctx context.Context, builder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*VoteConfig, error) {

	builder = builder.Columns(voteConfigRows)

	if preMinId > 0 {
		builder = builder.Where(" id < ? ", preMinId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id DESC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*VoteConfig
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultVoteConfigModel) FindPageListByIdASC(ctx context.Context, builder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*VoteConfig, error) {

	builder = builder.Columns(voteConfigRows)

	if preMaxId > 0 {
		builder = builder.Where(" id > ? ", preMaxId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id ASC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*VoteConfig
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultVoteConfigModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select().From(m.table)
}

func (m *defaultVoteConfigModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheVoteVoteConfigIdPrefix, primary)
}

func (m *defaultVoteConfigModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", voteConfigRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultVoteConfigModel) tableName() string {
	return m.table
}
