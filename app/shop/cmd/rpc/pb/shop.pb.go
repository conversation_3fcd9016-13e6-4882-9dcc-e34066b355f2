// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v3.19.4
// source: shop.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 商品基本信息
type GoodsInfo struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	CategoryName         string                 `protobuf:"bytes,1,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty"`
	CouponRemainQuantity int64                  `protobuf:"varint,2,opt,name=coupon_remain_quantity,json=couponRemainQuantity,proto3" json:"coupon_remain_quantity,omitempty"`
	ExtraCouponAmount    int64                  `protobuf:"varint,3,opt,name=extra_coupon_amount,json=extraCouponAmount,proto3" json:"extra_coupon_amount,omitempty"`
	PromotionRate        int64                  `protobuf:"varint,4,opt,name=promotion_rate,json=promotionRate,proto3" json:"promotion_rate,omitempty"`
	SubsidyGoodsType     int64                  `protobuf:"varint,5,opt,name=subsidy_goods_type,json=subsidyGoodsType,proto3" json:"subsidy_goods_type,omitempty"`
	CatIds               []int64                `protobuf:"varint,6,rep,packed,name=cat_ids,json=catIds,proto3" json:"cat_ids,omitempty"`
	CouponMinOrderAmount int64                  `protobuf:"varint,7,opt,name=coupon_min_order_amount,json=couponMinOrderAmount,proto3" json:"coupon_min_order_amount,omitempty"`
	CategoryId           string                 `protobuf:"bytes,8,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	MallId               int64                  `protobuf:"varint,9,opt,name=mall_id,json=mallId,proto3" json:"mall_id,omitempty"`
	SubsidyAmount        int64                  `protobuf:"varint,10,opt,name=subsidy_amount,json=subsidyAmount,proto3" json:"subsidy_amount,omitempty"`
	CouponPrice          int64                  `protobuf:"varint,11,opt,name=coupon_price,json=couponPrice,proto3" json:"coupon_price,omitempty"`
	MallName             string                 `protobuf:"bytes,12,opt,name=mall_name,json=mallName,proto3" json:"mall_name,omitempty"`
	CouponTotalQuantity  int64                  `protobuf:"varint,13,opt,name=coupon_total_quantity,json=couponTotalQuantity,proto3" json:"coupon_total_quantity,omitempty"`
	MarketFee            int64                  `protobuf:"varint,14,opt,name=market_fee,json=marketFee,proto3" json:"market_fee,omitempty"`
	MerchantType         string                 `protobuf:"bytes,15,opt,name=merchant_type,json=merchantType,proto3" json:"merchant_type,omitempty"`
	LgstTxt              string                 `protobuf:"bytes,16,opt,name=lgst_txt,json=lgstTxt,proto3" json:"lgst_txt,omitempty"`
	GoodsName            string                 `protobuf:"bytes,17,opt,name=goods_name,json=goodsName,proto3" json:"goods_name,omitempty"`
	SalesTip             string                 `protobuf:"bytes,18,opt,name=sales_tip,json=salesTip,proto3" json:"sales_tip,omitempty"`
	HasMaterial          bool                   `protobuf:"varint,19,opt,name=has_material,json=hasMaterial,proto3" json:"has_material,omitempty"`
	GoodsId              int64                  `protobuf:"varint,20,opt,name=goods_id,json=goodsId,proto3" json:"goods_id,omitempty"`
	PredictPromotionRate int64                  `protobuf:"varint,21,opt,name=predict_promotion_rate,json=predictPromotionRate,proto3" json:"predict_promotion_rate,omitempty"`
	DescTxt              string                 `protobuf:"bytes,22,opt,name=desc_txt,json=descTxt,proto3" json:"desc_txt,omitempty"`
	GoodsDesc            string                 `protobuf:"bytes,23,opt,name=goods_desc,json=goodsDesc,proto3" json:"goods_desc,omitempty"`
	OptName              string                 `protobuf:"bytes,24,opt,name=opt_name,json=optName,proto3" json:"opt_name,omitempty"`
	RealtimeSalesTip     string                 `protobuf:"bytes,25,opt,name=realtime_sales_tip,json=realtimeSalesTip,proto3" json:"realtime_sales_tip,omitempty"`
	ShareRate            int64                  `protobuf:"varint,26,opt,name=share_rate,json=shareRate,proto3" json:"share_rate,omitempty"`
	GoodsThumbnailUrl    string                 `protobuf:"bytes,27,opt,name=goods_thumbnail_url,json=goodsThumbnailUrl,proto3" json:"goods_thumbnail_url,omitempty"`
	OptIds               []int64                `protobuf:"varint,28,rep,packed,name=opt_ids,json=optIds,proto3" json:"opt_ids,omitempty"`
	OptId                string                 `protobuf:"bytes,29,opt,name=opt_id,json=optId,proto3" json:"opt_id,omitempty"`
	SearchId             string                 `protobuf:"bytes,30,opt,name=search_id,json=searchId,proto3" json:"search_id,omitempty"`
	GoodsImageUrl        string                 `protobuf:"bytes,31,opt,name=goods_image_url,json=goodsImageUrl,proto3" json:"goods_image_url,omitempty"`
	ActivityTags         []int64                `protobuf:"varint,32,rep,packed,name=activity_tags,json=activityTags,proto3" json:"activity_tags,omitempty"`
	HasCoupon            bool                   `protobuf:"varint,33,opt,name=has_coupon,json=hasCoupon,proto3" json:"has_coupon,omitempty"`
	MinNormalPrice       int64                  `protobuf:"varint,34,opt,name=min_normal_price,json=minNormalPrice,proto3" json:"min_normal_price,omitempty"`
	ServTxt              string                 `protobuf:"bytes,35,opt,name=serv_txt,json=servTxt,proto3" json:"serv_txt,omitempty"`
	UnifiedTags          []string               `protobuf:"bytes,36,rep,name=unified_tags,json=unifiedTags,proto3" json:"unified_tags,omitempty"`
	CouponStartTime      int64                  `protobuf:"varint,37,opt,name=coupon_start_time,json=couponStartTime,proto3" json:"coupon_start_time,omitempty"`
	MinGroupPrice        int64                  `protobuf:"varint,38,opt,name=min_group_price,json=minGroupPrice,proto3" json:"min_group_price,omitempty"`
	CouponDiscount       int64                  `protobuf:"varint,39,opt,name=coupon_discount,json=couponDiscount,proto3" json:"coupon_discount,omitempty"`
	GoodsSign            string                 `protobuf:"bytes,40,opt,name=goods_sign,json=goodsSign,proto3" json:"goods_sign,omitempty"`
	CouponEndTime        int64                  `protobuf:"varint,41,opt,name=coupon_end_time,json=couponEndTime,proto3" json:"coupon_end_time,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *GoodsInfo) Reset() {
	*x = GoodsInfo{}
	mi := &file_shop_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsInfo) ProtoMessage() {}

func (x *GoodsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_shop_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsInfo.ProtoReflect.Descriptor instead.
func (*GoodsInfo) Descriptor() ([]byte, []int) {
	return file_shop_proto_rawDescGZIP(), []int{0}
}

func (x *GoodsInfo) GetCategoryName() string {
	if x != nil {
		return x.CategoryName
	}
	return ""
}

func (x *GoodsInfo) GetCouponRemainQuantity() int64 {
	if x != nil {
		return x.CouponRemainQuantity
	}
	return 0
}

func (x *GoodsInfo) GetExtraCouponAmount() int64 {
	if x != nil {
		return x.ExtraCouponAmount
	}
	return 0
}

func (x *GoodsInfo) GetPromotionRate() int64 {
	if x != nil {
		return x.PromotionRate
	}
	return 0
}

func (x *GoodsInfo) GetSubsidyGoodsType() int64 {
	if x != nil {
		return x.SubsidyGoodsType
	}
	return 0
}

func (x *GoodsInfo) GetCatIds() []int64 {
	if x != nil {
		return x.CatIds
	}
	return nil
}

func (x *GoodsInfo) GetCouponMinOrderAmount() int64 {
	if x != nil {
		return x.CouponMinOrderAmount
	}
	return 0
}

func (x *GoodsInfo) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *GoodsInfo) GetMallId() int64 {
	if x != nil {
		return x.MallId
	}
	return 0
}

func (x *GoodsInfo) GetSubsidyAmount() int64 {
	if x != nil {
		return x.SubsidyAmount
	}
	return 0
}

func (x *GoodsInfo) GetCouponPrice() int64 {
	if x != nil {
		return x.CouponPrice
	}
	return 0
}

func (x *GoodsInfo) GetMallName() string {
	if x != nil {
		return x.MallName
	}
	return ""
}

func (x *GoodsInfo) GetCouponTotalQuantity() int64 {
	if x != nil {
		return x.CouponTotalQuantity
	}
	return 0
}

func (x *GoodsInfo) GetMarketFee() int64 {
	if x != nil {
		return x.MarketFee
	}
	return 0
}

func (x *GoodsInfo) GetMerchantType() string {
	if x != nil {
		return x.MerchantType
	}
	return ""
}

func (x *GoodsInfo) GetLgstTxt() string {
	if x != nil {
		return x.LgstTxt
	}
	return ""
}

func (x *GoodsInfo) GetGoodsName() string {
	if x != nil {
		return x.GoodsName
	}
	return ""
}

func (x *GoodsInfo) GetSalesTip() string {
	if x != nil {
		return x.SalesTip
	}
	return ""
}

func (x *GoodsInfo) GetHasMaterial() bool {
	if x != nil {
		return x.HasMaterial
	}
	return false
}

func (x *GoodsInfo) GetGoodsId() int64 {
	if x != nil {
		return x.GoodsId
	}
	return 0
}

func (x *GoodsInfo) GetPredictPromotionRate() int64 {
	if x != nil {
		return x.PredictPromotionRate
	}
	return 0
}

func (x *GoodsInfo) GetDescTxt() string {
	if x != nil {
		return x.DescTxt
	}
	return ""
}

func (x *GoodsInfo) GetGoodsDesc() string {
	if x != nil {
		return x.GoodsDesc
	}
	return ""
}

func (x *GoodsInfo) GetOptName() string {
	if x != nil {
		return x.OptName
	}
	return ""
}

func (x *GoodsInfo) GetRealtimeSalesTip() string {
	if x != nil {
		return x.RealtimeSalesTip
	}
	return ""
}

func (x *GoodsInfo) GetShareRate() int64 {
	if x != nil {
		return x.ShareRate
	}
	return 0
}

func (x *GoodsInfo) GetGoodsThumbnailUrl() string {
	if x != nil {
		return x.GoodsThumbnailUrl
	}
	return ""
}

func (x *GoodsInfo) GetOptIds() []int64 {
	if x != nil {
		return x.OptIds
	}
	return nil
}

func (x *GoodsInfo) GetOptId() string {
	if x != nil {
		return x.OptId
	}
	return ""
}

func (x *GoodsInfo) GetSearchId() string {
	if x != nil {
		return x.SearchId
	}
	return ""
}

func (x *GoodsInfo) GetGoodsImageUrl() string {
	if x != nil {
		return x.GoodsImageUrl
	}
	return ""
}

func (x *GoodsInfo) GetActivityTags() []int64 {
	if x != nil {
		return x.ActivityTags
	}
	return nil
}

func (x *GoodsInfo) GetHasCoupon() bool {
	if x != nil {
		return x.HasCoupon
	}
	return false
}

func (x *GoodsInfo) GetMinNormalPrice() int64 {
	if x != nil {
		return x.MinNormalPrice
	}
	return 0
}

func (x *GoodsInfo) GetServTxt() string {
	if x != nil {
		return x.ServTxt
	}
	return ""
}

func (x *GoodsInfo) GetUnifiedTags() []string {
	if x != nil {
		return x.UnifiedTags
	}
	return nil
}

func (x *GoodsInfo) GetCouponStartTime() int64 {
	if x != nil {
		return x.CouponStartTime
	}
	return 0
}

func (x *GoodsInfo) GetMinGroupPrice() int64 {
	if x != nil {
		return x.MinGroupPrice
	}
	return 0
}

func (x *GoodsInfo) GetCouponDiscount() int64 {
	if x != nil {
		return x.CouponDiscount
	}
	return 0
}

func (x *GoodsInfo) GetGoodsSign() string {
	if x != nil {
		return x.GoodsSign
	}
	return ""
}

func (x *GoodsInfo) GetCouponEndTime() int64 {
	if x != nil {
		return x.CouponEndTime
	}
	return 0
}

// 商品详情
type GoodsDetail struct {
	state                       protoimpl.MessageState `protogen:"open.v1"`
	Id                          int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CategoryName                string                 `protobuf:"bytes,2,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty"`
	CouponRemainQuantity        int64                  `protobuf:"varint,3,opt,name=coupon_remain_quantity,json=couponRemainQuantity,proto3" json:"coupon_remain_quantity,omitempty"`
	PromotionRate               int64                  `protobuf:"varint,4,opt,name=promotion_rate,json=promotionRate,proto3" json:"promotion_rate,omitempty"`
	SubsidyGoodsType            int64                  `protobuf:"varint,5,opt,name=subsidy_goods_type,json=subsidyGoodsType,proto3" json:"subsidy_goods_type,omitempty"`
	ServiceTags                 []int64                `protobuf:"varint,6,rep,packed,name=service_tags,json=serviceTags,proto3" json:"service_tags,omitempty"`
	MallId                      int64                  `protobuf:"varint,7,opt,name=mall_id,json=mallId,proto3" json:"mall_id,omitempty"`
	MallName                    string                 `protobuf:"bytes,8,opt,name=mall_name,json=mallName,proto3" json:"mall_name,omitempty"`
	MallCouponEndTime           int64                  `protobuf:"varint,9,opt,name=mall_coupon_end_time,json=mallCouponEndTime,proto3" json:"mall_coupon_end_time,omitempty"`
	LgstTxt                     string                 `protobuf:"bytes,10,opt,name=lgst_txt,json=lgstTxt,proto3" json:"lgst_txt,omitempty"`
	GoodsName                   string                 `protobuf:"bytes,11,opt,name=goods_name,json=goodsName,proto3" json:"goods_name,omitempty"`
	GoodsGalleryUrls            []string               `protobuf:"bytes,12,rep,name=goods_gallery_urls,json=goodsGalleryUrls,proto3" json:"goods_gallery_urls,omitempty"`
	GoodsId                     int64                  `protobuf:"varint,13,opt,name=goods_id,json=goodsId,proto3" json:"goods_id,omitempty"`
	BrandName                   string                 `protobuf:"bytes,14,opt,name=brand_name,json=brandName,proto3" json:"brand_name,omitempty"`
	PredictPromotionRate        int64                  `protobuf:"varint,15,opt,name=predict_promotion_rate,json=predictPromotionRate,proto3" json:"predict_promotion_rate,omitempty"`
	GoodsDesc                   string                 `protobuf:"bytes,16,opt,name=goods_desc,json=goodsDesc,proto3" json:"goods_desc,omitempty"`
	OptName                     string                 `protobuf:"bytes,17,opt,name=opt_name,json=optName,proto3" json:"opt_name,omitempty"`
	ShareRate                   int64                  `protobuf:"varint,18,opt,name=share_rate,json=shareRate,proto3" json:"share_rate,omitempty"`
	OptIds                      []int64                `protobuf:"varint,19,rep,packed,name=opt_ids,json=optIds,proto3" json:"opt_ids,omitempty"`
	GoodsImageUrl               string                 `protobuf:"bytes,20,opt,name=goods_image_url,json=goodsImageUrl,proto3" json:"goods_image_url,omitempty"`
	MallImgUrl                  string                 `protobuf:"bytes,21,opt,name=mall_img_url,json=mallImgUrl,proto3" json:"mall_img_url,omitempty"`
	HasMallCoupon               bool                   `protobuf:"varint,22,opt,name=has_mall_coupon,json=hasMallCoupon,proto3" json:"has_mall_coupon,omitempty"`
	UnifiedTags                 []string               `protobuf:"bytes,23,rep,name=unified_tags,json=unifiedTags,proto3" json:"unified_tags,omitempty"`
	VideoUrls                   []string               `protobuf:"bytes,24,rep,name=video_urls,json=videoUrls,proto3" json:"video_urls,omitempty"`
	CouponStartTime             int64                  `protobuf:"varint,25,opt,name=coupon_start_time,json=couponStartTime,proto3" json:"coupon_start_time,omitempty"`
	MinGroupPrice               int64                  `protobuf:"varint,26,opt,name=min_group_price,json=minGroupPrice,proto3" json:"min_group_price,omitempty"`
	CouponDiscount              int64                  `protobuf:"varint,27,opt,name=coupon_discount,json=couponDiscount,proto3" json:"coupon_discount,omitempty"`
	CouponEndTime               int64                  `protobuf:"varint,28,opt,name=coupon_end_time,json=couponEndTime,proto3" json:"coupon_end_time,omitempty"`
	ZsDuoId                     int64                  `protobuf:"varint,29,opt,name=zs_duo_id,json=zsDuoId,proto3" json:"zs_duo_id,omitempty"`
	MallCouponRemainQuantity    int64                  `protobuf:"varint,30,opt,name=mall_coupon_remain_quantity,json=mallCouponRemainQuantity,proto3" json:"mall_coupon_remain_quantity,omitempty"`
	PlanType                    int64                  `protobuf:"varint,31,opt,name=plan_type,json=planType,proto3" json:"plan_type,omitempty"`
	ExtraCouponAmount           int64                  `protobuf:"varint,32,opt,name=extra_coupon_amount,json=extraCouponAmount,proto3" json:"extra_coupon_amount,omitempty"`
	MaterialList                []string               `protobuf:"bytes,33,rep,name=material_list,json=materialList,proto3" json:"material_list,omitempty"`
	CatIds                      []int64                `protobuf:"varint,34,rep,packed,name=cat_ids,json=catIds,proto3" json:"cat_ids,omitempty"`
	CouponMinOrderAmount        int64                  `protobuf:"varint,35,opt,name=coupon_min_order_amount,json=couponMinOrderAmount,proto3" json:"coupon_min_order_amount,omitempty"`
	CategoryId                  int64                  `protobuf:"varint,36,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	MallCouponDiscountPct       int64                  `protobuf:"varint,37,opt,name=mall_coupon_discount_pct,json=mallCouponDiscountPct,proto3" json:"mall_coupon_discount_pct,omitempty"`
	CatId                       int64                  `protobuf:"varint,38,opt,name=cat_id,json=catId,proto3" json:"cat_id,omitempty"`
	CouponTotalQuantity         int64                  `protobuf:"varint,39,opt,name=coupon_total_quantity,json=couponTotalQuantity,proto3" json:"coupon_total_quantity,omitempty"`
	MallCouponMinOrderAmount    int64                  `protobuf:"varint,40,opt,name=mall_coupon_min_order_amount,json=mallCouponMinOrderAmount,proto3" json:"mall_coupon_min_order_amount,omitempty"`
	MerchantType                int64                  `protobuf:"varint,41,opt,name=merchant_type,json=merchantType,proto3" json:"merchant_type,omitempty"`
	SalesTip                    string                 `protobuf:"bytes,42,opt,name=sales_tip,json=salesTip,proto3" json:"sales_tip,omitempty"`
	IsMultiGroup                bool                   `protobuf:"varint,43,opt,name=is_multi_group,json=isMultiGroup,proto3" json:"is_multi_group,omitempty"`
	OnlySceneAuth               bool                   `protobuf:"varint,44,opt,name=only_scene_auth,json=onlySceneAuth,proto3" json:"only_scene_auth,omitempty"`
	DescTxt                     string                 `protobuf:"bytes,45,opt,name=desc_txt,json=descTxt,proto3" json:"desc_txt,omitempty"`
	GoodsThumbnailUrl           string                 `protobuf:"bytes,46,opt,name=goods_thumbnail_url,json=goodsThumbnailUrl,proto3" json:"goods_thumbnail_url,omitempty"`
	OptId                       int64                  `protobuf:"varint,47,opt,name=opt_id,json=optId,proto3" json:"opt_id,omitempty"`
	ActivityTags                []int64                `protobuf:"varint,48,rep,packed,name=activity_tags,json=activityTags,proto3" json:"activity_tags,omitempty"`
	HasCoupon                   bool                   `protobuf:"varint,49,opt,name=has_coupon,json=hasCoupon,proto3" json:"has_coupon,omitempty"`
	MinNormalPrice              int64                  `protobuf:"varint,50,opt,name=min_normal_price,json=minNormalPrice,proto3" json:"min_normal_price,omitempty"`
	MallCouponStartTime         int64                  `protobuf:"varint,51,opt,name=mall_coupon_start_time,json=mallCouponStartTime,proto3" json:"mall_coupon_start_time,omitempty"`
	ServTxt                     string                 `protobuf:"bytes,52,opt,name=serv_txt,json=servTxt,proto3" json:"serv_txt,omitempty"`
	MallCouponTotalQuantity     int64                  `protobuf:"varint,53,opt,name=mall_coupon_total_quantity,json=mallCouponTotalQuantity,proto3" json:"mall_coupon_total_quantity,omitempty"`
	MallCouponMaxDiscountAmount int64                  `protobuf:"varint,54,opt,name=mall_coupon_max_discount_amount,json=mallCouponMaxDiscountAmount,proto3" json:"mall_coupon_max_discount_amount,omitempty"`
	MallCps                     int64                  `protobuf:"varint,55,opt,name=mall_cps,json=mallCps,proto3" json:"mall_cps,omitempty"`
	GoodsSign                   string                 `protobuf:"bytes,56,opt,name=goods_sign,json=goodsSign,proto3" json:"goods_sign,omitempty"`
	unknownFields               protoimpl.UnknownFields
	sizeCache                   protoimpl.SizeCache
}

func (x *GoodsDetail) Reset() {
	*x = GoodsDetail{}
	mi := &file_shop_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsDetail) ProtoMessage() {}

func (x *GoodsDetail) ProtoReflect() protoreflect.Message {
	mi := &file_shop_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsDetail.ProtoReflect.Descriptor instead.
func (*GoodsDetail) Descriptor() ([]byte, []int) {
	return file_shop_proto_rawDescGZIP(), []int{1}
}

func (x *GoodsDetail) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GoodsDetail) GetCategoryName() string {
	if x != nil {
		return x.CategoryName
	}
	return ""
}

func (x *GoodsDetail) GetCouponRemainQuantity() int64 {
	if x != nil {
		return x.CouponRemainQuantity
	}
	return 0
}

func (x *GoodsDetail) GetPromotionRate() int64 {
	if x != nil {
		return x.PromotionRate
	}
	return 0
}

func (x *GoodsDetail) GetSubsidyGoodsType() int64 {
	if x != nil {
		return x.SubsidyGoodsType
	}
	return 0
}

func (x *GoodsDetail) GetServiceTags() []int64 {
	if x != nil {
		return x.ServiceTags
	}
	return nil
}

func (x *GoodsDetail) GetMallId() int64 {
	if x != nil {
		return x.MallId
	}
	return 0
}

func (x *GoodsDetail) GetMallName() string {
	if x != nil {
		return x.MallName
	}
	return ""
}

func (x *GoodsDetail) GetMallCouponEndTime() int64 {
	if x != nil {
		return x.MallCouponEndTime
	}
	return 0
}

func (x *GoodsDetail) GetLgstTxt() string {
	if x != nil {
		return x.LgstTxt
	}
	return ""
}

func (x *GoodsDetail) GetGoodsName() string {
	if x != nil {
		return x.GoodsName
	}
	return ""
}

func (x *GoodsDetail) GetGoodsGalleryUrls() []string {
	if x != nil {
		return x.GoodsGalleryUrls
	}
	return nil
}

func (x *GoodsDetail) GetGoodsId() int64 {
	if x != nil {
		return x.GoodsId
	}
	return 0
}

func (x *GoodsDetail) GetBrandName() string {
	if x != nil {
		return x.BrandName
	}
	return ""
}

func (x *GoodsDetail) GetPredictPromotionRate() int64 {
	if x != nil {
		return x.PredictPromotionRate
	}
	return 0
}

func (x *GoodsDetail) GetGoodsDesc() string {
	if x != nil {
		return x.GoodsDesc
	}
	return ""
}

func (x *GoodsDetail) GetOptName() string {
	if x != nil {
		return x.OptName
	}
	return ""
}

func (x *GoodsDetail) GetShareRate() int64 {
	if x != nil {
		return x.ShareRate
	}
	return 0
}

func (x *GoodsDetail) GetOptIds() []int64 {
	if x != nil {
		return x.OptIds
	}
	return nil
}

func (x *GoodsDetail) GetGoodsImageUrl() string {
	if x != nil {
		return x.GoodsImageUrl
	}
	return ""
}

func (x *GoodsDetail) GetMallImgUrl() string {
	if x != nil {
		return x.MallImgUrl
	}
	return ""
}

func (x *GoodsDetail) GetHasMallCoupon() bool {
	if x != nil {
		return x.HasMallCoupon
	}
	return false
}

func (x *GoodsDetail) GetUnifiedTags() []string {
	if x != nil {
		return x.UnifiedTags
	}
	return nil
}

func (x *GoodsDetail) GetVideoUrls() []string {
	if x != nil {
		return x.VideoUrls
	}
	return nil
}

func (x *GoodsDetail) GetCouponStartTime() int64 {
	if x != nil {
		return x.CouponStartTime
	}
	return 0
}

func (x *GoodsDetail) GetMinGroupPrice() int64 {
	if x != nil {
		return x.MinGroupPrice
	}
	return 0
}

func (x *GoodsDetail) GetCouponDiscount() int64 {
	if x != nil {
		return x.CouponDiscount
	}
	return 0
}

func (x *GoodsDetail) GetCouponEndTime() int64 {
	if x != nil {
		return x.CouponEndTime
	}
	return 0
}

func (x *GoodsDetail) GetZsDuoId() int64 {
	if x != nil {
		return x.ZsDuoId
	}
	return 0
}

func (x *GoodsDetail) GetMallCouponRemainQuantity() int64 {
	if x != nil {
		return x.MallCouponRemainQuantity
	}
	return 0
}

func (x *GoodsDetail) GetPlanType() int64 {
	if x != nil {
		return x.PlanType
	}
	return 0
}

func (x *GoodsDetail) GetExtraCouponAmount() int64 {
	if x != nil {
		return x.ExtraCouponAmount
	}
	return 0
}

func (x *GoodsDetail) GetMaterialList() []string {
	if x != nil {
		return x.MaterialList
	}
	return nil
}

func (x *GoodsDetail) GetCatIds() []int64 {
	if x != nil {
		return x.CatIds
	}
	return nil
}

func (x *GoodsDetail) GetCouponMinOrderAmount() int64 {
	if x != nil {
		return x.CouponMinOrderAmount
	}
	return 0
}

func (x *GoodsDetail) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *GoodsDetail) GetMallCouponDiscountPct() int64 {
	if x != nil {
		return x.MallCouponDiscountPct
	}
	return 0
}

func (x *GoodsDetail) GetCatId() int64 {
	if x != nil {
		return x.CatId
	}
	return 0
}

func (x *GoodsDetail) GetCouponTotalQuantity() int64 {
	if x != nil {
		return x.CouponTotalQuantity
	}
	return 0
}

func (x *GoodsDetail) GetMallCouponMinOrderAmount() int64 {
	if x != nil {
		return x.MallCouponMinOrderAmount
	}
	return 0
}

func (x *GoodsDetail) GetMerchantType() int64 {
	if x != nil {
		return x.MerchantType
	}
	return 0
}

func (x *GoodsDetail) GetSalesTip() string {
	if x != nil {
		return x.SalesTip
	}
	return ""
}

func (x *GoodsDetail) GetIsMultiGroup() bool {
	if x != nil {
		return x.IsMultiGroup
	}
	return false
}

func (x *GoodsDetail) GetOnlySceneAuth() bool {
	if x != nil {
		return x.OnlySceneAuth
	}
	return false
}

func (x *GoodsDetail) GetDescTxt() string {
	if x != nil {
		return x.DescTxt
	}
	return ""
}

func (x *GoodsDetail) GetGoodsThumbnailUrl() string {
	if x != nil {
		return x.GoodsThumbnailUrl
	}
	return ""
}

func (x *GoodsDetail) GetOptId() int64 {
	if x != nil {
		return x.OptId
	}
	return 0
}

func (x *GoodsDetail) GetActivityTags() []int64 {
	if x != nil {
		return x.ActivityTags
	}
	return nil
}

func (x *GoodsDetail) GetHasCoupon() bool {
	if x != nil {
		return x.HasCoupon
	}
	return false
}

func (x *GoodsDetail) GetMinNormalPrice() int64 {
	if x != nil {
		return x.MinNormalPrice
	}
	return 0
}

func (x *GoodsDetail) GetMallCouponStartTime() int64 {
	if x != nil {
		return x.MallCouponStartTime
	}
	return 0
}

func (x *GoodsDetail) GetServTxt() string {
	if x != nil {
		return x.ServTxt
	}
	return ""
}

func (x *GoodsDetail) GetMallCouponTotalQuantity() int64 {
	if x != nil {
		return x.MallCouponTotalQuantity
	}
	return 0
}

func (x *GoodsDetail) GetMallCouponMaxDiscountAmount() int64 {
	if x != nil {
		return x.MallCouponMaxDiscountAmount
	}
	return 0
}

func (x *GoodsDetail) GetMallCps() int64 {
	if x != nil {
		return x.MallCps
	}
	return 0
}

func (x *GoodsDetail) GetGoodsSign() string {
	if x != nil {
		return x.GoodsSign
	}
	return ""
}

// 心愿兑换记录表
type WishExchange struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId        int64                  `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	GoodsUrl      int64                  `protobuf:"varint,3,opt,name=goods_url,json=goodsUrl,proto3" json:"goods_url,omitempty"`
	Integral      int64                  `protobuf:"varint,4,opt,name=integral,proto3" json:"integral,omitempty"`
	Extra         string                 `protobuf:"bytes,5,opt,name=extra,proto3" json:"extra,omitempty"`
	GoodsSign     string                 `protobuf:"bytes,6,opt,name=goods_sign,json=goodsSign,proto3" json:"goods_sign,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WishExchange) Reset() {
	*x = WishExchange{}
	mi := &file_shop_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WishExchange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WishExchange) ProtoMessage() {}

func (x *WishExchange) ProtoReflect() protoreflect.Message {
	mi := &file_shop_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WishExchange.ProtoReflect.Descriptor instead.
func (*WishExchange) Descriptor() ([]byte, []int) {
	return file_shop_proto_rawDescGZIP(), []int{2}
}

func (x *WishExchange) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WishExchange) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *WishExchange) GetGoodsUrl() int64 {
	if x != nil {
		return x.GoodsUrl
	}
	return 0
}

func (x *WishExchange) GetIntegral() int64 {
	if x != nil {
		return x.Integral
	}
	return 0
}

func (x *WishExchange) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

func (x *WishExchange) GetGoodsSign() string {
	if x != nil {
		return x.GoodsSign
	}
	return ""
}

// 订单表
type Order struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UserId        int64                  `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	GoodsSign     int64                  `protobuf:"varint,3,opt,name=goods_sign,json=goodsSign,proto3" json:"goods_sign,omitempty"`
	CreateTime    int64                  `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Extra         string                 `protobuf:"bytes,5,opt,name=extra,proto3" json:"extra,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Order) Reset() {
	*x = Order{}
	mi := &file_shop_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Order) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Order) ProtoMessage() {}

func (x *Order) ProtoReflect() protoreflect.Message {
	mi := &file_shop_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Order.ProtoReflect.Descriptor instead.
func (*Order) Descriptor() ([]byte, []int) {
	return file_shop_proto_rawDescGZIP(), []int{3}
}

func (x *Order) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Order) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *Order) GetGoodsSign() int64 {
	if x != nil {
		return x.GoodsSign
	}
	return 0
}

func (x *Order) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *Order) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

// 商品列表
type GoodsListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LastId        int64                  `protobuf:"varint,1,opt,name=lastId,proto3" json:"lastId,omitempty"`
	Size          int64                  `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsListReq) Reset() {
	*x = GoodsListReq{}
	mi := &file_shop_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsListReq) ProtoMessage() {}

func (x *GoodsListReq) ProtoReflect() protoreflect.Message {
	mi := &file_shop_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsListReq.ProtoReflect.Descriptor instead.
func (*GoodsListReq) Descriptor() ([]byte, []int) {
	return file_shop_proto_rawDescGZIP(), []int{4}
}

func (x *GoodsListReq) GetLastId() int64 {
	if x != nil {
		return x.LastId
	}
	return 0
}

func (x *GoodsListReq) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

type GoodsListResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         int64                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*GoodsInfo           `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsListResp) Reset() {
	*x = GoodsListResp{}
	mi := &file_shop_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsListResp) ProtoMessage() {}

func (x *GoodsListResp) ProtoReflect() protoreflect.Message {
	mi := &file_shop_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsListResp.ProtoReflect.Descriptor instead.
func (*GoodsListResp) Descriptor() ([]byte, []int) {
	return file_shop_proto_rawDescGZIP(), []int{5}
}

func (x *GoodsListResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GoodsListResp) GetList() []*GoodsInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 搜索商品
type GoodsSearchReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Keyword       string                 `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword,omitempty"`
	PageSize      int64                  `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Page          int64                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	UserId        int64                  `protobuf:"varint,4,opt,name=userId,proto3" json:"userId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsSearchReq) Reset() {
	*x = GoodsSearchReq{}
	mi := &file_shop_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsSearchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsSearchReq) ProtoMessage() {}

func (x *GoodsSearchReq) ProtoReflect() protoreflect.Message {
	mi := &file_shop_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsSearchReq.ProtoReflect.Descriptor instead.
func (*GoodsSearchReq) Descriptor() ([]byte, []int) {
	return file_shop_proto_rawDescGZIP(), []int{6}
}

func (x *GoodsSearchReq) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *GoodsSearchReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GoodsSearchReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GoodsSearchReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GoodsSearchResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         int64                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*GoodsInfo           `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsSearchResp) Reset() {
	*x = GoodsSearchResp{}
	mi := &file_shop_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsSearchResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsSearchResp) ProtoMessage() {}

func (x *GoodsSearchResp) ProtoReflect() protoreflect.Message {
	mi := &file_shop_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsSearchResp.ProtoReflect.Descriptor instead.
func (*GoodsSearchResp) Descriptor() ([]byte, []int) {
	return file_shop_proto_rawDescGZIP(), []int{7}
}

func (x *GoodsSearchResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GoodsSearchResp) GetList() []*GoodsInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 心愿兑换接口
type GoodsWishReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GoodsSign     string                 `protobuf:"bytes,1,opt,name=goodsSign,proto3" json:"goodsSign,omitempty"`
	UserId        int64                  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`
	Integral      int64                  `protobuf:"varint,3,opt,name=integral,proto3" json:"integral,omitempty"`
	Extra         string                 `protobuf:"bytes,4,opt,name=extra,proto3" json:"extra,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsWishReq) Reset() {
	*x = GoodsWishReq{}
	mi := &file_shop_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsWishReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsWishReq) ProtoMessage() {}

func (x *GoodsWishReq) ProtoReflect() protoreflect.Message {
	mi := &file_shop_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsWishReq.ProtoReflect.Descriptor instead.
func (*GoodsWishReq) Descriptor() ([]byte, []int) {
	return file_shop_proto_rawDescGZIP(), []int{8}
}

func (x *GoodsWishReq) GetGoodsSign() string {
	if x != nil {
		return x.GoodsSign
	}
	return ""
}

func (x *GoodsWishReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GoodsWishReq) GetIntegral() int64 {
	if x != nil {
		return x.Integral
	}
	return 0
}

func (x *GoodsWishReq) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

type GoodsWishResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsWishResp) Reset() {
	*x = GoodsWishResp{}
	mi := &file_shop_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsWishResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsWishResp) ProtoMessage() {}

func (x *GoodsWishResp) ProtoReflect() protoreflect.Message {
	mi := &file_shop_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsWishResp.ProtoReflect.Descriptor instead.
func (*GoodsWishResp) Descriptor() ([]byte, []int) {
	return file_shop_proto_rawDescGZIP(), []int{9}
}

func (x *GoodsWishResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// 心愿兑换列表
type GoodsWishListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	PageSize      int64                  `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Page          int64                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsWishListReq) Reset() {
	*x = GoodsWishListReq{}
	mi := &file_shop_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsWishListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsWishListReq) ProtoMessage() {}

func (x *GoodsWishListReq) ProtoReflect() protoreflect.Message {
	mi := &file_shop_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsWishListReq.ProtoReflect.Descriptor instead.
func (*GoodsWishListReq) Descriptor() ([]byte, []int) {
	return file_shop_proto_rawDescGZIP(), []int{10}
}

func (x *GoodsWishListReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GoodsWishListReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GoodsWishListReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

type GoodsWishListResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         int64                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*WishExchange        `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoodsWishListResp) Reset() {
	*x = GoodsWishListResp{}
	mi := &file_shop_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoodsWishListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsWishListResp) ProtoMessage() {}

func (x *GoodsWishListResp) ProtoReflect() protoreflect.Message {
	mi := &file_shop_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsWishListResp.ProtoReflect.Descriptor instead.
func (*GoodsWishListResp) Descriptor() ([]byte, []int) {
	return file_shop_proto_rawDescGZIP(), []int{11}
}

func (x *GoodsWishListResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GoodsWishListResp) GetList() []*WishExchange {
	if x != nil {
		return x.List
	}
	return nil
}

// 订单列表
type OrderListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int64                  `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	PageSize      int64                  `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Page          int64                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrderListReq) Reset() {
	*x = OrderListReq{}
	mi := &file_shop_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderListReq) ProtoMessage() {}

func (x *OrderListReq) ProtoReflect() protoreflect.Message {
	mi := &file_shop_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderListReq.ProtoReflect.Descriptor instead.
func (*OrderListReq) Descriptor() ([]byte, []int) {
	return file_shop_proto_rawDescGZIP(), []int{12}
}

func (x *OrderListReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *OrderListReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *OrderListReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

type OrderListResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         int64                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*Order               `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrderListResp) Reset() {
	*x = OrderListResp{}
	mi := &file_shop_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderListResp) ProtoMessage() {}

func (x *OrderListResp) ProtoReflect() protoreflect.Message {
	mi := &file_shop_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderListResp.ProtoReflect.Descriptor instead.
func (*OrderListResp) Descriptor() ([]byte, []int) {
	return file_shop_proto_rawDescGZIP(), []int{13}
}

func (x *OrderListResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *OrderListResp) GetList() []*Order {
	if x != nil {
		return x.List
	}
	return nil
}

// 从多多进宝平台拉取订单信息
type OrderPullReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrderPullReq) Reset() {
	*x = OrderPullReq{}
	mi := &file_shop_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderPullReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderPullReq) ProtoMessage() {}

func (x *OrderPullReq) ProtoReflect() protoreflect.Message {
	mi := &file_shop_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderPullReq.ProtoReflect.Descriptor instead.
func (*OrderPullReq) Descriptor() ([]byte, []int) {
	return file_shop_proto_rawDescGZIP(), []int{14}
}

type OrderPullResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrderPullResp) Reset() {
	*x = OrderPullResp{}
	mi := &file_shop_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderPullResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderPullResp) ProtoMessage() {}

func (x *OrderPullResp) ProtoReflect() protoreflect.Message {
	mi := &file_shop_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderPullResp.ProtoReflect.Descriptor instead.
func (*OrderPullResp) Descriptor() ([]byte, []int) {
	return file_shop_proto_rawDescGZIP(), []int{15}
}

var File_shop_proto protoreflect.FileDescriptor

var file_shop_proto_rawDesc = string([]byte{
	0x0a, 0x0a, 0x73, 0x68, 0x6f, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x73, 0x74,
	0x72, 0x65, 0x61, 0x6d, 0x22, 0xd6, 0x0b, 0x0a, 0x09, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x63, 0x6f, 0x75, 0x70, 0x6f,
	0x6e, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x14, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x52,
	0x65, 0x6d, 0x61, 0x69, 0x6e, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x2e, 0x0a,
	0x13, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x25, 0x0a,
	0x0e, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x75, 0x62, 0x73, 0x69, 0x64, 0x79, 0x5f,
	0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x69, 0x64, 0x79, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x06, 0x63, 0x61, 0x74, 0x49, 0x64, 0x73, 0x12, 0x35, 0x0a, 0x17, 0x63,
	0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x6d, 0x69, 0x6e, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x14, 0x63, 0x6f,
	0x75, 0x70, 0x6f, 0x6e, 0x4d, 0x69, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6d, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e,
	0x73, 0x75, 0x62, 0x73, 0x69, 0x64, 0x79, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x73, 0x75, 0x62, 0x73, 0x69, 0x64, 0x79, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x70, 0x6f,
	0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x6c, 0x6c, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x61, 0x6c, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x13, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x51,
	0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x61, 0x72,
	0x6b, 0x65, 0x74, 0x46, 0x65, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6c,
	0x67, 0x73, 0x74, 0x5f, 0x74, 0x78, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c,
	0x67, 0x73, 0x74, 0x54, 0x78, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x6f, 0x6f, 0x64,
	0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x5f, 0x74,
	0x69, 0x70, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x54,
	0x69, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x68, 0x61, 0x73, 0x5f, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x68, 0x61, 0x73, 0x4d, 0x61, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x64,
	0x12, 0x34, 0x0a, 0x16, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x6d,
	0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x14, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x65, 0x73, 0x63, 0x5f, 0x74,
	0x78, 0x74, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x65, 0x73, 0x63, 0x54, 0x78,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x44, 0x65, 0x73, 0x63,
	0x12, 0x19, 0x0a, 0x08, 0x6f, 0x70, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x18, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x72,
	0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x5f, 0x74, 0x69,
	0x70, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6d,
	0x65, 0x53, 0x61, 0x6c, 0x65, 0x73, 0x54, 0x69, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x68, 0x61,
	0x72, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73,
	0x68, 0x61, 0x72, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x67, 0x6f, 0x6f, 0x64,
	0x73, 0x5f, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x6e, 0x61, 0x69, 0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x54, 0x68, 0x75, 0x6d,
	0x62, 0x6e, 0x61, 0x69, 0x6c, 0x55, 0x72, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x1c, 0x20, 0x03, 0x28, 0x03, 0x52, 0x06, 0x6f, 0x70, 0x74, 0x49, 0x64,
	0x73, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x1d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x6f, 0x70, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x67, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x23, 0x0a,
	0x0d, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x20,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x54, 0x61,
	0x67, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x61, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e,
	0x18, 0x21, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x68, 0x61, 0x73, 0x43, 0x6f, 0x75, 0x70, 0x6f,
	0x6e, 0x12, 0x28, 0x0a, 0x10, 0x6d, 0x69, 0x6e, 0x5f, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x5f,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x22, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x6d, 0x69, 0x6e,
	0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x73,
	0x65, 0x72, 0x76, 0x5f, 0x74, 0x78, 0x74, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73,
	0x65, 0x72, 0x76, 0x54, 0x78, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65,
	0x64, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x24, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x75, 0x6e,
	0x69, 0x66, 0x69, 0x65, 0x64, 0x54, 0x61, 0x67, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6f, 0x75,
	0x70, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x25,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x69, 0x6e, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x26, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d,
	0x6d, 0x69, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x27, 0x0a,
	0x0f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x27, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x44, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f,
	0x73, 0x69, 0x67, 0x6e, 0x18, 0x28, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x6f, 0x6f, 0x64,
	0x73, 0x53, 0x69, 0x67, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f,
	0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x29, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d,
	0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xe9, 0x10,
	0x0a, 0x0b, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x6d,
	0x61, 0x69, 0x6e, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x14, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x52, 0x65, 0x6d, 0x61, 0x69, 0x6e,
	0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x6d,
	0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0d, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x65, 0x12,
	0x2c, 0x0a, 0x12, 0x73, 0x75, 0x62, 0x73, 0x69, 0x64, 0x79, 0x5f, 0x67, 0x6f, 0x6f, 0x64, 0x73,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x73, 0x75, 0x62,
	0x73, 0x69, 0x64, 0x79, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x61, 0x67, 0x73,
	0x12, 0x17, 0x0a, 0x07, 0x6d, 0x61, 0x6c, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x6d, 0x61, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x6c,
	0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x61,
	0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x14, 0x6d, 0x61, 0x6c, 0x6c, 0x5f, 0x63,
	0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x6d, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e,
	0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x67, 0x73, 0x74, 0x5f,
	0x74, 0x78, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x67, 0x73, 0x74, 0x54,
	0x78, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x2c, 0x0a, 0x12, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x67, 0x61, 0x6c, 0x6c, 0x65,
	0x72, 0x79, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x67,
	0x6f, 0x6f, 0x64, 0x73, 0x47, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x79, 0x55, 0x72, 0x6c, 0x73, 0x12,
	0x19, 0x0a, 0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x72,
	0x61, 0x6e, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x62, 0x72, 0x61, 0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x70, 0x72, 0x65,
	0x64, 0x69, 0x63, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72,
	0x61, 0x74, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x14, 0x70, 0x72, 0x65, 0x64, 0x69,
	0x63, 0x74, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x44, 0x65, 0x73, 0x63, 0x12, 0x19,
	0x0a, 0x08, 0x6f, 0x70, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6f, 0x70, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x68, 0x61,
	0x72, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73,
	0x68, 0x61, 0x72, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x13, 0x20, 0x03, 0x28, 0x03, 0x52, 0x06, 0x6f, 0x70, 0x74, 0x49, 0x64,
	0x73, 0x12, 0x26, 0x0a, 0x0f, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x67, 0x6f, 0x6f, 0x64,
	0x73, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x20, 0x0a, 0x0c, 0x6d, 0x61, 0x6c,
	0x6c, 0x5f, 0x69, 0x6d, 0x67, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x6d, 0x61, 0x6c, 0x6c, 0x49, 0x6d, 0x67, 0x55, 0x72, 0x6c, 0x12, 0x26, 0x0a, 0x0f, 0x68,
	0x61, 0x73, 0x5f, 0x6d, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x68, 0x61, 0x73, 0x4d, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x75,
	0x70, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x6e, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x74,
	0x61, 0x67, 0x73, 0x18, 0x17, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x75, 0x6e, 0x69, 0x66, 0x69,
	0x65, 0x64, 0x54, 0x61, 0x67, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f,
	0x75, 0x72, 0x6c, 0x73, 0x18, 0x18, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x76, 0x69, 0x64, 0x65,
	0x6f, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x69, 0x6e, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6d, 0x69, 0x6e, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x75,
	0x70, 0x6f, 0x6e, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x1b, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0e, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x63, 0x6f, 0x75,
	0x70, 0x6f, 0x6e, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x09, 0x7a, 0x73,
	0x5f, 0x64, 0x75, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x7a,
	0x73, 0x44, 0x75, 0x6f, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x1b, 0x6d, 0x61, 0x6c, 0x6c, 0x5f, 0x63,
	0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x71, 0x75, 0x61,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x18, 0x6d, 0x61, 0x6c,
	0x6c, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x52, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x51, 0x75, 0x61,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x63, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x20, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x11, 0x65, 0x78, 0x74, 0x72, 0x61, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x74, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x21, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x74, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x22, 0x20, 0x03, 0x28, 0x03, 0x52, 0x06, 0x63, 0x61, 0x74, 0x49, 0x64, 0x73,
	0x12, 0x35, 0x0a, 0x17, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x6d, 0x69, 0x6e, 0x5f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x23, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x14, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x4d, 0x69, 0x6e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x24, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x18, 0x6d, 0x61, 0x6c, 0x6c,
	0x5f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x70, 0x63, 0x74, 0x18, 0x25, 0x20, 0x01, 0x28, 0x03, 0x52, 0x15, 0x6d, 0x61, 0x6c, 0x6c,
	0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x63,
	0x74, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x26, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x63, 0x61, 0x74, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x15, 0x63, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x18, 0x27, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x54,
	0x6f, 0x74, 0x61, 0x6c, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x3e, 0x0a, 0x1c,
	0x6d, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x6d, 0x69, 0x6e, 0x5f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x28, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x18, 0x6d, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x4d, 0x69,
	0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d,
	0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x29, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0c, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x5f, 0x74, 0x69, 0x70, 0x18, 0x2a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x54, 0x69, 0x70, 0x12, 0x24,
	0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x18, 0x2b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x12, 0x26, 0x0a, 0x0f, 0x6f, 0x6e, 0x6c, 0x79, 0x5f, 0x73, 0x63, 0x65,
	0x6e, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x6f,
	0x6e, 0x6c, 0x79, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x41, 0x75, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x08,
	0x64, 0x65, 0x73, 0x63, 0x5f, 0x74, 0x78, 0x74, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x64, 0x65, 0x73, 0x63, 0x54, 0x78, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x67, 0x6f, 0x6f, 0x64, 0x73,
	0x5f, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x6e, 0x61, 0x69, 0x6c, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x2e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x54, 0x68, 0x75, 0x6d, 0x62,
	0x6e, 0x61, 0x69, 0x6c, 0x55, 0x72, 0x6c, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x2f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6f, 0x70, 0x74, 0x49, 0x64, 0x12, 0x23,
	0x0a, 0x0d, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18,
	0x30, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x54,
	0x61, 0x67, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x61, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x70, 0x6f,
	0x6e, 0x18, 0x31, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x68, 0x61, 0x73, 0x43, 0x6f, 0x75, 0x70,
	0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x10, 0x6d, 0x69, 0x6e, 0x5f, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c,
	0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x32, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x6d, 0x69,
	0x6e, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x33, 0x0a, 0x16,
	0x6d, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x33, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x6d, 0x61,
	0x6c, 0x6c, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x5f, 0x74, 0x78, 0x74, 0x18, 0x34, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x54, 0x78, 0x74, 0x12, 0x3b, 0x0a, 0x1a,
	0x6d, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x35, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x17, 0x6d, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x54, 0x6f, 0x74, 0x61,
	0x6c, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x44, 0x0a, 0x1f, 0x6d, 0x61, 0x6c,
	0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x36, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x1b, 0x6d, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x75, 0x70, 0x6f, 0x6e, 0x4d, 0x61,
	0x78, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x19, 0x0a, 0x08, 0x6d, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x70, 0x73, 0x18, 0x37, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x6d, 0x61, 0x6c, 0x6c, 0x43, 0x70, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x6f,
	0x6f, 0x64, 0x73, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x18, 0x38, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x67, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x69, 0x67, 0x6e, 0x22, 0xa5, 0x01, 0x0a, 0x0c, 0x57, 0x69,
	0x73, 0x68, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x55, 0x72, 0x6c,
	0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x12, 0x14, 0x0a, 0x05,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x73, 0x69, 0x67, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x69, 0x67,
	0x6e, 0x22, 0x86, 0x01, 0x0a, 0x05, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x73, 0x69,
	0x67, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x53,
	0x69, 0x67, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x22, 0x3a, 0x0a, 0x0c, 0x47, 0x6f,
	0x6f, 0x64, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x61,
	0x73, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6c, 0x61, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x4c, 0x0a, 0x0d, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x25, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x73, 0x74,
	0x72, 0x65, 0x61, 0x6d, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x22, 0x72, 0x0a, 0x0e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x4e, 0x0a, 0x0f, 0x47, 0x6f, 0x6f, 0x64,
	0x73, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x12, 0x25, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x76, 0x0a, 0x0c, 0x47, 0x6f, 0x6f, 0x64,
	0x73, 0x57, 0x69, 0x73, 0x68, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x6f, 0x6f, 0x64,
	0x73, 0x53, 0x69, 0x67, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x6f, 0x6f,
	0x64, 0x73, 0x53, 0x69, 0x67, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x78,
	0x74, 0x72, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61,
	0x22, 0x29, 0x0a, 0x0d, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x57, 0x69, 0x73, 0x68, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x5a, 0x0a, 0x10, 0x47,
	0x6f, 0x6f, 0x64, 0x73, 0x57, 0x69, 0x73, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x53, 0x0a, 0x11, 0x47, 0x6f, 0x6f, 0x64, 0x73,
	0x57, 0x69, 0x73, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x28, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x2e, 0x57, 0x69, 0x73, 0x68, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x56, 0x0a, 0x0c,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x22, 0x48, 0x0a, 0x0d, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x21, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x73, 0x74, 0x72, 0x65,
	0x61, 0x6d, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x0e,
	0x0a, 0x0c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x75, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x22, 0x0f,
	0x0a, 0x0d, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x75, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x32,
	0x86, 0x03, 0x0a, 0x04, 0x53, 0x68, 0x6f, 0x70, 0x12, 0x3b, 0x0a, 0x0c, 0x67, 0x65, 0x74, 0x47,
	0x6f, 0x6f, 0x64, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x2e, 0x73, 0x74, 0x72, 0x65, 0x61,
	0x6d, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x15,
	0x2e, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3e, 0x0a, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x47,
	0x6f, 0x6f, 0x64, 0x73, 0x12, 0x16, 0x2e, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x2e, 0x47, 0x6f,
	0x6f, 0x64, 0x73, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x73,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x0c, 0x77, 0x69, 0x73, 0x68, 0x45, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x14, 0x2e, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x2e, 0x47,
	0x6f, 0x6f, 0x64, 0x73, 0x57, 0x69, 0x73, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x73, 0x74,
	0x72, 0x65, 0x61, 0x6d, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x57, 0x69, 0x73, 0x68, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x4a, 0x0a, 0x13, 0x67, 0x65, 0x74, 0x57, 0x69, 0x73, 0x68, 0x45, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x2e, 0x73, 0x74, 0x72, 0x65,
	0x61, 0x6d, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x57, 0x69, 0x73, 0x68, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x2e, 0x47, 0x6f, 0x6f,
	0x64, 0x73, 0x57, 0x69, 0x73, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3b,
	0x0a, 0x0c, 0x67, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14,
	0x2e, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x0c, 0x67,
	0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x75, 0x6c, 0x6c, 0x12, 0x14, 0x2e, 0x73, 0x74,
	0x72, 0x65, 0x61, 0x6d, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x75, 0x6c, 0x6c, 0x52, 0x65,
	0x71, 0x1a, 0x15, 0x2e, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x50, 0x75, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_shop_proto_rawDescOnce sync.Once
	file_shop_proto_rawDescData []byte
)

func file_shop_proto_rawDescGZIP() []byte {
	file_shop_proto_rawDescOnce.Do(func() {
		file_shop_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_shop_proto_rawDesc), len(file_shop_proto_rawDesc)))
	})
	return file_shop_proto_rawDescData
}

var file_shop_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_shop_proto_goTypes = []any{
	(*GoodsInfo)(nil),         // 0: stream.GoodsInfo
	(*GoodsDetail)(nil),       // 1: stream.GoodsDetail
	(*WishExchange)(nil),      // 2: stream.WishExchange
	(*Order)(nil),             // 3: stream.Order
	(*GoodsListReq)(nil),      // 4: stream.GoodsListReq
	(*GoodsListResp)(nil),     // 5: stream.GoodsListResp
	(*GoodsSearchReq)(nil),    // 6: stream.GoodsSearchReq
	(*GoodsSearchResp)(nil),   // 7: stream.GoodsSearchResp
	(*GoodsWishReq)(nil),      // 8: stream.GoodsWishReq
	(*GoodsWishResp)(nil),     // 9: stream.GoodsWishResp
	(*GoodsWishListReq)(nil),  // 10: stream.GoodsWishListReq
	(*GoodsWishListResp)(nil), // 11: stream.GoodsWishListResp
	(*OrderListReq)(nil),      // 12: stream.OrderListReq
	(*OrderListResp)(nil),     // 13: stream.OrderListResp
	(*OrderPullReq)(nil),      // 14: stream.OrderPullReq
	(*OrderPullResp)(nil),     // 15: stream.OrderPullResp
}
var file_shop_proto_depIdxs = []int32{
	0,  // 0: stream.GoodsListResp.list:type_name -> stream.GoodsInfo
	0,  // 1: stream.GoodsSearchResp.list:type_name -> stream.GoodsInfo
	2,  // 2: stream.GoodsWishListResp.list:type_name -> stream.WishExchange
	3,  // 3: stream.OrderListResp.list:type_name -> stream.Order
	4,  // 4: stream.Shop.getGoodsList:input_type -> stream.GoodsListReq
	6,  // 5: stream.Shop.searchGoods:input_type -> stream.GoodsSearchReq
	8,  // 6: stream.Shop.wishExchange:input_type -> stream.GoodsWishReq
	10, // 7: stream.Shop.getWishExchangeList:input_type -> stream.GoodsWishListReq
	12, // 8: stream.Shop.getOrderList:input_type -> stream.OrderListReq
	14, // 9: stream.Shop.getOrderPull:input_type -> stream.OrderPullReq
	5,  // 10: stream.Shop.getGoodsList:output_type -> stream.GoodsListResp
	7,  // 11: stream.Shop.searchGoods:output_type -> stream.GoodsSearchResp
	9,  // 12: stream.Shop.wishExchange:output_type -> stream.GoodsWishResp
	11, // 13: stream.Shop.getWishExchangeList:output_type -> stream.GoodsWishListResp
	13, // 14: stream.Shop.getOrderList:output_type -> stream.OrderListResp
	15, // 15: stream.Shop.getOrderPull:output_type -> stream.OrderPullResp
	10, // [10:16] is the sub-list for method output_type
	4,  // [4:10] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_shop_proto_init() }
func file_shop_proto_init() {
	if File_shop_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_shop_proto_rawDesc), len(file_shop_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_shop_proto_goTypes,
		DependencyIndexes: file_shop_proto_depIdxs,
		MessageInfos:      file_shop_proto_msgTypes,
	}.Build()
	File_shop_proto = out.File
	file_shop_proto_goTypes = nil
	file_shop_proto_depIdxs = nil
}
