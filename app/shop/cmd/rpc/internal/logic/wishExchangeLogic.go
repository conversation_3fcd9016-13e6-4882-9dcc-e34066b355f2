package logic

import (
	"context"
	"looklook/app/checkin/cmd/rpc/checkin"

	"looklook/app/shop/cmd/rpc/internal/svc"
	"looklook/app/shop/cmd/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type WishExchangeLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewWishExchangeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WishExchangeLogic {
	return &WishExchangeLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *WishExchangeLogic) WishExchange(in *pb.GoodsWishReq) (*pb.GoodsWishResp, error) {
	// todo: add your logic here and delete this line
	// 1、 增加一条兑换记录
	// 2、 减少用户的积分
	// 3、返回

	// todo: 分布式事务 dtm

	err := l.svcCtx.WishRedemptionRecords.WishExchange(in.UserId, in.GoodsSign, in.Integral, in.Extra)
	if err != nil {
		return nil, err
	}

	// todo： 判断当前用户是否兑换过该商品

	_, err = l.svcCtx.CheckinRpc.DeductIntegral(context.Background(), &checkin.DeductIntegralReq{
		UserId:   in.UserId,
		Integral: in.Integral,
	})
	if err != nil {
		return nil, err
	}

	return &pb.GoodsWishResp{
		Success: true,
	}, nil
}
