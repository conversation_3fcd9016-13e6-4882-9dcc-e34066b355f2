package checkin

import (
	"net/http"

	"looklook/common/result"

	"looklook/app/checkin/cmd/api/internal/logic/checkin"
	"looklook/app/checkin/cmd/api/internal/svc"
)

func GetIntegralHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := checkin.NewGetIntegralLogic(r.Context(), svcCtx)
		resp, err := l.GetIntegral()

		result.HttpResult(r, w, resp, err)
	}
}
