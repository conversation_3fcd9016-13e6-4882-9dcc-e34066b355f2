// Code generated by goctl. DO NOT EDIT.

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
	"looklook/common/globalkey"
)

var (
	integralFieldNames          = builder.RawFieldNames(&Integral{})
	integralRows                = strings.Join(integralFieldNames, ",")
	integralRowsExpectAutoSet   = strings.Join(stringx.Remove(integralFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), ",")
	integralRowsWithPlaceHolder = strings.Join(stringx.Remove(integralFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), "=?,") + "=?"

	cacheCheckinIntegralIdPrefix = "cache:checkin:integral:id:"
)

type (
	integralModel interface {
		Insert(ctx context.Context, data *Integral) (sql.Result, error)
		TransInsert(ctx context.Context, session sqlx.Session, data *Integral) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Integral, error)
		Update(ctx context.Context, data *Integral) error
		List(ctx context.Context, page, limit int64) ([]*Integral, error)
		TransUpdate(ctx context.Context, session sqlx.Session, data *Integral) error
		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		SelectBuilder() squirrel.SelectBuilder
		FindSum(ctx context.Context, sumBuilder squirrel.SelectBuilder, field string) (float64, error)
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder, field string) (int64, error)
		FindAll(ctx context.Context, rowBuilder squirrel.SelectBuilder, orderBy string) ([]*Integral, error)
		FindPageListByPage(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*Integral, error)
		FindPageListByPageWithTotal(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*Integral, int64, error)
		FindPageListByIdDESC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*Integral, error)
		FindPageListByIdASC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*Integral, error)
		Delete(ctx context.Context, id int64) error
	}

	defaultIntegralModel struct {
		sqlc.CachedConn
		table string
	}

	Integral struct {
		Id       int64 `db:"id"`
		UserId   int64 `db:"user_id"`
		Integral int64 `db:"integral"`
	}
)

func newIntegralModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultIntegralModel {
	return &defaultIntegralModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`integral`",
	}
}

func (m *defaultIntegralModel) Delete(ctx context.Context, id int64) error {
	checkinIntegralIdKey := fmt.Sprintf("%s%v", cacheCheckinIntegralIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		return conn.ExecCtx(ctx, query, id)
	}, checkinIntegralIdKey)
	return err
}

func (m *defaultIntegralModel) FindOne(ctx context.Context, id int64) (*Integral, error) {
	checkinIntegralIdKey := fmt.Sprintf("%s%v", cacheCheckinIntegralIdPrefix, id)
	var resp Integral
	err := m.QueryRowCtx(ctx, &resp, checkinIntegralIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", integralRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultIntegralModel) Insert(ctx context.Context, data *Integral) (sql.Result, error) {
	checkinIntegralIdKey := fmt.Sprintf("%s%v", cacheCheckinIntegralIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?)", m.table, integralRowsExpectAutoSet)
		return conn.ExecCtx(ctx, query, data.UserId, data.Integral)
	}, checkinIntegralIdKey)
	return ret, err
}

func (m *defaultIntegralModel) TransInsert(ctx context.Context, session sqlx.Session, data *Integral) (sql.Result, error) {
	checkinIntegralIdKey := fmt.Sprintf("%s%v", cacheCheckinIntegralIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?)", m.table, integralRowsExpectAutoSet)
		return session.ExecCtx(ctx, query, data.UserId, data.Integral)
	}, checkinIntegralIdKey)
	return ret, err
}
func (m *defaultIntegralModel) Update(ctx context.Context, data *Integral) error {
	checkinIntegralIdKey := fmt.Sprintf("%s%v", cacheCheckinIntegralIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, integralRowsWithPlaceHolder)
		return conn.ExecCtx(ctx, query, data.UserId, data.Integral, data.Id)
	}, checkinIntegralIdKey)
	return err
}

func (m *defaultIntegralModel) TransUpdate(ctx context.Context, session sqlx.Session, data *Integral) error {
	checkinIntegralIdKey := fmt.Sprintf("%s%v", cacheCheckinIntegralIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, integralRowsWithPlaceHolder)
		return session.ExecCtx(ctx, query, data.UserId, data.Integral, data.Id)
	}, checkinIntegralIdKey)
	return err
}

func (m *defaultIntegralModel) List(ctx context.Context, page, limit int64) ([]*Integral, error) {
	query := fmt.Sprintf("select %s from %s limit ?,?", integralRows, m.table)
	var resp []*Integral
	//err := m.conn.QueryRowsCtx(ctx, &resp, query, (page-1)*limit, limit)
	err := m.QueryRowsNoCacheCtx(ctx, &resp, query, (page-1)*limit, limit)
	return resp, err
}

func (m *defaultIntegralModel) Trans(ctx context.Context, fn func(ctx context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, func(ctx context.Context, session sqlx.Session) error {
		return fn(ctx, session)
	})
}

func (m *defaultIntegralModel) FindSum(ctx context.Context, builder squirrel.SelectBuilder, field string) (float64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindSum Least One Field"), "FindSum Least One Field")
	}

	builder = builder.Columns("IFNULL(SUM(" + field + "),0)")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp float64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultIntegralModel) FindCount(ctx context.Context, builder squirrel.SelectBuilder, field string) (int64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindCount Least One Field"), "FindCount Least One Field")
	}

	builder = builder.Columns("COUNT(" + field + ")")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultIntegralModel) FindAll(ctx context.Context, builder squirrel.SelectBuilder, orderBy string) ([]*Integral, error) {

	builder = builder.Columns(integralRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*Integral
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultIntegralModel) FindPageListByPage(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*Integral, error) {

	builder = builder.Columns(integralRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*Integral
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultIntegralModel) FindPageListByPageWithTotal(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*Integral, int64, error) {

	total, err := m.FindCount(ctx, builder, "id")
	if err != nil {
		return nil, 0, err
	}

	builder = builder.Columns(integralRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, total, err
	}

	var resp []*Integral
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, total, nil
	default:
		return nil, total, err
	}
}

func (m *defaultIntegralModel) FindPageListByIdDESC(ctx context.Context, builder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*Integral, error) {

	builder = builder.Columns(integralRows)

	if preMinId > 0 {
		builder = builder.Where(" id < ? ", preMinId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id DESC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*Integral
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultIntegralModel) FindPageListByIdASC(ctx context.Context, builder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*Integral, error) {

	builder = builder.Columns(integralRows)

	if preMaxId > 0 {
		builder = builder.Where(" id > ? ", preMaxId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id ASC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*Integral
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultIntegralModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select().From(m.table)
}

func (m *defaultIntegralModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheCheckinIntegralIdPrefix, primary)
}

func (m *defaultIntegralModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", integralRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultIntegralModel) tableName() string {
	return m.table
}
