package logic

import (
	"context"

	"looklook/app/checkin/cmd/rpc/internal/svc"
	"looklook/app/checkin/cmd/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeductIntegralLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeductIntegralLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeductIntegralLogic {
	return &DeductIntegralLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *DeductIntegralLogic) DeductIntegral(in *pb.DeductIntegralReq) (*pb.DeductIntegralResp, error) {
	// todo: add your logic here and delete this line

	// Deduct the integral from the user's account
	err := l.svcCtx.IntegralModel.DeductIntegral(l.ctx, in.UserId, in.Integral)
	if err != nil {
		return nil, err
	}

	return &pb.DeductIntegralResp{}, nil
}
