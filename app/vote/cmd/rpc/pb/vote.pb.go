// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v4.25.1
// source: vote.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// --------------------------------投票表--------------------------------
type VoteConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                 //id
	UserId     int64  `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`         //用户id
	LotteryId  int64  `protobuf:"varint,3,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"`   //抽奖ID
	EnableVote int64  `protobuf:"varint,4,opt,name=enableVote,proto3" json:"enableVote,omitempty"` //是否启用投票功能 1是 0否
	VoteConfig string `protobuf:"bytes,5,opt,name=voteConfig,proto3" json:"voteConfig,omitempty"`  //投票配置字段说明: {"title": "投票标题", "description": "投票描述【非必填】", "winner_selection": "中奖者设置：1从所有投票者中抽取 2从票数最多的一方中抽取", "type": "投票类型：1单选 2多选", "min_votes": "最小投票范围", "max_votes": "最大投票范围", "options": [{"text": "张三", "image": "path/to/zhangsan.jpg"}, {"text": "李四", "image": "path/to/lisi.jpg"}, {"text": "王五", "image": "path/to/wangwu.jpg"}]}
	CreateTime int64  `protobuf:"varint,6,opt,name=createTime,proto3" json:"createTime,omitempty"` //createTime
	UpdateTime int64  `protobuf:"varint,7,opt,name=updateTime,proto3" json:"updateTime,omitempty"` //updateTime
}

func (x *VoteConfig) Reset() {
	*x = VoteConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VoteConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoteConfig) ProtoMessage() {}

func (x *VoteConfig) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoteConfig.ProtoReflect.Descriptor instead.
func (*VoteConfig) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{0}
}

func (x *VoteConfig) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *VoteConfig) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *VoteConfig) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

func (x *VoteConfig) GetEnableVote() int64 {
	if x != nil {
		return x.EnableVote
	}
	return 0
}

func (x *VoteConfig) GetVoteConfig() string {
	if x != nil {
		return x.VoteConfig
	}
	return ""
}

func (x *VoteConfig) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *VoteConfig) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

type VoteOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text  string `protobuf:"bytes,1,opt,name=Text,proto3" json:"Text,omitempty"`   //选项名称
	Image string `protobuf:"bytes,2,opt,name=Image,proto3" json:"Image,omitempty"` //选项图片
}

func (x *VoteOption) Reset() {
	*x = VoteOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VoteOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoteOption) ProtoMessage() {}

func (x *VoteOption) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoteOption.ProtoReflect.Descriptor instead.
func (*VoteOption) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{1}
}

func (x *VoteOption) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *VoteOption) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

type VoteConfigJSONData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title           string        `protobuf:"bytes,1,opt,name=Title,proto3" json:"Title,omitempty"`                      //投票标题
	Description     string        `protobuf:"bytes,2,opt,name=Description,proto3" json:"Description,omitempty"`          //投票描述【非必填】
	WinnerSelection int64         `protobuf:"varint,3,opt,name=WinnerSelection,proto3" json:"WinnerSelection,omitempty"` //中奖者设置：1从所有投票者中抽取 2从票数最多的一方中抽取
	Type            int64         `protobuf:"varint,4,opt,name=Type,proto3" json:"Type,omitempty"`                       //投票类型：1单选 2多选
	MinVotes        int64         `protobuf:"varint,5,opt,name=MinVotes,proto3" json:"MinVotes,omitempty"`               //最小投票范围
	MaxVotes        int64         `protobuf:"varint,6,opt,name=MaxVotes,proto3" json:"MaxVotes,omitempty"`               //最大投票范围
	Options         []*VoteOption `protobuf:"bytes,7,rep,name=Options,proto3" json:"Options,omitempty"`                  //选项列表
}

func (x *VoteConfigJSONData) Reset() {
	*x = VoteConfigJSONData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VoteConfigJSONData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoteConfigJSONData) ProtoMessage() {}

func (x *VoteConfigJSONData) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoteConfigJSONData.ProtoReflect.Descriptor instead.
func (*VoteConfigJSONData) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{2}
}

func (x *VoteConfigJSONData) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *VoteConfigJSONData) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *VoteConfigJSONData) GetWinnerSelection() int64 {
	if x != nil {
		return x.WinnerSelection
	}
	return 0
}

func (x *VoteConfigJSONData) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *VoteConfigJSONData) GetMinVotes() int64 {
	if x != nil {
		return x.MinVotes
	}
	return 0
}

func (x *VoteConfigJSONData) GetMaxVotes() int64 {
	if x != nil {
		return x.MaxVotes
	}
	return 0
}

func (x *VoteConfigJSONData) GetOptions() []*VoteOption {
	if x != nil {
		return x.Options
	}
	return nil
}

type AddVoteConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId     int64  `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`         //用户id
	LotteryId  int64  `protobuf:"varint,2,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"`   //抽奖ID
	EnableVote int64  `protobuf:"varint,3,opt,name=enableVote,proto3" json:"enableVote,omitempty"` //是否启用投票功能 1是 0否
	VoteConfig string `protobuf:"bytes,4,opt,name=voteConfig,proto3" json:"voteConfig,omitempty"`  //投票配置字段说明: {"title": "投票标题", "description": "投票描述【非必填】", "winner_selection": "中奖者设置：1从所有投票者中抽取 2从票数最多的一方中抽取", "type": "投票类型：1单选 2多选", "min_votes": "最小投票范围", "max_votes": "最大投票范围", "options": [{"text": "张三", "image": "path/to/zhangsan.jpg"}, {"text": "李四", "image": "path/to/lisi.jpg"}, {"text": "王五", "image": "path/to/wangwu.jpg"}]}
}

func (x *AddVoteConfigReq) Reset() {
	*x = AddVoteConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddVoteConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddVoteConfigReq) ProtoMessage() {}

func (x *AddVoteConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddVoteConfigReq.ProtoReflect.Descriptor instead.
func (*AddVoteConfigReq) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{3}
}

func (x *AddVoteConfigReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AddVoteConfigReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

func (x *AddVoteConfigReq) GetEnableVote() int64 {
	if x != nil {
		return x.EnableVote
	}
	return 0
}

func (x *AddVoteConfigReq) GetVoteConfig() string {
	if x != nil {
		return x.VoteConfig
	}
	return ""
}

type AddVoteConfigResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *AddVoteConfigResp) Reset() {
	*x = AddVoteConfigResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddVoteConfigResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddVoteConfigResp) ProtoMessage() {}

func (x *AddVoteConfigResp) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddVoteConfigResp.ProtoReflect.Descriptor instead.
func (*AddVoteConfigResp) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{4}
}

func (x *AddVoteConfigResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdateVoteConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                 //id
	LotteryId  int64  `protobuf:"varint,3,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"`   //抽奖ID
	EnableVote int64  `protobuf:"varint,4,opt,name=enableVote,proto3" json:"enableVote,omitempty"` //是否启用投票功能 1是 0否
	VoteConfig string `protobuf:"bytes,5,opt,name=voteConfig,proto3" json:"voteConfig,omitempty"`  //投票配置字段说明: {"title": "投票标题", "description": "投票描述【非必填】", "winner_selection": "中奖者设置：1从所有投票者中抽取 2从票数最多的一方中抽取", "type": "投票类型：1单选 2多选", "min_votes": "最小投票范围", "max_votes": "最大投票范围", "options": [{"text": "张三", "image": "path/to/zhangsan.jpg"}, {"text": "李四", "image": "path/to/lisi.jpg"}, {"text": "王五", "image": "path/to/wangwu.jpg"}]}
}

func (x *UpdateVoteConfigReq) Reset() {
	*x = UpdateVoteConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateVoteConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVoteConfigReq) ProtoMessage() {}

func (x *UpdateVoteConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVoteConfigReq.ProtoReflect.Descriptor instead.
func (*UpdateVoteConfigReq) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateVoteConfigReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateVoteConfigReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

func (x *UpdateVoteConfigReq) GetEnableVote() int64 {
	if x != nil {
		return x.EnableVote
	}
	return 0
}

func (x *UpdateVoteConfigReq) GetVoteConfig() string {
	if x != nil {
		return x.VoteConfig
	}
	return ""
}

type UpdateVoteConfigResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateVoteConfigResp) Reset() {
	*x = UpdateVoteConfigResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateVoteConfigResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVoteConfigResp) ProtoMessage() {}

func (x *UpdateVoteConfigResp) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVoteConfigResp.ProtoReflect.Descriptor instead.
func (*UpdateVoteConfigResp) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{6}
}

type DelVoteConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *DelVoteConfigReq) Reset() {
	*x = DelVoteConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelVoteConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelVoteConfigReq) ProtoMessage() {}

func (x *DelVoteConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelVoteConfigReq.ProtoReflect.Descriptor instead.
func (*DelVoteConfigReq) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{7}
}

func (x *DelVoteConfigReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DelVoteConfigResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DelVoteConfigResp) Reset() {
	*x = DelVoteConfigResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelVoteConfigResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelVoteConfigResp) ProtoMessage() {}

func (x *DelVoteConfigResp) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelVoteConfigResp.ProtoReflect.Descriptor instead.
func (*DelVoteConfigResp) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{8}
}

type GetVoteConfigByIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *GetVoteConfigByIdReq) Reset() {
	*x = GetVoteConfigByIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVoteConfigByIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVoteConfigByIdReq) ProtoMessage() {}

func (x *GetVoteConfigByIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVoteConfigByIdReq.ProtoReflect.Descriptor instead.
func (*GetVoteConfigByIdReq) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{9}
}

func (x *GetVoteConfigByIdReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetVoteConfigByIdResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VoteConfig *VoteConfig `protobuf:"bytes,1,opt,name=voteConfig,proto3" json:"voteConfig,omitempty"` //voteConfig
}

func (x *GetVoteConfigByIdResp) Reset() {
	*x = GetVoteConfigByIdResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVoteConfigByIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVoteConfigByIdResp) ProtoMessage() {}

func (x *GetVoteConfigByIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVoteConfigByIdResp.ProtoReflect.Descriptor instead.
func (*GetVoteConfigByIdResp) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{10}
}

func (x *GetVoteConfigByIdResp) GetVoteConfig() *VoteConfig {
	if x != nil {
		return x.VoteConfig
	}
	return nil
}

type SearchVoteConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page       int64  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`             //page
	Limit      int64  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`           //limit
	Id         int64  `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`                 //id
	UserId     int64  `protobuf:"varint,4,opt,name=userId,proto3" json:"userId,omitempty"`         //用户id
	LotteryId  int64  `protobuf:"varint,5,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"`   //抽奖ID
	EnableVote int64  `protobuf:"varint,6,opt,name=enableVote,proto3" json:"enableVote,omitempty"` //是否启用投票功能 1是 0否
	VoteConfig string `protobuf:"bytes,7,opt,name=voteConfig,proto3" json:"voteConfig,omitempty"`  //投票配置字段说明: {"title": "投票标题", "description": "投票描述【非必填】", "winner_selection": "中奖者设置：1从所有投票者中抽取 2从票数最多的一方中抽取", "type": "投票类型：1单选 2多选", "min_votes": "最小投票范围", "max_votes": "最大投票范围", "options": [{"text": "张三", "image": "path/to/zhangsan.jpg"}, {"text": "李四", "image": "path/to/lisi.jpg"}, {"text": "王五", "image": "path/to/wangwu.jpg"}]}
	CreateTime int64  `protobuf:"varint,8,opt,name=createTime,proto3" json:"createTime,omitempty"` //createTime
	UpdateTime int64  `protobuf:"varint,9,opt,name=updateTime,proto3" json:"updateTime,omitempty"` //updateTime
}

func (x *SearchVoteConfigReq) Reset() {
	*x = SearchVoteConfigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchVoteConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchVoteConfigReq) ProtoMessage() {}

func (x *SearchVoteConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchVoteConfigReq.ProtoReflect.Descriptor instead.
func (*SearchVoteConfigReq) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{11}
}

func (x *SearchVoteConfigReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchVoteConfigReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *SearchVoteConfigReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SearchVoteConfigReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *SearchVoteConfigReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

func (x *SearchVoteConfigReq) GetEnableVote() int64 {
	if x != nil {
		return x.EnableVote
	}
	return 0
}

func (x *SearchVoteConfigReq) GetVoteConfig() string {
	if x != nil {
		return x.VoteConfig
	}
	return ""
}

func (x *SearchVoteConfigReq) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *SearchVoteConfigReq) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

type SearchVoteConfigResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VoteConfig []*VoteConfig `protobuf:"bytes,1,rep,name=voteConfig,proto3" json:"voteConfig,omitempty"` //voteConfig
}

func (x *SearchVoteConfigResp) Reset() {
	*x = SearchVoteConfigResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchVoteConfigResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchVoteConfigResp) ProtoMessage() {}

func (x *SearchVoteConfigResp) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchVoteConfigResp.ProtoReflect.Descriptor instead.
func (*SearchVoteConfigResp) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{12}
}

func (x *SearchVoteConfigResp) GetVoteConfig() []*VoteConfig {
	if x != nil {
		return x.VoteConfig
	}
	return nil
}

// --------------------------------投票记录表--------------------------------
type VoteRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                         //id
	LotteryId      int64 `protobuf:"varint,2,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"`           //抽奖ID
	UserId         int64 `protobuf:"varint,3,opt,name=userId,proto3" json:"userId,omitempty"`                 //用户ID
	SelectedOption int64 `protobuf:"varint,4,opt,name=selectedOption,proto3" json:"selectedOption,omitempty"` //用户选择的投票选项
	DelState       int64 `protobuf:"varint,5,opt,name=delState,proto3" json:"delState,omitempty"`
	CreatedAt      int64 `protobuf:"varint,6,opt,name=createdAt,proto3" json:"createdAt,omitempty"` //投票时间
}

func (x *VoteRecord) Reset() {
	*x = VoteRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VoteRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoteRecord) ProtoMessage() {}

func (x *VoteRecord) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoteRecord.ProtoReflect.Descriptor instead.
func (*VoteRecord) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{13}
}

func (x *VoteRecord) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *VoteRecord) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

func (x *VoteRecord) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *VoteRecord) GetSelectedOption() int64 {
	if x != nil {
		return x.SelectedOption
	}
	return 0
}

func (x *VoteRecord) GetDelState() int64 {
	if x != nil {
		return x.DelState
	}
	return 0
}

func (x *VoteRecord) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

type AddVoteRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LotteryId      int64 `protobuf:"varint,1,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"`           //抽奖ID
	UserId         int64 `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`                 //用户ID
	SelectedOption int64 `protobuf:"varint,3,opt,name=selectedOption,proto3" json:"selectedOption,omitempty"` //用户选择的投票选项
	CreatedAt      int64 `protobuf:"varint,4,opt,name=createdAt,proto3" json:"createdAt,omitempty"`           //投票时间
}

func (x *AddVoteRecordReq) Reset() {
	*x = AddVoteRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddVoteRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddVoteRecordReq) ProtoMessage() {}

func (x *AddVoteRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddVoteRecordReq.ProtoReflect.Descriptor instead.
func (*AddVoteRecordReq) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{14}
}

func (x *AddVoteRecordReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

func (x *AddVoteRecordReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AddVoteRecordReq) GetSelectedOption() int64 {
	if x != nil {
		return x.SelectedOption
	}
	return 0
}

func (x *AddVoteRecordReq) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

type AddVoteRecordResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *AddVoteRecordResp) Reset() {
	*x = AddVoteRecordResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddVoteRecordResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddVoteRecordResp) ProtoMessage() {}

func (x *AddVoteRecordResp) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddVoteRecordResp.ProtoReflect.Descriptor instead.
func (*AddVoteRecordResp) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{15}
}

func (x *AddVoteRecordResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdateVoteRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                         //id
	LotteryId      int64 `protobuf:"varint,2,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"`           //抽奖ID
	UserId         int64 `protobuf:"varint,3,opt,name=userId,proto3" json:"userId,omitempty"`                 //用户ID
	SelectedOption int64 `protobuf:"varint,4,opt,name=selectedOption,proto3" json:"selectedOption,omitempty"` //用户选择的投票选项
	CreatedAt      int64 `protobuf:"varint,5,opt,name=createdAt,proto3" json:"createdAt,omitempty"`           //投票时间
}

func (x *UpdateVoteRecordReq) Reset() {
	*x = UpdateVoteRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateVoteRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVoteRecordReq) ProtoMessage() {}

func (x *UpdateVoteRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVoteRecordReq.ProtoReflect.Descriptor instead.
func (*UpdateVoteRecordReq) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateVoteRecordReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateVoteRecordReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

func (x *UpdateVoteRecordReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UpdateVoteRecordReq) GetSelectedOption() int64 {
	if x != nil {
		return x.SelectedOption
	}
	return 0
}

func (x *UpdateVoteRecordReq) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

type VoteRecordDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptionText     string `protobuf:"bytes,1,opt,name=OptionText,proto3" json:"OptionText,omitempty"`          //投票选项名称
	SelectedOption int64  `protobuf:"varint,2,opt,name=SelectedOption,proto3" json:"SelectedOption,omitempty"` //投票选项序号
	OptionImage    string `protobuf:"bytes,3,opt,name=OptionImage,proto3" json:"OptionImage,omitempty"`        //投票选项图片,默认为空【表示文字投票选项】
	VoteCount      int64  `protobuf:"varint,4,opt,name=VoteCount,proto3" json:"VoteCount,omitempty"`           //选项已获投票数量
}

func (x *VoteRecordDetail) Reset() {
	*x = VoteRecordDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VoteRecordDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VoteRecordDetail) ProtoMessage() {}

func (x *VoteRecordDetail) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VoteRecordDetail.ProtoReflect.Descriptor instead.
func (*VoteRecordDetail) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{17}
}

func (x *VoteRecordDetail) GetOptionText() string {
	if x != nil {
		return x.OptionText
	}
	return ""
}

func (x *VoteRecordDetail) GetSelectedOption() int64 {
	if x != nil {
		return x.SelectedOption
	}
	return 0
}

func (x *VoteRecordDetail) GetOptionImage() string {
	if x != nil {
		return x.OptionImage
	}
	return ""
}

func (x *VoteRecordDetail) GetVoteCount() int64 {
	if x != nil {
		return x.VoteCount
	}
	return 0
}

type UserVoteRecordDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LotteryId      int64 `protobuf:"varint,1,opt,name=LotteryId,proto3" json:"LotteryId,omitempty"`           //抽奖ID
	SelectedOption int64 `protobuf:"varint,3,opt,name=SelectedOption,proto3" json:"SelectedOption,omitempty"` //用户选择的投票选项
}

func (x *UserVoteRecordDetail) Reset() {
	*x = UserVoteRecordDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserVoteRecordDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserVoteRecordDetail) ProtoMessage() {}

func (x *UserVoteRecordDetail) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserVoteRecordDetail.ProtoReflect.Descriptor instead.
func (*UserVoteRecordDetail) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{18}
}

func (x *UserVoteRecordDetail) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

func (x *UserVoteRecordDetail) GetSelectedOption() int64 {
	if x != nil {
		return x.SelectedOption
	}
	return 0
}

type UpdateVoteRecordResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateVoteRecordResp) Reset() {
	*x = UpdateVoteRecordResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateVoteRecordResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVoteRecordResp) ProtoMessage() {}

func (x *UpdateVoteRecordResp) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVoteRecordResp.ProtoReflect.Descriptor instead.
func (*UpdateVoteRecordResp) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{19}
}

type DelVoteRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *DelVoteRecordReq) Reset() {
	*x = DelVoteRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelVoteRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelVoteRecordReq) ProtoMessage() {}

func (x *DelVoteRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelVoteRecordReq.ProtoReflect.Descriptor instead.
func (*DelVoteRecordReq) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{20}
}

func (x *DelVoteRecordReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DelVoteRecordResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DelVoteRecordResp) Reset() {
	*x = DelVoteRecordResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelVoteRecordResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelVoteRecordResp) ProtoMessage() {}

func (x *DelVoteRecordResp) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelVoteRecordResp.ProtoReflect.Descriptor instead.
func (*DelVoteRecordResp) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{21}
}

type GetVoteRecordByIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` //id
}

func (x *GetVoteRecordByIdReq) Reset() {
	*x = GetVoteRecordByIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVoteRecordByIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVoteRecordByIdReq) ProtoMessage() {}

func (x *GetVoteRecordByIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVoteRecordByIdReq.ProtoReflect.Descriptor instead.
func (*GetVoteRecordByIdReq) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{22}
}

func (x *GetVoteRecordByIdReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetVoteRecordByIdResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VoteRecord *VoteRecord `protobuf:"bytes,1,opt,name=voteRecord,proto3" json:"voteRecord,omitempty"` //voteRecord
}

func (x *GetVoteRecordByIdResp) Reset() {
	*x = GetVoteRecordByIdResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVoteRecordByIdResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVoteRecordByIdResp) ProtoMessage() {}

func (x *GetVoteRecordByIdResp) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVoteRecordByIdResp.ProtoReflect.Descriptor instead.
func (*GetVoteRecordByIdResp) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{23}
}

func (x *GetVoteRecordByIdResp) GetVoteRecord() *VoteRecord {
	if x != nil {
		return x.VoteRecord
	}
	return nil
}

type GetVoteRecordDetailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LotteryId int64 `protobuf:"varint,1,opt,name=LotteryId,proto3" json:"LotteryId,omitempty"` //id
}

func (x *GetVoteRecordDetailReq) Reset() {
	*x = GetVoteRecordDetailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVoteRecordDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVoteRecordDetailReq) ProtoMessage() {}

func (x *GetVoteRecordDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVoteRecordDetailReq.ProtoReflect.Descriptor instead.
func (*GetVoteRecordDetailReq) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{24}
}

func (x *GetVoteRecordDetailReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

type GetVoteRecordDetailResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VoteRecordDetails []*VoteRecordDetail `protobuf:"bytes,1,rep,name=voteRecordDetails,proto3" json:"voteRecordDetails,omitempty"` // 投票选项的详细信息切片
}

func (x *GetVoteRecordDetailResp) Reset() {
	*x = GetVoteRecordDetailResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVoteRecordDetailResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVoteRecordDetailResp) ProtoMessage() {}

func (x *GetVoteRecordDetailResp) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVoteRecordDetailResp.ProtoReflect.Descriptor instead.
func (*GetVoteRecordDetailResp) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{25}
}

func (x *GetVoteRecordDetailResp) GetVoteRecordDetails() []*VoteRecordDetail {
	if x != nil {
		return x.VoteRecordDetails
	}
	return nil
}

type GetUserVoteRecordDetailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LotteryId int64 `protobuf:"varint,1,opt,name=LotteryId,proto3" json:"LotteryId,omitempty"` //id
	UserId    int64 `protobuf:"varint,5,opt,name=UserId,proto3" json:"UserId,omitempty"`       //用户ID
}

func (x *GetUserVoteRecordDetailReq) Reset() {
	*x = GetUserVoteRecordDetailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserVoteRecordDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserVoteRecordDetailReq) ProtoMessage() {}

func (x *GetUserVoteRecordDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserVoteRecordDetailReq.ProtoReflect.Descriptor instead.
func (*GetUserVoteRecordDetailReq) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{26}
}

func (x *GetUserVoteRecordDetailReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

func (x *GetUserVoteRecordDetailReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GetUserVoteRecordDetailResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserVoteRecordDetails []*UserVoteRecordDetail `protobuf:"bytes,1,rep,name=userVoteRecordDetails,proto3" json:"userVoteRecordDetails,omitempty"` // 用户投票详情
}

func (x *GetUserVoteRecordDetailResp) Reset() {
	*x = GetUserVoteRecordDetailResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserVoteRecordDetailResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserVoteRecordDetailResp) ProtoMessage() {}

func (x *GetUserVoteRecordDetailResp) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserVoteRecordDetailResp.ProtoReflect.Descriptor instead.
func (*GetUserVoteRecordDetailResp) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{27}
}

func (x *GetUserVoteRecordDetailResp) GetUserVoteRecordDetails() []*UserVoteRecordDetail {
	if x != nil {
		return x.UserVoteRecordDetails
	}
	return nil
}

type SearchVoteRecordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page           int64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`                     //page
	Limit          int64 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`                   //limit
	Id             int64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`                         //id
	LotteryId      int64 `protobuf:"varint,4,opt,name=lotteryId,proto3" json:"lotteryId,omitempty"`           //抽奖ID
	UserId         int64 `protobuf:"varint,5,opt,name=userId,proto3" json:"userId,omitempty"`                 //用户ID
	SelectedOption int64 `protobuf:"varint,6,opt,name=selectedOption,proto3" json:"selectedOption,omitempty"` //用户选择的投票选项
	CreatedAt      int64 `protobuf:"varint,7,opt,name=createdAt,proto3" json:"createdAt,omitempty"`           //投票时间
}

func (x *SearchVoteRecordReq) Reset() {
	*x = SearchVoteRecordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchVoteRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchVoteRecordReq) ProtoMessage() {}

func (x *SearchVoteRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchVoteRecordReq.ProtoReflect.Descriptor instead.
func (*SearchVoteRecordReq) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{28}
}

func (x *SearchVoteRecordReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchVoteRecordReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *SearchVoteRecordReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SearchVoteRecordReq) GetLotteryId() int64 {
	if x != nil {
		return x.LotteryId
	}
	return 0
}

func (x *SearchVoteRecordReq) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *SearchVoteRecordReq) GetSelectedOption() int64 {
	if x != nil {
		return x.SelectedOption
	}
	return 0
}

func (x *SearchVoteRecordReq) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

type SearchVoteRecordResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VoteRecord []*VoteRecord `protobuf:"bytes,1,rep,name=voteRecord,proto3" json:"voteRecord,omitempty"` //voteRecord
}

func (x *SearchVoteRecordResp) Reset() {
	*x = SearchVoteRecordResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_vote_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchVoteRecordResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchVoteRecordResp) ProtoMessage() {}

func (x *SearchVoteRecordResp) ProtoReflect() protoreflect.Message {
	mi := &file_vote_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchVoteRecordResp.ProtoReflect.Descriptor instead.
func (*SearchVoteRecordResp) Descriptor() ([]byte, []int) {
	return file_vote_proto_rawDescGZIP(), []int{29}
}

func (x *SearchVoteRecordResp) GetVoteRecord() []*VoteRecord {
	if x != nil {
		return x.VoteRecord
	}
	return nil
}

var File_vote_proto protoreflect.FileDescriptor

var file_vote_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x76, 0x6f, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x02, 0x70, 0x62,
	0x22, 0xd2, 0x01, 0x0a, 0x0a, 0x56, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65,
	0x72, 0x79, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f, 0x74, 0x74,
	0x65, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x56,
	0x6f, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x56, 0x6f, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x76, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x6f, 0x74, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x36, 0x0a, 0x0a, 0x56, 0x6f, 0x74, 0x65, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x54, 0x65, 0x78, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x22, 0xec, 0x01,
	0x0a, 0x12, 0x56, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4a, 0x53, 0x4f, 0x4e,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x0f,
	0x57, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x57, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x53, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x4d, 0x69,
	0x6e, 0x56, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x4d, 0x69,
	0x6e, 0x56, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x4d, 0x61, 0x78, 0x56, 0x6f, 0x74,
	0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x4d, 0x61, 0x78, 0x56, 0x6f, 0x74,
	0x65, 0x73, 0x12, 0x28, 0x0a, 0x07, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x62, 0x2e, 0x56, 0x6f, 0x74, 0x65, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x07, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x88, 0x01, 0x0a,
	0x10, 0x41, 0x64, 0x64, 0x56, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65,
	0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x74,
	0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x56, 0x6f, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x56, 0x6f, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x76, 0x6f, 0x74, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x6f, 0x74,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x23, 0x0a, 0x11, 0x41, 0x64, 0x64, 0x56, 0x6f,
	0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x83, 0x01, 0x0a,
	0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79,
	0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x56, 0x6f, 0x74, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x56, 0x6f,
	0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x76, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x22, 0x16, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x6f, 0x74, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x22, 0x22, 0x0a, 0x10, 0x44, 0x65,
	0x6c, 0x56, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x13,
	0x0a, 0x11, 0x44, 0x65, 0x6c, 0x56, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x26, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x56, 0x6f, 0x74, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x47, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x56, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x79, 0x49, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x2e, 0x0a, 0x0a, 0x76, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x62, 0x2e, 0x56, 0x6f,
	0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x76, 0x6f, 0x74, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x22, 0x85, 0x02, 0x0a, 0x13, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56,
	0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c,
	0x0a, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x56, 0x6f, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x56, 0x6f, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x76, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x76, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1e, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x46, 0x0a, 0x14,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x2e, 0x0a, 0x0a, 0x76, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x62, 0x2e, 0x56, 0x6f,
	0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x76, 0x6f, 0x74, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x22, 0xb4, 0x01, 0x0a, 0x0a, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x8e, 0x01, 0x0a, 0x10,
	0x41, 0x64, 0x64, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71,
	0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e,
	0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c,
	0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x23, 0x0a, 0x11,
	0x41, 0x64, 0x64, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x22, 0xa1, 0x01, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x6f, 0x74, 0x65,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x74,
	0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x6f,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x26, 0x0a, 0x0e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x9a, 0x01, 0x0a, 0x10, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x53, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x56, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x56, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x22, 0x5c, 0x0a, 0x14, 0x55, 0x73, 0x65, 0x72, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x4c, 0x6f,
	0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x4c,
	0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x53, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0x16, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x22, 0x22, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x56,
	0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x13, 0x0a, 0x11,
	0x44, 0x65, 0x6c, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x26, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x47, 0x0a, 0x15, 0x47, 0x65, 0x74,
	0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x2e, 0x0a, 0x0a, 0x76, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x62, 0x2e, 0x56, 0x6f, 0x74, 0x65,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x0a, 0x76, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x22, 0x36, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09,
	0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x4c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x22, 0x5d, 0x0a, 0x17, 0x47, 0x65,
	0x74, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x42, 0x0a, 0x11, 0x76, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x70, 0x62, 0x2e, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x11, 0x76, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x52, 0x0a, 0x1a, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x4c, 0x6f, 0x74, 0x74, 0x65,
	0x72, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x4c, 0x6f, 0x74, 0x74,
	0x65, 0x72, 0x79, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x6d, 0x0a,
	0x1b, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4e, 0x0a, 0x15,
	0x75, 0x73, 0x65, 0x72, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x62,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x15, 0x75, 0x73, 0x65, 0x72, 0x56, 0x6f, 0x74, 0x65, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xcb, 0x01, 0x0a,
	0x13, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c,
	0x0a, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x6c, 0x6f, 0x74, 0x74, 0x65, 0x72, 0x79, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x73, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x46, 0x0a, 0x14, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x2e, 0x0a, 0x0a, 0x76, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x62, 0x2e, 0x56, 0x6f, 0x74, 0x65,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x0a, 0x76, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x32, 0xda, 0x06, 0x0a, 0x04, 0x76, 0x6f, 0x74, 0x65, 0x12, 0x3c, 0x0a, 0x0d, 0x41,
	0x64, 0x64, 0x56, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x14, 0x2e, 0x70,
	0x62, 0x2e, 0x41, 0x64, 0x64, 0x56, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x65, 0x71, 0x1a, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x56, 0x6f, 0x74, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x12, 0x45, 0x0a, 0x10, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x56, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x17, 0x2e,
	0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x56, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x3c, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x56, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x14, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c, 0x56, 0x6f, 0x74, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c,
	0x56, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x12, 0x48,
	0x0a, 0x11, 0x47, 0x65, 0x74, 0x56, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42,
	0x79, 0x49, 0x64, 0x12, 0x18, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x6f, 0x74, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e,
	0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x45, 0x0a, 0x10, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x56, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x17, 0x2e, 0x70,
	0x62, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x56, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x3c, 0x0a, 0x0d, 0x41, 0x64, 0x64, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x12, 0x14, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x41, 0x64, 0x64, 0x56,
	0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x45, 0x0a,
	0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x12, 0x17, 0x2e, 0x70, 0x62, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x6f, 0x74,
	0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x70, 0x62, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x3c, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x56, 0x6f, 0x74, 0x65, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x14, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x6c, 0x56, 0x6f,
	0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x70, 0x62,
	0x2e, 0x44, 0x65, 0x6c, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x48, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x42, 0x79, 0x49, 0x64, 0x12, 0x18, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74,
	0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65,
	0x71, 0x1a, 0x19, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x42, 0x79, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4e, 0x0a, 0x13,
	0x47, 0x65, 0x74, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x1a, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x6f, 0x74, 0x65,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a,
	0x1b, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x5a, 0x0a, 0x17,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1e, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x45, 0x0a, 0x10, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x17, 0x2e, 0x70,
	0x62, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x56, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x42,
	0x06, 0x5a, 0x04, 0x2e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_vote_proto_rawDescOnce sync.Once
	file_vote_proto_rawDescData = file_vote_proto_rawDesc
)

func file_vote_proto_rawDescGZIP() []byte {
	file_vote_proto_rawDescOnce.Do(func() {
		file_vote_proto_rawDescData = protoimpl.X.CompressGZIP(file_vote_proto_rawDescData)
	})
	return file_vote_proto_rawDescData
}

var file_vote_proto_msgTypes = make([]protoimpl.MessageInfo, 30)
var file_vote_proto_goTypes = []interface{}{
	(*VoteConfig)(nil),                  // 0: pb.VoteConfig
	(*VoteOption)(nil),                  // 1: pb.VoteOption
	(*VoteConfigJSONData)(nil),          // 2: pb.VoteConfigJSONData
	(*AddVoteConfigReq)(nil),            // 3: pb.AddVoteConfigReq
	(*AddVoteConfigResp)(nil),           // 4: pb.AddVoteConfigResp
	(*UpdateVoteConfigReq)(nil),         // 5: pb.UpdateVoteConfigReq
	(*UpdateVoteConfigResp)(nil),        // 6: pb.UpdateVoteConfigResp
	(*DelVoteConfigReq)(nil),            // 7: pb.DelVoteConfigReq
	(*DelVoteConfigResp)(nil),           // 8: pb.DelVoteConfigResp
	(*GetVoteConfigByIdReq)(nil),        // 9: pb.GetVoteConfigByIdReq
	(*GetVoteConfigByIdResp)(nil),       // 10: pb.GetVoteConfigByIdResp
	(*SearchVoteConfigReq)(nil),         // 11: pb.SearchVoteConfigReq
	(*SearchVoteConfigResp)(nil),        // 12: pb.SearchVoteConfigResp
	(*VoteRecord)(nil),                  // 13: pb.VoteRecord
	(*AddVoteRecordReq)(nil),            // 14: pb.AddVoteRecordReq
	(*AddVoteRecordResp)(nil),           // 15: pb.AddVoteRecordResp
	(*UpdateVoteRecordReq)(nil),         // 16: pb.UpdateVoteRecordReq
	(*VoteRecordDetail)(nil),            // 17: pb.VoteRecordDetail
	(*UserVoteRecordDetail)(nil),        // 18: pb.UserVoteRecordDetail
	(*UpdateVoteRecordResp)(nil),        // 19: pb.UpdateVoteRecordResp
	(*DelVoteRecordReq)(nil),            // 20: pb.DelVoteRecordReq
	(*DelVoteRecordResp)(nil),           // 21: pb.DelVoteRecordResp
	(*GetVoteRecordByIdReq)(nil),        // 22: pb.GetVoteRecordByIdReq
	(*GetVoteRecordByIdResp)(nil),       // 23: pb.GetVoteRecordByIdResp
	(*GetVoteRecordDetailReq)(nil),      // 24: pb.GetVoteRecordDetailReq
	(*GetVoteRecordDetailResp)(nil),     // 25: pb.GetVoteRecordDetailResp
	(*GetUserVoteRecordDetailReq)(nil),  // 26: pb.GetUserVoteRecordDetailReq
	(*GetUserVoteRecordDetailResp)(nil), // 27: pb.GetUserVoteRecordDetailResp
	(*SearchVoteRecordReq)(nil),         // 28: pb.SearchVoteRecordReq
	(*SearchVoteRecordResp)(nil),        // 29: pb.SearchVoteRecordResp
}
var file_vote_proto_depIdxs = []int32{
	1,  // 0: pb.VoteConfigJSONData.Options:type_name -> pb.VoteOption
	0,  // 1: pb.GetVoteConfigByIdResp.voteConfig:type_name -> pb.VoteConfig
	0,  // 2: pb.SearchVoteConfigResp.voteConfig:type_name -> pb.VoteConfig
	13, // 3: pb.GetVoteRecordByIdResp.voteRecord:type_name -> pb.VoteRecord
	17, // 4: pb.GetVoteRecordDetailResp.voteRecordDetails:type_name -> pb.VoteRecordDetail
	18, // 5: pb.GetUserVoteRecordDetailResp.userVoteRecordDetails:type_name -> pb.UserVoteRecordDetail
	13, // 6: pb.SearchVoteRecordResp.voteRecord:type_name -> pb.VoteRecord
	3,  // 7: pb.vote.AddVoteConfig:input_type -> pb.AddVoteConfigReq
	5,  // 8: pb.vote.UpdateVoteConfig:input_type -> pb.UpdateVoteConfigReq
	7,  // 9: pb.vote.DelVoteConfig:input_type -> pb.DelVoteConfigReq
	9,  // 10: pb.vote.GetVoteConfigById:input_type -> pb.GetVoteConfigByIdReq
	11, // 11: pb.vote.SearchVoteConfig:input_type -> pb.SearchVoteConfigReq
	14, // 12: pb.vote.AddVoteRecord:input_type -> pb.AddVoteRecordReq
	16, // 13: pb.vote.UpdateVoteRecord:input_type -> pb.UpdateVoteRecordReq
	20, // 14: pb.vote.DelVoteRecord:input_type -> pb.DelVoteRecordReq
	22, // 15: pb.vote.GetVoteRecordById:input_type -> pb.GetVoteRecordByIdReq
	24, // 16: pb.vote.GetVoteRecordDetail:input_type -> pb.GetVoteRecordDetailReq
	26, // 17: pb.vote.GetUserVoteRecordDetail:input_type -> pb.GetUserVoteRecordDetailReq
	28, // 18: pb.vote.SearchVoteRecord:input_type -> pb.SearchVoteRecordReq
	4,  // 19: pb.vote.AddVoteConfig:output_type -> pb.AddVoteConfigResp
	6,  // 20: pb.vote.UpdateVoteConfig:output_type -> pb.UpdateVoteConfigResp
	8,  // 21: pb.vote.DelVoteConfig:output_type -> pb.DelVoteConfigResp
	10, // 22: pb.vote.GetVoteConfigById:output_type -> pb.GetVoteConfigByIdResp
	12, // 23: pb.vote.SearchVoteConfig:output_type -> pb.SearchVoteConfigResp
	15, // 24: pb.vote.AddVoteRecord:output_type -> pb.AddVoteRecordResp
	19, // 25: pb.vote.UpdateVoteRecord:output_type -> pb.UpdateVoteRecordResp
	21, // 26: pb.vote.DelVoteRecord:output_type -> pb.DelVoteRecordResp
	23, // 27: pb.vote.GetVoteRecordById:output_type -> pb.GetVoteRecordByIdResp
	25, // 28: pb.vote.GetVoteRecordDetail:output_type -> pb.GetVoteRecordDetailResp
	27, // 29: pb.vote.GetUserVoteRecordDetail:output_type -> pb.GetUserVoteRecordDetailResp
	29, // 30: pb.vote.SearchVoteRecord:output_type -> pb.SearchVoteRecordResp
	19, // [19:31] is the sub-list for method output_type
	7,  // [7:19] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_vote_proto_init() }
func file_vote_proto_init() {
	if File_vote_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_vote_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VoteConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VoteOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VoteConfigJSONData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddVoteConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddVoteConfigResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateVoteConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateVoteConfigResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelVoteConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelVoteConfigResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVoteConfigByIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVoteConfigByIdResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchVoteConfigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchVoteConfigResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VoteRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddVoteRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddVoteRecordResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateVoteRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VoteRecordDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserVoteRecordDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateVoteRecordResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelVoteRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelVoteRecordResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVoteRecordByIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVoteRecordByIdResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVoteRecordDetailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVoteRecordDetailResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserVoteRecordDetailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserVoteRecordDetailResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchVoteRecordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_vote_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchVoteRecordResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_vote_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   30,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_vote_proto_goTypes,
		DependencyIndexes: file_vote_proto_depIdxs,
		MessageInfos:      file_vote_proto_msgTypes,
	}.Build()
	File_vote_proto = out.File
	file_vote_proto_rawDesc = nil
	file_vote_proto_goTypes = nil
	file_vote_proto_depIdxs = nil
}
