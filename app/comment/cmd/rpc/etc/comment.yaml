Name: comment-rpc
ListenOn: 0.0.0.0:2009
Mode: dev

Log:
  ServiceName: comment-rpc
  Mode: console
  # 打印堆栈信息 方便查询错误
  Encoding: plain
  Level: error

#监控
Prometheus:
  Host: 0.0.0.0
  Port: 4032
  Path: /metrics

#链路追踪
Telemetry:
  Name: comment-rpc
  Endpoint: http://jaeger:14268/api/traces
  Sampler: 1.0
  Batcher: jaeger

Redis:
  Host: redis:6379
  Type: node
  Pass: G62m50oigInC30sf
  Key: comment-rpc
DB:
  DataSource: root:PXDN93VRKUm8TeE7@tcp(mysql:3306)/lottery?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai
Cache:
  - Host: redis:6379
    Pass: G62m50oigInC30sf

#rpc
UsercenterRpcConf:
  Endpoints:
    - 127.0.0.1:2004
  NonBlock: true