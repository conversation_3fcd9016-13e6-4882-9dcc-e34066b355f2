syntax = "proto3";

option go_package = "./pb";

package pb;


//model
message Homestay {
    int64   id = 1;
    string  title = 2;
    string  subTitle = 3;
    string  banner = 4;
    string  info = 5;
    int64   peopleNum = 6;            //容纳人的数量
    int64   homestayBusinessId = 7;   //店铺id
    int64   userId = 8;               //房东id
    int64   rowState = 9;             //0:下架 1:上架
    int64   rowType = 10;             //售卖类型0：按房间出售 1:按人次出售
    string  foodInfo = 11;            //餐食标准
    int64   foodPrice = 12;           //餐食价格(分)
    int64   homestayPrice = 13;       //民宿价格(分)
    int64   marketHomestayPrice = 14; //民宿市场价格(分)

}


//req 、resp
message HomestayDetailReq {
  int64   id = 1;
}
message HomestayDetailResp {
  Homestay homestay = 1;
}



//service
service travel {
    //homestayDetail
    rpc homestayDetail(HomestayDetailReq) returns(HomestayDetailResp);
}
