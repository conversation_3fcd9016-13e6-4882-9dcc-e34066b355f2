// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.19.4
// source: shop.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Shop_GetGoodsList_FullMethodName        = "/stream.Shop/getGoodsList"
	Shop_SearchGoods_FullMethodName         = "/stream.Shop/searchGoods"
	Shop_WishExchange_FullMethodName        = "/stream.Shop/wishExchange"
	Shop_GetWishExchangeList_FullMethodName = "/stream.Shop/getWishExchangeList"
	Shop_GetOrderList_FullMethodName        = "/stream.Shop/getOrderList"
	Shop_GetOrderPull_FullMethodName        = "/stream.Shop/getOrderPull"
)

// ShopClient is the client API for Shop service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ShopClient interface {
	GetGoodsList(ctx context.Context, in *GoodsListReq, opts ...grpc.CallOption) (*GoodsListResp, error)
	SearchGoods(ctx context.Context, in *GoodsSearchReq, opts ...grpc.CallOption) (*GoodsSearchResp, error)
	WishExchange(ctx context.Context, in *GoodsWishReq, opts ...grpc.CallOption) (*GoodsWishResp, error)
	GetWishExchangeList(ctx context.Context, in *GoodsWishListReq, opts ...grpc.CallOption) (*GoodsWishListResp, error)
	GetOrderList(ctx context.Context, in *OrderListReq, opts ...grpc.CallOption) (*OrderListResp, error)
	GetOrderPull(ctx context.Context, in *OrderPullReq, opts ...grpc.CallOption) (*OrderPullResp, error)
}

type shopClient struct {
	cc grpc.ClientConnInterface
}

func NewShopClient(cc grpc.ClientConnInterface) ShopClient {
	return &shopClient{cc}
}

func (c *shopClient) GetGoodsList(ctx context.Context, in *GoodsListReq, opts ...grpc.CallOption) (*GoodsListResp, error) {
	out := new(GoodsListResp)
	err := c.cc.Invoke(ctx, Shop_GetGoodsList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *shopClient) SearchGoods(ctx context.Context, in *GoodsSearchReq, opts ...grpc.CallOption) (*GoodsSearchResp, error) {
	out := new(GoodsSearchResp)
	err := c.cc.Invoke(ctx, Shop_SearchGoods_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *shopClient) WishExchange(ctx context.Context, in *GoodsWishReq, opts ...grpc.CallOption) (*GoodsWishResp, error) {
	out := new(GoodsWishResp)
	err := c.cc.Invoke(ctx, Shop_WishExchange_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *shopClient) GetWishExchangeList(ctx context.Context, in *GoodsWishListReq, opts ...grpc.CallOption) (*GoodsWishListResp, error) {
	out := new(GoodsWishListResp)
	err := c.cc.Invoke(ctx, Shop_GetWishExchangeList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *shopClient) GetOrderList(ctx context.Context, in *OrderListReq, opts ...grpc.CallOption) (*OrderListResp, error) {
	out := new(OrderListResp)
	err := c.cc.Invoke(ctx, Shop_GetOrderList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *shopClient) GetOrderPull(ctx context.Context, in *OrderPullReq, opts ...grpc.CallOption) (*OrderPullResp, error) {
	out := new(OrderPullResp)
	err := c.cc.Invoke(ctx, Shop_GetOrderPull_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ShopServer is the server API for Shop service.
// All implementations must embed UnimplementedShopServer
// for forward compatibility.
type ShopServer interface {
	GetGoodsList(context.Context, *GoodsListReq) (*GoodsListResp, error)
	SearchGoods(context.Context, *GoodsSearchReq) (*GoodsSearchResp, error)
	WishExchange(context.Context, *GoodsWishReq) (*GoodsWishResp, error)
	GetWishExchangeList(context.Context, *GoodsWishListReq) (*GoodsWishListResp, error)
	GetOrderList(context.Context, *OrderListReq) (*OrderListResp, error)
	GetOrderPull(context.Context, *OrderPullReq) (*OrderPullResp, error)
	mustEmbedUnimplementedShopServer()
}

// UnimplementedShopServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedShopServer struct{}

func (UnimplementedShopServer) GetGoodsList(context.Context, *GoodsListReq) (*GoodsListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGoodsList not implemented")
}
func (UnimplementedShopServer) SearchGoods(context.Context, *GoodsSearchReq) (*GoodsSearchResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchGoods not implemented")
}
func (UnimplementedShopServer) WishExchange(context.Context, *GoodsWishReq) (*GoodsWishResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WishExchange not implemented")
}
func (UnimplementedShopServer) GetWishExchangeList(context.Context, *GoodsWishListReq) (*GoodsWishListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWishExchangeList not implemented")
}
func (UnimplementedShopServer) GetOrderList(context.Context, *OrderListReq) (*OrderListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderList not implemented")
}
func (UnimplementedShopServer) GetOrderPull(context.Context, *OrderPullReq) (*OrderPullResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderPull not implemented")
}
func (UnimplementedShopServer) mustEmbedUnimplementedShopServer() {}
func (UnimplementedShopServer) testEmbeddedByValue()              {}

// UnsafeShopServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ShopServer will
// result in compilation errors.
type UnsafeShopServer interface {
	mustEmbedUnimplementedShopServer()
}

func RegisterShopServer(s grpc.ServiceRegistrar, srv ShopServer) {
	// If the following call pancis, it indicates UnimplementedShopServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Shop_ServiceDesc, srv)
}

func _Shop_GetGoodsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ShopServer).GetGoodsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Shop_GetGoodsList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ShopServer).GetGoodsList(ctx, req.(*GoodsListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Shop_SearchGoods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsSearchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ShopServer).SearchGoods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Shop_SearchGoods_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ShopServer).SearchGoods(ctx, req.(*GoodsSearchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Shop_WishExchange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsWishReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ShopServer).WishExchange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Shop_WishExchange_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ShopServer).WishExchange(ctx, req.(*GoodsWishReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Shop_GetWishExchangeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoodsWishListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ShopServer).GetWishExchangeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Shop_GetWishExchangeList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ShopServer).GetWishExchangeList(ctx, req.(*GoodsWishListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Shop_GetOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ShopServer).GetOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Shop_GetOrderList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ShopServer).GetOrderList(ctx, req.(*OrderListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Shop_GetOrderPull_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderPullReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ShopServer).GetOrderPull(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Shop_GetOrderPull_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ShopServer).GetOrderPull(ctx, req.(*OrderPullReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Shop_ServiceDesc is the grpc.ServiceDesc for Shop service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Shop_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "stream.Shop",
	HandlerType: (*ShopServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "getGoodsList",
			Handler:    _Shop_GetGoodsList_Handler,
		},
		{
			MethodName: "searchGoods",
			Handler:    _Shop_SearchGoods_Handler,
		},
		{
			MethodName: "wishExchange",
			Handler:    _Shop_WishExchange_Handler,
		},
		{
			MethodName: "getWishExchangeList",
			Handler:    _Shop_GetWishExchangeList_Handler,
		},
		{
			MethodName: "getOrderList",
			Handler:    _Shop_GetOrderList_Handler,
		},
		{
			MethodName: "getOrderPull",
			Handler:    _Shop_GetOrderPull_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "shop.proto",
}
