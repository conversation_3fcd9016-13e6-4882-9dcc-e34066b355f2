// Code generated by goctl. DO NOT EDIT.
// Source: shop.proto

package shop

import (
	"context"

	"looklook/app/shop/cmd/rpc/pb"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	GoodsDetail       = pb.GoodsDetail
	GoodsInfo         = pb.GoodsInfo
	GoodsListReq      = pb.GoodsListReq
	GoodsListResp     = pb.GoodsListResp
	GoodsSearchReq    = pb.GoodsSearchReq
	GoodsSearchResp   = pb.GoodsSearchResp
	GoodsWishListReq  = pb.GoodsWishListReq
	GoodsWishListResp = pb.GoodsWishListResp
	GoodsWishReq      = pb.GoodsWishReq
	GoodsWishResp     = pb.GoodsWishResp
	Order             = pb.Order
	OrderListReq      = pb.OrderListReq
	OrderListResp     = pb.OrderListResp
	OrderPullReq      = pb.OrderPullReq
	OrderPullResp     = pb.OrderPullResp
	WishExchange      = pb.WishExchange

	Shop interface {
		GetGoodsList(ctx context.Context, in *GoodsListReq, opts ...grpc.CallOption) (*GoodsListResp, error)
		SearchGoods(ctx context.Context, in *GoodsSearchReq, opts ...grpc.CallOption) (*GoodsSearchResp, error)
		WishExchange(ctx context.Context, in *GoodsWishReq, opts ...grpc.CallOption) (*GoodsWishResp, error)
		GetWishExchangeList(ctx context.Context, in *GoodsWishListReq, opts ...grpc.CallOption) (*GoodsWishListResp, error)
		GetOrderList(ctx context.Context, in *OrderListReq, opts ...grpc.CallOption) (*OrderListResp, error)
		GetOrderPull(ctx context.Context, in *OrderPullReq, opts ...grpc.CallOption) (*OrderPullResp, error)
	}

	defaultShop struct {
		cli zrpc.Client
	}
)

func NewShop(cli zrpc.Client) Shop {
	return &defaultShop{
		cli: cli,
	}
}

func (m *defaultShop) GetGoodsList(ctx context.Context, in *GoodsListReq, opts ...grpc.CallOption) (*GoodsListResp, error) {
	client := pb.NewShopClient(m.cli.Conn())
	return client.GetGoodsList(ctx, in, opts...)
}

func (m *defaultShop) SearchGoods(ctx context.Context, in *GoodsSearchReq, opts ...grpc.CallOption) (*GoodsSearchResp, error) {
	client := pb.NewShopClient(m.cli.Conn())
	return client.SearchGoods(ctx, in, opts...)
}

func (m *defaultShop) WishExchange(ctx context.Context, in *GoodsWishReq, opts ...grpc.CallOption) (*GoodsWishResp, error) {
	client := pb.NewShopClient(m.cli.Conn())
	return client.WishExchange(ctx, in, opts...)
}

func (m *defaultShop) GetWishExchangeList(ctx context.Context, in *GoodsWishListReq, opts ...grpc.CallOption) (*GoodsWishListResp, error) {
	client := pb.NewShopClient(m.cli.Conn())
	return client.GetWishExchangeList(ctx, in, opts...)
}

func (m *defaultShop) GetOrderList(ctx context.Context, in *OrderListReq, opts ...grpc.CallOption) (*OrderListResp, error) {
	client := pb.NewShopClient(m.cli.Conn())
	return client.GetOrderList(ctx, in, opts...)
}

func (m *defaultShop) GetOrderPull(ctx context.Context, in *OrderPullReq, opts ...grpc.CallOption) (*OrderPullResp, error) {
	client := pb.NewShopClient(m.cli.Conn())
	return client.GetOrderPull(ctx, in, opts...)
}
