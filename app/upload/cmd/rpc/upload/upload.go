// Code generated by goctl. DO NOT EDIT.
// Source: upload.proto

package upload

import (
	"context"

	"looklook/app/upload/cmd/rpc/pb"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	FileUploadReq  = pb.FileUploadReq
	FileUploadResp = pb.FileUploadResp

	Upload interface {
		Upload(ctx context.Context, in *FileUploadReq, opts ...grpc.CallOption) (*FileUploadResp, error)
	}

	defaultUpload struct {
		cli zrpc.Client
	}
)

func NewUpload(cli zrpc.Client) Upload {
	return &defaultUpload{
		cli: cli,
	}
}

func (m *defaultUpload) Upload(ctx context.Context, in *FileUploadReq, opts ...grpc.CallOption) (*FileUploadResp, error) {
	client := pb.NewUploadClient(m.cli.Conn())
	return client.Upload(ctx, in, opts...)
}
