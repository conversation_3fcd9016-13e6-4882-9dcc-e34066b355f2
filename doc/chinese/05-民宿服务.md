

### 五、民宿服务

本项目地址 :  https://github.com/Mikaelemmmm/go-zero-looklook



#### 1、民宿服务业务架构图

<img src="./images/5/image-20220428110630664.png" alt="image-20220428110630664" style="zoom:50%;" />





#### 2、依赖关系

 travel-api（民宿api） 依赖 travel-rpc（民宿rpc）、usercenter-rpc（用户中心rpc）



travel分为几个业务

- homestay ：民宿房源

```protobuf
//民宿模块v1版本的接口
@server(
	prefix: travel/v1
	group: homestay
)
service travel {
	
	@doc "民宿列表（为你优选）"
	@handler homestayList
	post /homestay/homestayList (HomestayListReq) returns (HomestayListResp)
	
	@doc "房东所有民宿列表"
	@handler businessList
	post /homestay/businessList (BusinessListReq) returns (BusinessListResp)
	
	@doc "猜你喜欢民宿列表"
	@handler guessList
	post /homestay/guessList (GuessListReq) returns (GuessListResp)
	
	@doc "民宿详情"
	@handler homestayDetail
	post /homestay/homestayDetail (HomestayDetailReq) returns (HomestayDetailResp)
	
}
```

- homestayBusiness ： 民宿店家

```protobuf
//店铺模块v1版本的接口
@server(
	prefix: travel/v1
	group: homestayBussiness
)
service travel {
	@doc "最佳房东"
	@handler goodBoss
	post /homestayBussiness/goodBoss (GoodBossReq) returns (GoodBossResp)
	
	@doc "店铺列表"
	@handler homestayBussinessList
	post /homestayBussiness/homestayBussinessList (HomestayBussinessListReq) returns (HomestayBussinessListResp)
	
	@doc "房东信息"
	@handler homestayBussinessDetail
	post /homestayBussiness/homestayBussinessDetail (HomestayBussinessDetailReq) returns (HomestayBussinessDetailResp)
}
```

- homestayComment ： 民宿评论

```protobuf
//民宿评论模块v1版本的接口
@server(
	prefix: travel/v1
	group: homestayComment
)
service travel {
	@doc "民宿评论列表"
	@handler commentList
	post /homestayComment/commentList (CommentListReq) returns (CommentListResp)
}
```





#### 3、举例：民宿列表（为你优选）

##### 1、api服务

1、写api接口文件

app/travel/cmd/api/desc/homestay/homestay.api

```protobuf
type (
	HomestayListReq {
		LastId   int64  `json:"lastId"`
		PageSize int64  `json:"pageSize"`
		RowType  string `json:"rowType"` //preferredHomestay:优选民宿
	}
	HomestayListResp {
		List []Homestay `json:"list"`
	}
)


```

app/travel/cmd/api/desc/travel.api

```protobuf
import (
	"homestay/homestay.api"
	....
)

//民宿模块v1版本的接口
@server(
	prefix: travel/v1
	group: homestay
)
service travel {
	
	@doc "民宿列表（为你优选）"
	@handler homestayList
	post /homestay/homestayList (HomestayListReq) returns (HomestayListResp)
	
	......
}
```

2、goctl生成api代码

1）命令行进入app/travel/cmd/api/desc目录下。

2）去项目目录下deploy/script/gencode/gen.sh中，复制如下一条命令，在命令行中执行（命令行要切换到app/travel/cmd目录）

```shell
$ goctl api go -api *.api -dir ../  -style=goZero
```

3、打开app/travel/cmd/api/internal/logic/homestay/homestayListLogic.go

![image-20220210190926487](./images/5/image-20220210190926487.png)

因我我们的推荐是在后台配置的，所以我们创建了一个活动表（这里你也可以选择配置到redis中），总之我们就是先从活动表中拿到配置的推荐民宿id，然后在通过id去获取对应民宿信息列表。

##### 2【小技巧】 mapreduce

这里可以看到，我拿到了id集合之后，不是普通的foreach一个个获取，而是使用了go-zero为我们封装好了的mapreduce获取数据，这样就可以并发去获取数据，而不是要去取一个完成之后在取下一个，时间上大大缩短了，这里只是想给搭建展示这样一个功能，有的同学非要较真，可以传递一个id slice或者id arr到rpc，然后在rpc中在去并发获取每个，这样也没什么不好，我这里只是给大家展示这个功能



##### 3、rpc服务

定义protobuf文件

app/travel/cmd/rpc/pb/travel.proto

```protobuf
//model
message Homestay {
    int64   id = 1;
    string  title = 2;
    string  subTitle = 3;
    string  banner = 4;
    string  info = 5;
    int64   peopleNum = 6;            //容纳人的数量
    int64   homestayBusinessId = 7;   //店铺id
    int64   userId = 8;               //房东id
    int64   rowState = 9;             //0:下架 1:上架
    int64   rowType = 10;             //售卖类型0：按房间出售 1:按人次出售
    string  foodInfo = 11;            //餐食标准
    int64   foodPrice = 12;           //餐食价格(分)
    int64   homestayPrice = 13;       //民宿价格(分)
    int64   marketHomestayPrice = 14; //民宿市场价格(分)
}

//req 、resp
message HomestayDetailReq {
  int64   id = 1;
}
message HomestayDetailResp {
  Homestay homestay = 1;
}

//service
service travel {
    //民宿详情
    rpc homestayDetail(HomestayDetailReq) returns(HomestayDetailResp);
}
```



- 使用goctl生成代码，这里不需要自己手动敲

  1）命令行进入app/travel/cmd/rpc/pb目录下。

  2）去项目目录下deploy/script/gencode/gen.sh中，复制如下两条命令，在命令行中执行（命令行要切换到app/travel/cmd目录）

  ```shell
  $ goctl rpc protoc *.proto --go_out=../ --go-grpc_out=../  --zrpc_out=../
  $ sed -i "" 's/,omitempty//g' *.pb.go
  ```

- 打开app/travel/cmd/rpc/internal/logic/homestayDetailLogic.go写逻辑代码

  ![image-20220120120423588](./images/5/image-20220120120423588.png)

  

  这里没什么逻辑，查询Findone，然后返回给api，因为api那边是通过id传递过来的，然后可以看到我们这边又一次使用了前一张提到的gorm作者提供的另外一款神器copier，上一节是在api中使用，将rpc的proto文件的数据copy到api文件 ， 这里可以看到，我们把model返回的数据copy给proto的数据同样可以用，怎么样是不是很方便。

  

  

  ##### 4【小技巧】 model cache、singleflight

  在这里为什么我们不去findlist，是因为我们在findone方法中有缓存，我们一个个根据id查询数据时候，只有第一次会命中db，其他时间基本都是命中的redis cache，这样不仅速度快，就算流量激增的时候，也不会全部打到db上，而是都在redis上，这样会大大提高我们系统的访问速度以及db支撑能力。

  一般我们自己维护db cache会写的零零散散，但是go-zero使用了配套内置工具goctl生成的model，自带sqlc+sqlx实现的代码，实现了自动缓存管理，我们根本不需要去管理缓存，只需要用sqlx写 sql数据，sqlc会自动帮我们管理缓存，并且是通过**singleflight** ,也就是说即使缓存在某个时间失效，在失效那一刻同时有大量并发请求进来时，go-zero在查询db时候也只会放行一个线程进来，其他线程是在等待，当这个线程从数据库拿数据回来之后将该数据缓存到redis同时所有之前等待线程共享此数据返回，后续在进来的线程查相同数据时，就只会进入到redis中而不会进入到db。

  

  
  
  这样rpc拿到所有数据之后，就可以返回给前端显示了
  
  
  
  #### 4、结尾
  
  其他的几个服务没有业务什么逻辑性的这里就不再一一说明，看api文档基本都知道是什么了，根据上面例子代码自行查看即可，后面有牵扯业务复杂的地方会逐一说明
  
  
  
  









