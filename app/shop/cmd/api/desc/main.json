{"swagger": "2.0", "info": {"title": "type title here", "description": "type desc here", "version": "type version here"}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"shop/v1/goodsInfo/getGoodsById": {"post": {"summary": "查询商品信息", "operationId": "getGoodsById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GoodsInfoResp"}}}, "parameters": [{"name": "body", "description": "商品信息的请求,拿到用户id和商品id", "in": "body", "required": true, "schema": {"$ref": "#/definitions/GoodsInfoReq"}}], "requestBody": {}, "tags": ["shop"]}}, "shop/v1/goodsInfo/getGoodsList": {"post": {"summary": "查询所有商品信息", "operationId": "getGoodsList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GoodsListResp"}}}, "parameters": [{"name": "body", "description": "商品列表的请求 传入页号", "in": "body", "required": true, "schema": {"$ref": "#/definitions/GoodsListReq"}}], "requestBody": {}, "tags": ["shop"]}}, "shop/v1/goodsInfo/syncPddGoods": {"post": {"summary": "同步拼多多数据", "operationId": "syncPddGoods", "responses": {"200": {"description": "A successful response.", "schema": {}}}, "requestBody": {}, "tags": ["shop"]}}}, "definitions": {"GoodsInfo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "categoryId": {"type": "integer", "format": "int64"}, "goodsId": {"type": "string"}, "precouponPrice": {"type": "number", "format": "double"}, "aftercouponPrice": {"type": "number", "format": "double"}, "goodsDesc": {"type": "string"}, "wishPoints": {"type": "integer", "format": "int64"}, "couponStartTime": {"type": "integer", "format": "int64"}, "couponEndTime": {"type": "integer", "format": "int64"}, "couponDiscount": {"type": "integer", "format": "int64"}, "couponRemainQuantity": {"type": "integer", "format": "int64"}}, "title": "GoodsInfo", "required": ["id", "categoryId", "goodsId", "precouponPrice", "aftercouponPrice", "goodsDesc", "wishPoints", "couponStartTime", "couponEndTime", "couponDiscount", "couponRemainQuantity"]}, "GoodsInfoReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "商品id"}}, "title": "GoodsInfoReq", "required": ["id"]}, "GoodsInfoResp": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "categoryId": {"type": "integer", "format": "int64"}, "goodsId": {"type": "string"}, "precouponPrice": {"type": "number", "format": "double"}, "aftercouponPrice": {"type": "number", "format": "double"}, "goodsDesc": {"type": "string"}, "wishPoints": {"type": "integer", "format": "int64"}, "couponStartTime": {"type": "integer", "format": "int64"}, "couponEndTime": {"type": "integer", "format": "int64"}, "couponDiscount": {"type": "integer", "format": "int64"}, "couponRemainQuantity": {"type": "integer", "format": "int64"}}, "title": "GoodsInfoResp", "required": ["id", "categoryId", "goodsId", "precouponPrice", "aftercouponPrice", "goodsDesc", "wishPoints", "couponStartTime", "couponEndTime", "couponDiscount", "couponRemainQuantity"]}, "GoodsListReq": {"type": "object", "properties": {"pageSize": {"type": "integer", "format": "int64"}}, "title": "GoodsListReq", "required": ["pageSize"]}, "GoodsListResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/GoodsInfo"}}}, "title": "GoodsListResp", "required": ["list"]}}, "securityDefinitions": {"apiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Enter JWT Bearer token **_only_**", "name": "Authorization", "in": "header"}}, "security": [{"apiKey": []}]}