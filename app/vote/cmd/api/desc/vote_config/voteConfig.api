syntax = "v1"

info(
    title: "投票服务"
    desc: "投票服务"
    author: "mike"
    email: "<EMAIL>"
    version: "v1"
)


// VoteConfigJSONData 是用于存储 voteConfig 字段的结构体类型
type VoteConfigJSONData {
    Title string `json:"title" validate:"required"`                                                                       //投票标题
    Description string `json:"description" validate:"required"`                                                           //投票描述【非必填】
    WinnerSelection int64 `json:"winnerSelection" validate:"eq=0|eq=1"`                                          //中奖者设置：1从所有投票者中抽取 2从票数最多的一方中抽取
    Type int64 `json:"type" validate:"eq=0|eq=1"`                                                                //投票类型：1单选 2多选
    MinVotes int64 `json:"minVotes" validate:"required"`                                                                  //最小投票范围
    MaxVotes int64 `json:"maxVotes"`                                                                                      //最大投票范围
    Options []VoteOption `json:"options" validate:"required,dive,required"`                                               //选项列表
}

// VoteOption 用于存储每个选项的信息
type VoteOption {
    Text string `json:"text" validate:"required"`                       //选项名称
    Image string `json:"image" validate:"required"`                     //选项图片
}

type (
    CreateVoteReq {
        LotteryId int64 `json:"lotteryId" validate:"required"`                                                   //抽奖id
        EnableVote int64 `json:"enableVote" validate:"eq=0|eq=1"`                                       //是否启用投票功能 1是 0否
        VoteConfig VoteConfigJSONData `json:"voteConfig" validate:"required"`                                    //投票配置字段说明
    }

    CreateVoteResp {
        Id int64 `json:"id"`
    }
)

type (
    UpdateVoteReq {
        Id int64 `json:"id" validate:"required"`                              //编辑投票id
        EnableVote int64 `json:"enableVote" validate:"eq=0|eq=1"`              //是否启用投票功能 1是 0否
        VoteConfig VoteConfigJSONData `json:"voteConfig" validate:"required"` //投票配置字段说明
    }
    UpdateVoteResp {
    }
)

type (
    EnableVoteReq {
        LotteryId int64 `json:"lotteryId" validate:"required"`  //抽奖id
    }

    EnableVoteResp {
        DelState int64 `json:"id"` //投票状态
    }
)

type (
    ViewVoteDetailReq {
        Id int64 `json:"id"`                              //投票id
    }

    ViewVoteDetailResp {
        Id int64 `json:"id"`                              //投票id
        UserId int64 `json:"userId"`                      //用户id
        LotteryId int64 `json:"lotteryId"`                //抽奖id
        EnableVote int64 `json:"enableVote"`              //是否启用投票功能 1是 0否
        VoteConfig VoteConfigJSONData `json:"voteConfig"` //投票配置字段说明
    }
)
