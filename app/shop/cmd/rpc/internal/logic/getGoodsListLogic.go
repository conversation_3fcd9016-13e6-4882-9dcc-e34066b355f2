package logic

import (
	"context"
	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
	"looklook/app/shop/cmd/rpc/internal/svc"
	"looklook/app/shop/cmd/rpc/pb"
)

type GetGoodsListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetGoodsListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetGoodsListLogic {
	return &GetGoodsListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetGoodsListLogic) GetGoodsList(in *pb.GoodsListReq) (*pb.GoodsListResp, error) {
	list, err := l.svcCtx.GoodsModel.GetGoodsListByLastId(in.LastId, int(in.Size))
	if err != nil {
		return nil, err
	}

	goods := make([]*pb.GoodsInfo, 0)

	for _, item := range list {
		pbGoods := new(pb.GoodsInfo)
		_ = copier.Copy(pbGoods, item)
		goods = append(goods, pbGoods)
	}
	return &pb.GoodsListResp{
		Total: int64(len(list)),
		List:  goods,
	}, nil
}
