// Code generated by goctl. DO NOT EDIT.

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
	"looklook/common/globalkey"
)

var (
	checkinRecordFieldNames          = builder.RawFieldNames(&CheckinRecord{})
	checkinRecordRows                = strings.Join(checkinRecordFieldNames, ",")
	checkinRecordRowsExpectAutoSet   = strings.Join(stringx.Remove(checkinRecordFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), ",")
	checkinRecordRowsWithPlaceHolder = strings.Join(stringx.Remove(checkinRecordFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), "=?,") + "=?"

	cacheCheckinCheckinRecordIdPrefix = "cache:checkin:checkinRecord:id:"
)

type (
	checkinRecordModel interface {
		Insert(ctx context.Context, data *CheckinRecord) (sql.Result, error)
		TransInsert(ctx context.Context, session sqlx.Session, data *CheckinRecord) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*CheckinRecord, error)
		Update(ctx context.Context, data *CheckinRecord) error
		List(ctx context.Context, page, limit int64) ([]*CheckinRecord, error)
		TransUpdate(ctx context.Context, session sqlx.Session, data *CheckinRecord) error
		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		SelectBuilder() squirrel.SelectBuilder
		FindSum(ctx context.Context, sumBuilder squirrel.SelectBuilder, field string) (float64, error)
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder, field string) (int64, error)
		FindAll(ctx context.Context, rowBuilder squirrel.SelectBuilder, orderBy string) ([]*CheckinRecord, error)
		FindPageListByPage(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*CheckinRecord, error)
		FindPageListByPageWithTotal(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*CheckinRecord, int64, error)
		FindPageListByIdDESC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*CheckinRecord, error)
		FindPageListByIdASC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*CheckinRecord, error)
		Delete(ctx context.Context, id int64) error
	}

	defaultCheckinRecordModel struct {
		sqlc.CachedConn
		table string
	}

	CheckinRecord struct {
		Id                    int64        `db:"id"`
		UserId                int64        `db:"user_id"`
		ContinuousCheckinDays int64        `db:"continuous_checkin_days"` // Number of consecutive check-in days
		State                 int64        `db:"state"`                   // Whether to sign in on the day, 1 means signed, 0 means not signed.
		LastCheckinDate       sql.NullTime `db:"last_checkin_date"`       // Date of last check-in
	}
)

func newCheckinRecordModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultCheckinRecordModel {
	return &defaultCheckinRecordModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`checkin_record`",
	}
}

func (m *defaultCheckinRecordModel) Delete(ctx context.Context, id int64) error {
	checkinCheckinRecordIdKey := fmt.Sprintf("%s%v", cacheCheckinCheckinRecordIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		return conn.ExecCtx(ctx, query, id)
	}, checkinCheckinRecordIdKey)
	return err
}

func (m *defaultCheckinRecordModel) FindOne(ctx context.Context, id int64) (*CheckinRecord, error) {
	checkinCheckinRecordIdKey := fmt.Sprintf("%s%v", cacheCheckinCheckinRecordIdPrefix, id)
	var resp CheckinRecord
	err := m.QueryRowCtx(ctx, &resp, checkinCheckinRecordIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", checkinRecordRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultCheckinRecordModel) Insert(ctx context.Context, data *CheckinRecord) (sql.Result, error) {
	checkinCheckinRecordIdKey := fmt.Sprintf("%s%v", cacheCheckinCheckinRecordIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, checkinRecordRowsExpectAutoSet)
		return conn.ExecCtx(ctx, query, data.UserId, data.ContinuousCheckinDays, data.State, data.LastCheckinDate)
	}, checkinCheckinRecordIdKey)
	return ret, err
}

func (m *defaultCheckinRecordModel) TransInsert(ctx context.Context, session sqlx.Session, data *CheckinRecord) (sql.Result, error) {
	checkinCheckinRecordIdKey := fmt.Sprintf("%s%v", cacheCheckinCheckinRecordIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, checkinRecordRowsExpectAutoSet)
		return session.ExecCtx(ctx, query, data.UserId, data.ContinuousCheckinDays, data.State, data.LastCheckinDate)
	}, checkinCheckinRecordIdKey)
	return ret, err
}
func (m *defaultCheckinRecordModel) Update(ctx context.Context, data *CheckinRecord) error {
	checkinCheckinRecordIdKey := fmt.Sprintf("%s%v", cacheCheckinCheckinRecordIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, checkinRecordRowsWithPlaceHolder)
		return conn.ExecCtx(ctx, query, data.UserId, data.ContinuousCheckinDays, data.State, data.LastCheckinDate, data.Id)
	}, checkinCheckinRecordIdKey)
	return err
}

func (m *defaultCheckinRecordModel) TransUpdate(ctx context.Context, session sqlx.Session, data *CheckinRecord) error {
	checkinCheckinRecordIdKey := fmt.Sprintf("%s%v", cacheCheckinCheckinRecordIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, checkinRecordRowsWithPlaceHolder)
		return session.ExecCtx(ctx, query, data.UserId, data.ContinuousCheckinDays, data.State, data.LastCheckinDate, data.Id)
	}, checkinCheckinRecordIdKey)
	return err
}

func (m *defaultCheckinRecordModel) List(ctx context.Context, page, limit int64) ([]*CheckinRecord, error) {
	query := fmt.Sprintf("select %s from %s limit ?,?", checkinRecordRows, m.table)
	var resp []*CheckinRecord
	//err := m.conn.QueryRowsCtx(ctx, &resp, query, (page-1)*limit, limit)
	err := m.QueryRowsNoCacheCtx(ctx, &resp, query, (page-1)*limit, limit)
	return resp, err
}

func (m *defaultCheckinRecordModel) Trans(ctx context.Context, fn func(ctx context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, func(ctx context.Context, session sqlx.Session) error {
		return fn(ctx, session)
	})
}

func (m *defaultCheckinRecordModel) FindSum(ctx context.Context, builder squirrel.SelectBuilder, field string) (float64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindSum Least One Field"), "FindSum Least One Field")
	}

	builder = builder.Columns("IFNULL(SUM(" + field + "),0)")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp float64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultCheckinRecordModel) FindCount(ctx context.Context, builder squirrel.SelectBuilder, field string) (int64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindCount Least One Field"), "FindCount Least One Field")
	}

	builder = builder.Columns("COUNT(" + field + ")")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultCheckinRecordModel) FindAll(ctx context.Context, builder squirrel.SelectBuilder, orderBy string) ([]*CheckinRecord, error) {

	builder = builder.Columns(checkinRecordRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*CheckinRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultCheckinRecordModel) FindPageListByPage(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*CheckinRecord, error) {

	builder = builder.Columns(checkinRecordRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*CheckinRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultCheckinRecordModel) FindPageListByPageWithTotal(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*CheckinRecord, int64, error) {

	total, err := m.FindCount(ctx, builder, "id")
	if err != nil {
		return nil, 0, err
	}

	builder = builder.Columns(checkinRecordRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, total, err
	}

	var resp []*CheckinRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, total, nil
	default:
		return nil, total, err
	}
}

func (m *defaultCheckinRecordModel) FindPageListByIdDESC(ctx context.Context, builder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*CheckinRecord, error) {

	builder = builder.Columns(checkinRecordRows)

	if preMinId > 0 {
		builder = builder.Where(" id < ? ", preMinId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id DESC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*CheckinRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultCheckinRecordModel) FindPageListByIdASC(ctx context.Context, builder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*CheckinRecord, error) {

	builder = builder.Columns(checkinRecordRows)

	if preMaxId > 0 {
		builder = builder.Where(" id > ? ", preMaxId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id ASC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*CheckinRecord
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultCheckinRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select().From(m.table)
}

func (m *defaultCheckinRecordModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheCheckinCheckinRecordIdPrefix, primary)
}

func (m *defaultCheckinRecordModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", checkinRecordRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultCheckinRecordModel) tableName() string {
	return m.table
}
