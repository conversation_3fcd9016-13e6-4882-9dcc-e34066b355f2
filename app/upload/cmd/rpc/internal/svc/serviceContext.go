package svc

import (
	"github.com/qiniu/go-sdk/v7/auth/qbox"
	"github.com/qiniu/go-sdk/v7/storage"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"looklook/app/upload/cmd/rpc/internal/config"
	"looklook/app/upload/model"
)

type ServiceContext struct {
	Config          config.Config
	RedisClient     *redis.Redis
	FileUploadModel model.UploadFileModel
	//OssClient       *oss.Client
	UploadToken    string
	Base64Uploader *storage.Base64Uploader
}

func NewServiceContext(c config.Config) *ServiceContext {
	sqlConn := sqlx.NewMysql(c.DB.DataSource)
	//client, err := oss.New(c.Oss.Endpoint, c.Oss.AccessKeyId, c.Oss.AccessKeySecret)
	//if err != nil {
	//	log.Errorf(context.Background(), "Testt-1 %v", err.Error())
	//}
	//log.Infof(context.Background(), "Testt-2: key, value")
	//_ = client.SetBucketACL(c.Oss.BucketName, oss.ACLPublicRead)

	mac := qbox.NewMac(c.Oss.AccessKeyId, c.Oss.AccessKeySecret)

	cfg := storage.Config{
		Zone:          &storage.ZoneHuanan, // 根据实际情况选择存储区域
		UseHTTPS:      false,
		UseCdnDomains: false,
	}

	putPolicy := storage.PutPolicy{
		Scope: c.Oss.BucketName,
	}

	upToken := putPolicy.UploadToken(mac)

	formUploader := storage.NewBase64Uploader(&cfg)

	return &ServiceContext{
		Config: c,
		RedisClient: redis.New(c.Redis.Host, func(r *redis.Redis) {
			r.Type = c.Redis.Type
			r.Pass = c.Redis.Pass
		}),
		FileUploadModel: model.NewUploadFileModel(sqlConn, c.Cache),
		//OssClient:       client,
		UploadToken:    upToken,
		Base64Uploader: formUploader,
	}
}
