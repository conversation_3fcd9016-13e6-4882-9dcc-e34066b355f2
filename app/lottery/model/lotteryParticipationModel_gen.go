// Code generated by goctl. DO NOT EDIT.

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
	"looklook/common/globalkey"
)

var (
	lotteryParticipationFieldNames          = builder.RawFieldNames(&LotteryParticipation{})
	lotteryParticipationRows                = strings.Join(lotteryParticipationFieldNames, ",")
	lotteryParticipationRowsExpectAutoSet   = strings.Join(stringx.Remove(lotteryParticipationFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), ",")
	lotteryParticipationRowsWithPlaceHolder = strings.Join(stringx.Remove(lotteryParticipationFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), "=?,") + "=?"

	cacheGozeroLotteryParticipationIdPrefix              = "cache:gozero:lotteryParticipation:id:"
	cacheGozeroLotteryParticipationLotteryIdUserIdPrefix = "cache:gozero:lotteryParticipation:lotteryId:userId:"
)

type (
	lotteryParticipationModel interface {
		Insert(ctx context.Context, data *LotteryParticipation) (sql.Result, error)
		TransInsert(ctx context.Context, session sqlx.Session, data *LotteryParticipation) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*LotteryParticipation, error)
		FindOneByLotteryIdUserId(ctx context.Context, lotteryId int64, userId int64) (*LotteryParticipation, error)
		Update(ctx context.Context, data *LotteryParticipation) error
		List(ctx context.Context, page, limit int64) ([]*LotteryParticipation, error)
		TransUpdate(ctx context.Context, session sqlx.Session, data *LotteryParticipation) error
		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		SelectBuilder() squirrel.SelectBuilder
		FindSum(ctx context.Context, sumBuilder squirrel.SelectBuilder, field string) (float64, error)
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder, field string) (int64, error)
		FindAll(ctx context.Context, rowBuilder squirrel.SelectBuilder, orderBy string) ([]*LotteryParticipation, error)
		FindPageListByPage(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*LotteryParticipation, error)
		FindPageListByPageWithTotal(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*LotteryParticipation, int64, error)
		FindPageListByIdDESC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*LotteryParticipation, error)
		FindPageListByIdASC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*LotteryParticipation, error)
		Delete(ctx context.Context, id int64) error
	}

	defaultLotteryParticipationModel struct {
		sqlc.CachedConn
		table string
	}

	LotteryParticipation struct {
		Id        int64 `db:"id"`         // 主键
		LotteryId int64 `db:"lottery_id"` // 参与的抽奖的id
		UserId    int64 `db:"user_id"`    // 用户id
		IsWon     int64 `db:"is_won"`     // 中奖了吗？
		PrizeId   int64 `db:"prize_id"`   // 中奖id
	}
)

func newLotteryParticipationModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultLotteryParticipationModel {
	return &defaultLotteryParticipationModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`lottery_participation`",
	}
}

func (m *defaultLotteryParticipationModel) Delete(ctx context.Context, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	gozeroLotteryParticipationIdKey := fmt.Sprintf("%s%v", cacheGozeroLotteryParticipationIdPrefix, id)
	gozeroLotteryParticipationLotteryIdUserIdKey := fmt.Sprintf("%s%v:%v", cacheGozeroLotteryParticipationLotteryIdUserIdPrefix, data.LotteryId, data.UserId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		return conn.ExecCtx(ctx, query, id)
	}, gozeroLotteryParticipationIdKey, gozeroLotteryParticipationLotteryIdUserIdKey)
	return err
}

func (m *defaultLotteryParticipationModel) FindOne(ctx context.Context, id int64) (*LotteryParticipation, error) {
	gozeroLotteryParticipationIdKey := fmt.Sprintf("%s%v", cacheGozeroLotteryParticipationIdPrefix, id)
	var resp LotteryParticipation
	err := m.QueryRowCtx(ctx, &resp, gozeroLotteryParticipationIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", lotteryParticipationRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultLotteryParticipationModel) FindOneByLotteryIdUserId(ctx context.Context, lotteryId int64, userId int64) (*LotteryParticipation, error) {
	gozeroLotteryParticipationLotteryIdUserIdKey := fmt.Sprintf("%s%v:%v", cacheGozeroLotteryParticipationLotteryIdUserIdPrefix, lotteryId, userId)
	var resp LotteryParticipation
	err := m.QueryRowIndexCtx(ctx, &resp, gozeroLotteryParticipationLotteryIdUserIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `lottery_id` = ? and `user_id` = ? limit 1", lotteryParticipationRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, lotteryId, userId); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultLotteryParticipationModel) Insert(ctx context.Context, data *LotteryParticipation) (sql.Result, error) {
	gozeroLotteryParticipationIdKey := fmt.Sprintf("%s%v", cacheGozeroLotteryParticipationIdPrefix, data.Id)
	gozeroLotteryParticipationLotteryIdUserIdKey := fmt.Sprintf("%s%v:%v", cacheGozeroLotteryParticipationLotteryIdUserIdPrefix, data.LotteryId, data.UserId)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, lotteryParticipationRowsExpectAutoSet)
		return conn.ExecCtx(ctx, query, data.LotteryId, data.UserId, data.IsWon, data.PrizeId)
	}, gozeroLotteryParticipationIdKey, gozeroLotteryParticipationLotteryIdUserIdKey)
	return ret, err
}

func (m *defaultLotteryParticipationModel) TransInsert(ctx context.Context, session sqlx.Session, data *LotteryParticipation) (sql.Result, error) {
	gozeroLotteryParticipationIdKey := fmt.Sprintf("%s%v", cacheGozeroLotteryParticipationIdPrefix, data.Id)
	gozeroLotteryParticipationLotteryIdUserIdKey := fmt.Sprintf("%s%v:%v", cacheGozeroLotteryParticipationLotteryIdUserIdPrefix, data.LotteryId, data.UserId)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?)", m.table, lotteryParticipationRowsExpectAutoSet)
		return session.ExecCtx(ctx, query, data.LotteryId, data.UserId, data.IsWon, data.PrizeId)
	}, gozeroLotteryParticipationIdKey, gozeroLotteryParticipationLotteryIdUserIdKey)
	return ret, err
}
func (m *defaultLotteryParticipationModel) Update(ctx context.Context, newData *LotteryParticipation) error {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return err
	}

	gozeroLotteryParticipationIdKey := fmt.Sprintf("%s%v", cacheGozeroLotteryParticipationIdPrefix, data.Id)
	gozeroLotteryParticipationLotteryIdUserIdKey := fmt.Sprintf("%s%v:%v", cacheGozeroLotteryParticipationLotteryIdUserIdPrefix, data.LotteryId, data.UserId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, lotteryParticipationRowsWithPlaceHolder)
		return conn.ExecCtx(ctx, query, newData.LotteryId, newData.UserId, newData.IsWon, newData.PrizeId, newData.Id)
	}, gozeroLotteryParticipationIdKey, gozeroLotteryParticipationLotteryIdUserIdKey)
	return err
}

func (m *defaultLotteryParticipationModel) TransUpdate(ctx context.Context, session sqlx.Session, newData *LotteryParticipation) error {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return err
	}

	gozeroLotteryParticipationIdKey := fmt.Sprintf("%s%v", cacheGozeroLotteryParticipationIdPrefix, data.Id)
	gozeroLotteryParticipationLotteryIdUserIdKey := fmt.Sprintf("%s%v:%v", cacheGozeroLotteryParticipationLotteryIdUserIdPrefix, data.LotteryId, data.UserId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, lotteryParticipationRowsWithPlaceHolder)
		return session.ExecCtx(ctx, query, newData.LotteryId, newData.UserId, newData.IsWon, newData.PrizeId, newData.Id)
	}, gozeroLotteryParticipationIdKey, gozeroLotteryParticipationLotteryIdUserIdKey)
	return err
}

func (m *defaultLotteryParticipationModel) List(ctx context.Context, page, limit int64) ([]*LotteryParticipation, error) {
	query := fmt.Sprintf("select %s from %s limit ?,?", lotteryParticipationRows, m.table)
	var resp []*LotteryParticipation
	//err := m.conn.QueryRowsCtx(ctx, &resp, query, (page-1)*limit, limit)
	err := m.QueryRowsNoCacheCtx(ctx, &resp, query, (page-1)*limit, limit)
	return resp, err
}

func (m *defaultLotteryParticipationModel) Trans(ctx context.Context, fn func(ctx context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, func(ctx context.Context, session sqlx.Session) error {
		return fn(ctx, session)
	})
}

func (m *defaultLotteryParticipationModel) FindSum(ctx context.Context, builder squirrel.SelectBuilder, field string) (float64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindSum Least One Field"), "FindSum Least One Field")
	}

	builder = builder.Columns("IFNULL(SUM(" + field + "),0)")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp float64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultLotteryParticipationModel) FindCount(ctx context.Context, builder squirrel.SelectBuilder, field string) (int64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindCount Least One Field"), "FindCount Least One Field")
	}

	builder = builder.Columns("COUNT(" + field + ")")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultLotteryParticipationModel) FindAll(ctx context.Context, builder squirrel.SelectBuilder, orderBy string) ([]*LotteryParticipation, error) {

	builder = builder.Columns(lotteryParticipationRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*LotteryParticipation
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultLotteryParticipationModel) FindPageListByPage(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*LotteryParticipation, error) {

	builder = builder.Columns(lotteryParticipationRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*LotteryParticipation
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultLotteryParticipationModel) FindPageListByPageWithTotal(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*LotteryParticipation, int64, error) {

	total, err := m.FindCount(ctx, builder, "id")
	if err != nil {
		return nil, 0, err
	}

	builder = builder.Columns(lotteryParticipationRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, total, err
	}

	var resp []*LotteryParticipation
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, total, nil
	default:
		return nil, total, err
	}
}

func (m *defaultLotteryParticipationModel) FindPageListByIdDESC(ctx context.Context, builder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*LotteryParticipation, error) {

	builder = builder.Columns(lotteryParticipationRows)

	if preMinId > 0 {
		builder = builder.Where(" id < ? ", preMinId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id DESC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*LotteryParticipation
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultLotteryParticipationModel) FindPageListByIdASC(ctx context.Context, builder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*LotteryParticipation, error) {

	builder = builder.Columns(lotteryParticipationRows)

	if preMaxId > 0 {
		builder = builder.Where(" id > ? ", preMaxId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id ASC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*LotteryParticipation
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultLotteryParticipationModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select().From(m.table)
}

func (m *defaultLotteryParticipationModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheGozeroLotteryParticipationIdPrefix, primary)
}

func (m *defaultLotteryParticipationModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", lotteryParticipationRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultLotteryParticipationModel) tableName() string {
	return m.table
}
