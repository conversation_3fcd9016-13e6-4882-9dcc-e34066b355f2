package logic

import (
	"context"
	"fmt"
	"github.com/qiniu/go-sdk/v7/auth/qbox"

	"testing"
)
import (
	"github.com/qiniu/go-sdk/v7/storage"
)

// 定义七牛云配置信息
const (
	//AccessKey = "your-access-key"
	//SecretKey = "your-secret-key"
	//Bucket    = "your-bucket-name"
	AccessKey = "XbB11gkI4QylgGb5UP8t9XHhCwV28fE9EAcqV6jT"
	SecretKey = "9zGAjrRV8gDd-7u8Ej6E4N3krMDRElFtmrXH5Y6_"
	Bucket    = "lottery-love"
)

func TestQiniu(t *testing.T) {
	// 创建MAC对象
	mac := qbox.NewMac(AccessKey, SecretKey)

	// 配置存储区域
	cfg := storage.Config{
		Zone:          &storage.ZoneHuanan, // 根据实际情况选择存储区域
		UseHTTPS:      false,
		UseCdnDomains: false,
	}

	// 创建Bucket管理器
	//bucketManager := storage.NewBucketManager(mac, &cfg)

	// 此处可以进行后续的文件上传和下载操作

	// 生成上传凭证
	putPolicy := storage.PutPolicy{
		Scope: Bucket,
	}
	upToken := putPolicy.UploadToken(mac)

	// 构建表单上传对象
	formUploader := storage.NewFormUploader(&cfg)
	// 上传文件
	localFile := "./example.png" // 本地文件路径
	key := "example.png"         // 七牛云中的文件名
	ret := storage.PutRet{}

	err := formUploader.PutFile(context.Background(), &ret, upToken, key, localFile, nil)
	if err != nil {
		fmt.Println("上传失败：", err)
		return
	}
	fmt.Printf("上传成功，文件Key：%s\n", ret.Key)
}
