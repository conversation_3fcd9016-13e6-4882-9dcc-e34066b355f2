package logic

import (
	"context"
	"fmt"
	"github.com/valyala/fastjson"
	"looklook/app/shop/cmd/rpc/internal/svc"
	"looklook/app/shop/cmd/rpc/pb"
	"looklook/common/tool"
	"strconv"

	"github.com/zeromicro/go-zero/core/logx"
)

type SearchGoodsLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSearchGoodsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchGoodsLogic {
	return &SearchGoodsLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *SearchGoodsLogic) SearchGoods(in *pb.GoodsSearchReq) (*pb.GoodsSearchResp, error) {
	// todo: add your logic here and delete this line
	// 2、调用多多进宝的搜索商品接口，根据关键字搜索商品 http调用 pdd.ddk.goods.search
	// 3、将搜索到的商品信息返回给调用方，数据的转换，copy

	// -1、对入参进行处理，生成sign（按照入参的字母顺序排序，kv拼接，头部及尾部分别拼接client_secret，MD5，大写转换）
	//clientSecret := "4ded09b0a14eb72196f790a93c06eafcfb75ceb8"
	//pid := "42316735_301157319"
	//
	//// 0、参数校验，判断当前用户是否备案 调用http请求 pdd.ddk.member.authority.query  clientid client_secret custom_parameters pid timestamp 得到的sign  返回的结果中的bind字段为1表示已备案 0 表示未备案，需要备案
	//params := map[string]string{
	//	"custom_parameters": fmt.Sprintf("{\"uid\":%d}", in.UserId),
	//	"type":              "pdd.ddk.member.authority.query",
	//	"pid":               pid,
	//	"client_secret":     clientSecret,
	//}
	//body, err := tool.SendRequestToDDJB(params, "POST")
	//if err != nil {
	//	return nil, err
	//}
	//// 打印响应体
	////logx.Errorf("Testt-5 Response body: %s", string(body))
	//
	//bind := fastjson.GetInt(body, "authority_query_response", "bind")
	//if bind == 0 {
	//	// 1、没有授权，进行授权接口的调用 pdd.ddk.rp.prom.url.generate 生成备案链接 clientid client_secret custom_parameters pid timestamp 得到的sign 返回的结果中的url即为备案链接，前端跳转到备案链接进行备案
	//	params1 := map[string]string{
	//		"custom_parameters": fmt.Sprintf("{\"uid\":%d}", in.UserId),
	//		"channel_type":      "10",
	//		"client_secret":     clientSecret,
	//		"p_id_list":         fmt.Sprintf("[\"%s\"]", pid),
	//		"type":              "pdd.ddk.rp.prom.url.generate", // ["42316735_301157319"]
	//	}
	//
	//	logx.Errorf("Testt-14 params: %v", params1["p_id_list"])
	//
	//	body1, err := tool.SendRequestToDDJB(params1, "POST")
	//	if err != nil {
	//		return nil, err
	//	}
	//
	//	// 打印响应体
	//	logx.Errorf("Testt-6 Response body: %v", string(body1))
	//	return nil, errors.New("请先备案")
	//}

	// 2、调用多多进宝的搜索商品接口，根据关键字搜索商品 http调用 pdd.ddk.goods.search
	params := map[string]string{
		"custom_parameters": fmt.Sprintf("{\"uid\":%d}", in.UserId),
		"type":              "pdd.ddk.goods.search",
		"keyword":           in.Keyword,
		"page":              fmt.Sprintf("%d", in.Page),
		"page_size":         fmt.Sprintf("%d", in.PageSize),
	}

	body, err := tool.SendRequestToDDJB(params, "POST")
	if err != nil {
		return nil, err
	}

	// 打印响应体
	logx.Errorf("Testt-7 Response body: %s", string(body))

	// 3、将搜索到的商品信息返回给调用方，数据的转换，copy
	var list []*pb.GoodsInfo
	// 解析响应体
	// 1、解析响应体
	// 2、将解析出来的商品信息转换为pb.GoodsInfo
	// 3、将pb.GoodsInfo添加到list中
	// 4、将list返回给调用方

	parse, err := fastjson.Parse(string(body))
	if err != nil {
		return nil, err
	}
	for _, v := range parse.GetArray("goods_search_response", "goods_list") {

		var optIds []int64
		optIdsValue := v.GetArray("opt_ids")
		for _, optId := range optIdsValue {
			//optId.GetUint64()
			optIds = append(optIds, int64(optId.GetUint64()))
		}

		list = append(list, &pb.GoodsInfo{
			GoodsId:           int64(v.GetUint64("goods_id")),
			GoodsName:         string(v.GetStringBytes("goods_name")),
			GoodsDesc:         string(v.GetStringBytes("goods_desc")),
			GoodsImageUrl:     string(v.GetStringBytes("goods_image_url")),
			GoodsThumbnailUrl: string(v.GetStringBytes("goods_thumbnail_url")),
			MinGroupPrice:     int64(v.GetUint64("min_group_price")),
			MinNormalPrice:    int64(v.GetUint64("min_normal_price")),
			MallId:            int64(v.GetUint64("mall_id")),
			MallName:          string(v.GetStringBytes("mall_name")),
			CategoryId:        strconv.FormatInt(int64(v.GetUint64("category_id")), 10),
			CategoryName:      string(v.GetStringBytes("category_name")),
			GoodsSign:         string(v.GetStringBytes("goods_sign")),
			OptId:             strconv.FormatInt(int64(v.GetUint64("opt_id")), 10),
			OptName:           string(v.GetStringBytes("opt_name")),
			OptIds:            optIds,
		})
	}

	return &pb.GoodsSearchResp{}, nil
}
