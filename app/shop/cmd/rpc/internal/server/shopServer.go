// Code generated by goctl. DO NOT EDIT.
// Source: shop.proto

package server

import (
	"context"

	"looklook/app/shop/cmd/rpc/internal/logic"
	"looklook/app/shop/cmd/rpc/internal/svc"
	"looklook/app/shop/cmd/rpc/pb"
)

type ShopServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedShopServer
}

func NewShopServer(svcCtx *svc.ServiceContext) *ShopServer {
	return &ShopServer{
		svcCtx: svcCtx,
	}
}

func (s *ShopServer) GetGoodsList(ctx context.Context, in *pb.GoodsListReq) (*pb.GoodsListResp, error) {
	l := logic.NewGetGoodsListLogic(ctx, s.svcCtx)
	return l.GetGoodsList(in)
}

func (s *ShopServer) SearchGoods(ctx context.Context, in *pb.GoodsSearchReq) (*pb.GoodsSearchResp, error) {
	l := logic.NewSearchGoodsLogic(ctx, s.svcCtx)
	return l.SearchGoods(in)
}

func (s *ShopServer) WishExchange(ctx context.Context, in *pb.GoodsWishReq) (*pb.GoodsWishResp, error) {
	l := logic.NewWishExchangeLogic(ctx, s.svcCtx)
	return l.WishExchange(in)
}

func (s *ShopServer) GetWishExchangeList(ctx context.Context, in *pb.GoodsWishListReq) (*pb.GoodsWishListResp, error) {
	l := logic.NewGetWishExchangeListLogic(ctx, s.svcCtx)
	return l.GetWishExchangeList(in)
}

func (s *ShopServer) GetOrderList(ctx context.Context, in *pb.OrderListReq) (*pb.OrderListResp, error) {
	l := logic.NewGetOrderListLogic(ctx, s.svcCtx)
	return l.GetOrderList(in)
}

func (s *ShopServer) GetOrderPull(ctx context.Context, in *pb.OrderPullReq) (*pb.OrderPullResp, error) {
	l := logic.NewGetOrderPullLogic(ctx, s.svcCtx)
	return l.GetOrderPull(in)
}
