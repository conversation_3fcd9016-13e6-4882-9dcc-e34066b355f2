Name: payment-api
Host: 0.0.0.0
Port: 1002
Mode: dev

#jwtAuth
JwtAuth:
  AccessSecret: ae0536f9-6450-4606-8e13-5a19ed505da0

#监控
Prometheus:
  Host: 0.0.0.0
  Port: 4004
  Path: /metrics

#链路追踪
Telemetry:
  Name: payment-api
  Endpoint: http://jaeger:14268/api/traces
  Sampler: 1.0
  Batcher: jaeger

Log:
  ServiceName: payment-api
  Level: error

#小程序配置
WxMiniConf:
  AppId: wx2add729fadddddd
  Secret: 20365395b3de9b2a9351ddddddd

#WxPayConf
WxPayConf:
  MchId: "11234455"
  SerialNo: 333333964008A80E5524718049ABB343232323
  APIv3Key: twewe23vjXrlnpTd5afyZZJ6FrI2323dsd
  PrivateKey: |-
    -----BEGIN PRIVATE KEY-----
    11BADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDBY/kxOoNkZxgK
    /OKq+xMX5Uv+oeELdbNq9qZOYVs4Vi7FrSyGYcPQyqItRKhl7sCZJRsqec7pFZUv
    hDyP0yYv+cADRnTHODkdVmogFS0YUH9ocmO4cArZORkG4Mu5+m87PpzsB/637kGa
    n1lFxzMJqT/YqhynVqJtFhL/kVukig+nYcpueiemye/6EBpQYm9+ffZLr0l6l2n9
    Zm3zEo+scBBRrrFvZGu1DdskH19Mk9/Z19H+2zb2xu3G/vt8/ndoRrWtwz33beDc
    Bxm2QfyXjV8GiJ/DYYrgIamPHLVNCSWes8V77nLJNbUS0hLAJJlfnD79CWtQo8gj
    UrUvj+CzAgMBAAECggEARQGAVKGVaskimiEcXHhUms017bkjjSxFhiBWvVcd7p9J
    -----END PRIVATE KEY-----

  NotifyUrl : http://xxx.xxx.com/payment/v1/thirdPayment/thirdPaymentWxPayCallback

#rpc service
PaymentRpcConf:
  Endpoints:
    - 127.0.0.1:2002
  NonBlock: true

OrderRpcConf:
  Endpoints:
    - 127.0.0.1:2001
  NonBlock: true

UsercenterRpcConf:
  Endpoints:
    - 127.0.0.1:2004
  NonBlock: true
