// Code generated by goctl. DO NOT EDIT.

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"
	"looklook/common/globalkey"
)

var (
	userSponsorFieldNames          = builder.RawFieldNames(&UserSponsor{})
	userSponsorRows                = strings.Join(userSponsorFieldNames, ",")
	userSponsorRowsExpectAutoSet   = strings.Join(stringx.Remove(userSponsorFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), ",")
	userSponsorRowsWithPlaceHolder = strings.Join(stringx.Remove(userSponsorFieldNames, "`id`", "`create_at`", "`create_time`", "`created_at`", "`update_at`", "`update_time`", "`updated_at`"), "=?,") + "=?"

	cacheLooklookUsercenterUserSponsorIdPrefix = "cache:looklookUsercenter:userSponsor:id:"
)

type (
	userSponsorModel interface {
		Insert(ctx context.Context, data *UserSponsor) (sql.Result, error)
		TransInsert(ctx context.Context, session sqlx.Session, data *UserSponsor) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*UserSponsor, error)
		Update(ctx context.Context, data *UserSponsor) error
		List(ctx context.Context, page, limit int64) ([]*UserSponsor, error)
		TransUpdate(ctx context.Context, session sqlx.Session, data *UserSponsor) error
		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		SelectBuilder() squirrel.SelectBuilder
		FindSum(ctx context.Context, sumBuilder squirrel.SelectBuilder, field string) (float64, error)
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder, field string) (int64, error)
		FindAll(ctx context.Context, rowBuilder squirrel.SelectBuilder, orderBy string) ([]*UserSponsor, error)
		FindPageListByPage(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*UserSponsor, error)
		FindPageListByPageWithTotal(ctx context.Context, rowBuilder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*UserSponsor, int64, error)
		FindPageListByIdDESC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*UserSponsor, error)
		FindPageListByIdASC(ctx context.Context, rowBuilder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*UserSponsor, error)
		Delete(ctx context.Context, id int64) error
	}

	defaultUserSponsorModel struct {
		sqlc.CachedConn
		table string
	}

	UserSponsor struct {
		Id         int64        `db:"id"`
		UserId     int64        `db:"user_id"`
		Type       int64        `db:"type"`        // 1微信号 2公众号 3小程序 4微信群 5视频号
		AppletType int64        `db:"applet_type"` // type=3时该字段才有意义，1小程序链接 2路径跳转 3二维码跳转
		Name       string       `db:"name"`        // 名称
		Desc       string       `db:"desc"`        // 描述
		Avatar     string       `db:"avatar"`
		IsShow     int64        `db:"is_show"` // 1显示 2不显示
		QrCode     string       `db:"qr_code"` // 二维码图片地址, type=1 2 3&applet_type=3 4的时候启用
		InputA     string       `db:"input_a"` // type=5 applet_type=2 or applet_type=1 输入框A
		InputB     string       `db:"input_b"` // type=5 applet_type=2输入框B
		CreateTime time.Time    `db:"create_time"`
		UpdateTime time.Time    `db:"update_time"`
		DeleteTime sql.NullTime `db:"delete_time"`
	}
)

func newUserSponsorModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultUserSponsorModel {
	return &defaultUserSponsorModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`user_sponsor`",
	}
}

func (m *defaultUserSponsorModel) Delete(ctx context.Context, id int64) error {
	looklookUsercenterUserSponsorIdKey := fmt.Sprintf("%s%v", cacheLooklookUsercenterUserSponsorIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		return conn.ExecCtx(ctx, query, id)
	}, looklookUsercenterUserSponsorIdKey)
	return err
}

func (m *defaultUserSponsorModel) FindOne(ctx context.Context, id int64) (*UserSponsor, error) {
	looklookUsercenterUserSponsorIdKey := fmt.Sprintf("%s%v", cacheLooklookUsercenterUserSponsorIdPrefix, id)
	var resp UserSponsor
	err := m.QueryRowCtx(ctx, &resp, looklookUsercenterUserSponsorIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", userSponsorRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUserSponsorModel) Insert(ctx context.Context, data *UserSponsor) (sql.Result, error) {
	looklookUsercenterUserSponsorIdKey := fmt.Sprintf("%s%v", cacheLooklookUsercenterUserSponsorIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, userSponsorRowsExpectAutoSet)
		return conn.ExecCtx(ctx, query, data.UserId, data.Type, data.AppletType, data.Name, data.Desc, data.Avatar, data.IsShow, data.QrCode, data.InputA, data.InputB, data.DeleteTime)
	}, looklookUsercenterUserSponsorIdKey)
	return ret, err
}

func (m *defaultUserSponsorModel) TransInsert(ctx context.Context, session sqlx.Session, data *UserSponsor) (sql.Result, error) {
	looklookUsercenterUserSponsorIdKey := fmt.Sprintf("%s%v", cacheLooklookUsercenterUserSponsorIdPrefix, data.Id)
	ret, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, userSponsorRowsExpectAutoSet)
		return session.ExecCtx(ctx, query, data.UserId, data.Type, data.AppletType, data.Name, data.Desc, data.Avatar, data.IsShow, data.QrCode, data.InputA, data.InputB, data.DeleteTime)
	}, looklookUsercenterUserSponsorIdKey)
	return ret, err
}
func (m *defaultUserSponsorModel) Update(ctx context.Context, data *UserSponsor) error {
	looklookUsercenterUserSponsorIdKey := fmt.Sprintf("%s%v", cacheLooklookUsercenterUserSponsorIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, userSponsorRowsWithPlaceHolder)
		return conn.ExecCtx(ctx, query, data.UserId, data.Type, data.AppletType, data.Name, data.Desc, data.Avatar, data.IsShow, data.QrCode, data.InputA, data.InputB, data.DeleteTime, data.Id)
	}, looklookUsercenterUserSponsorIdKey)
	return err
}

func (m *defaultUserSponsorModel) TransUpdate(ctx context.Context, session sqlx.Session, data *UserSponsor) error {
	looklookUsercenterUserSponsorIdKey := fmt.Sprintf("%s%v", cacheLooklookUsercenterUserSponsorIdPrefix, data.Id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, userSponsorRowsWithPlaceHolder)
		return session.ExecCtx(ctx, query, data.UserId, data.Type, data.AppletType, data.Name, data.Desc, data.Avatar, data.IsShow, data.QrCode, data.InputA, data.InputB, data.DeleteTime, data.Id)
	}, looklookUsercenterUserSponsorIdKey)
	return err
}

func (m *defaultUserSponsorModel) List(ctx context.Context, page, limit int64) ([]*UserSponsor, error) {
	query := fmt.Sprintf("select %s from %s limit ?,?", userSponsorRows, m.table)
	var resp []*UserSponsor
	//err := m.conn.QueryRowsCtx(ctx, &resp, query, (page-1)*limit, limit)
	err := m.QueryRowsNoCacheCtx(ctx, &resp, query, (page-1)*limit, limit)
	return resp, err
}

func (m *defaultUserSponsorModel) Trans(ctx context.Context, fn func(ctx context.Context, session sqlx.Session) error) error {
	return m.TransactCtx(ctx, func(ctx context.Context, session sqlx.Session) error {
		return fn(ctx, session)
	})
}

func (m *defaultUserSponsorModel) FindSum(ctx context.Context, builder squirrel.SelectBuilder, field string) (float64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindSum Least One Field"), "FindSum Least One Field")
	}

	builder = builder.Columns("IFNULL(SUM(" + field + "),0)")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp float64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultUserSponsorModel) FindCount(ctx context.Context, builder squirrel.SelectBuilder, field string) (int64, error) {

	if len(field) == 0 {
		return 0, errors.Wrapf(errors.New("FindCount Least One Field"), "FindCount Least One Field")
	}

	builder = builder.Columns("COUNT(" + field + ")")

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *defaultUserSponsorModel) FindAll(ctx context.Context, builder squirrel.SelectBuilder, orderBy string) ([]*UserSponsor, error) {

	builder = builder.Columns(userSponsorRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*UserSponsor
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultUserSponsorModel) FindPageListByPage(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*UserSponsor, error) {

	builder = builder.Columns(userSponsorRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*UserSponsor
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultUserSponsorModel) FindPageListByPageWithTotal(ctx context.Context, builder squirrel.SelectBuilder, page, pageSize int64, orderBy string) ([]*UserSponsor, int64, error) {

	total, err := m.FindCount(ctx, builder, "id")
	if err != nil {
		return nil, 0, err
	}

	builder = builder.Columns(userSponsorRows)

	if orderBy == "" {
		builder = builder.OrderBy("id DESC")
	} else {
		builder = builder.OrderBy(orderBy)
	}

	if page < 1 {
		page = 1
	}
	offset := (page - 1) * pageSize

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).Offset(uint64(offset)).Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, total, err
	}

	var resp []*UserSponsor
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, total, nil
	default:
		return nil, total, err
	}
}

func (m *defaultUserSponsorModel) FindPageListByIdDESC(ctx context.Context, builder squirrel.SelectBuilder, preMinId, pageSize int64) ([]*UserSponsor, error) {

	builder = builder.Columns(userSponsorRows)

	if preMinId > 0 {
		builder = builder.Where(" id < ? ", preMinId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id DESC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*UserSponsor
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultUserSponsorModel) FindPageListByIdASC(ctx context.Context, builder squirrel.SelectBuilder, preMaxId, pageSize int64) ([]*UserSponsor, error) {

	builder = builder.Columns(userSponsorRows)

	if preMaxId > 0 {
		builder = builder.Where(" id > ? ", preMaxId)
	}

	query, values, err := builder.Where("del_state = ?", globalkey.DelStateNo).OrderBy("id ASC").Limit(uint64(pageSize)).ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*UserSponsor
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *defaultUserSponsorModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select().From(m.table)
}

func (m *defaultUserSponsorModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheLooklookUsercenterUserSponsorIdPrefix, primary)
}

func (m *defaultUserSponsorModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", userSponsorRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultUserSponsorModel) tableName() string {
	return m.table
}
