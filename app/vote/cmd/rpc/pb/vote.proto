syntax = "proto3";

option go_package ="./pb";

package pb;

// ------------------------------------ 
// Messages
// ------------------------------------ 

//--------------------------------投票表--------------------------------
message VoteConfig {
  int64 id = 1; //id
  int64 userId = 2; //用户id
  int64 lotteryId = 3; //抽奖ID
  int64 enableVote = 4; //是否启用投票功能 1是 0否
  string voteConfig = 5; //投票配置字段说明: {"title": "投票标题", "description": "投票描述【非必填】", "winner_selection": "中奖者设置：1从所有投票者中抽取 2从票数最多的一方中抽取", "type": "投票类型：1单选 2多选", "min_votes": "最小投票范围", "max_votes": "最大投票范围", "options": [{"text": "张三", "image": "path/to/zhangsan.jpg"}, {"text": "李四", "image": "path/to/lisi.jpg"}, {"text": "王五", "image": "path/to/wangwu.jpg"}]}
  int64 createTime = 6; //createTime
  int64 updateTime = 7; //updateTime
}

message VoteOption {
  string Text = 1; //选项名称
  string Image = 2; //选项图片
}

message VoteConfigJSONData {
  string Title = 1; //投票标题
  string Description = 2; //投票描述【非必填】
  int64 WinnerSelection = 3; //中奖者设置：1从所有投票者中抽取 2从票数最多的一方中抽取
  int64 Type = 4; //投票类型：1单选 2多选
  int64 MinVotes = 5; //最小投票范围
  int64 MaxVotes = 6; //最大投票范围
  repeated VoteOption Options = 7; //选项列表
}

message AddVoteConfigReq {
  int64 userId = 1; //用户id
  int64 lotteryId = 2; //抽奖ID
  int64 enableVote = 3; //是否启用投票功能 1是 0否
  string voteConfig = 4; //投票配置字段说明: {"title": "投票标题", "description": "投票描述【非必填】", "winner_selection": "中奖者设置：1从所有投票者中抽取 2从票数最多的一方中抽取", "type": "投票类型：1单选 2多选", "min_votes": "最小投票范围", "max_votes": "最大投票范围", "options": [{"text": "张三", "image": "path/to/zhangsan.jpg"}, {"text": "李四", "image": "path/to/lisi.jpg"}, {"text": "王五", "image": "path/to/wangwu.jpg"}]}
}

message AddVoteConfigResp {
  int64 id = 1; //id
}

message UpdateVoteConfigReq {
  int64 id = 1; //id
  int64 lotteryId = 3; //抽奖ID
  int64 enableVote = 4; //是否启用投票功能 1是 0否
  string voteConfig = 5; //投票配置字段说明: {"title": "投票标题", "description": "投票描述【非必填】", "winner_selection": "中奖者设置：1从所有投票者中抽取 2从票数最多的一方中抽取", "type": "投票类型：1单选 2多选", "min_votes": "最小投票范围", "max_votes": "最大投票范围", "options": [{"text": "张三", "image": "path/to/zhangsan.jpg"}, {"text": "李四", "image": "path/to/lisi.jpg"}, {"text": "王五", "image": "path/to/wangwu.jpg"}]}
}

message UpdateVoteConfigResp {
}

message DelVoteConfigReq {
  int64 id = 1; //id
}

message DelVoteConfigResp {
}

message GetVoteConfigByIdReq {
  int64 id = 1; //id
}

message GetVoteConfigByIdResp {
  VoteConfig voteConfig = 1; //voteConfig
}

message SearchVoteConfigReq {
  int64 page = 1; //page
  int64 limit = 2; //limit
  int64 id = 3; //id
  int64 userId = 4; //用户id
  int64 lotteryId = 5; //抽奖ID
  int64 enableVote = 6; //是否启用投票功能 1是 0否
  string voteConfig = 7; //投票配置字段说明: {"title": "投票标题", "description": "投票描述【非必填】", "winner_selection": "中奖者设置：1从所有投票者中抽取 2从票数最多的一方中抽取", "type": "投票类型：1单选 2多选", "min_votes": "最小投票范围", "max_votes": "最大投票范围", "options": [{"text": "张三", "image": "path/to/zhangsan.jpg"}, {"text": "李四", "image": "path/to/lisi.jpg"}, {"text": "王五", "image": "path/to/wangwu.jpg"}]}
  int64 createTime = 8; //createTime
  int64 updateTime = 9; //updateTime
}

message SearchVoteConfigResp {
  repeated VoteConfig voteConfig = 1; //voteConfig
}

//--------------------------------投票记录表--------------------------------
message VoteRecord {
  int64 id = 1; //id
  int64 lotteryId = 2; //抽奖ID
  int64 userId = 3; //用户ID
  int64 selectedOption = 4; //用户选择的投票选项
  int64 delState = 5;
  int64 createdAt = 6; //投票时间
}

message AddVoteRecordReq {
  int64 lotteryId = 1; //抽奖ID
  int64 userId = 2; //用户ID
  int64 selectedOption = 3; //用户选择的投票选项
  int64 createdAt = 4; //投票时间
}

message AddVoteRecordResp {
  int64 id = 1; //id
}

message UpdateVoteRecordReq {
  int64 id = 1; //id
  int64 lotteryId = 2; //抽奖ID
  int64 userId = 3; //用户ID
  int64 selectedOption = 4; //用户选择的投票选项
  int64 createdAt = 5; //投票时间
}

message VoteRecordDetail {
  string OptionText = 1; //投票选项名称
  int64 SelectedOption = 2; //投票选项序号
  string OptionImage = 3; //投票选项图片,默认为空【表示文字投票选项】
  int64 VoteCount = 4; //选项已获投票数量
}

message UserVoteRecordDetail {
  int64 LotteryId = 1; //抽奖ID
  int64 SelectedOption = 3; //用户选择的投票选项
}

message UpdateVoteRecordResp {
}

message DelVoteRecordReq {
  int64 id = 1; //id
}

message DelVoteRecordResp {
}

message GetVoteRecordByIdReq {
  int64 id = 1; //id
}

message GetVoteRecordByIdResp {
  VoteRecord voteRecord = 1; //voteRecord
}

message GetVoteRecordDetailReq {
  int64 LotteryId = 1; //id
}

message GetVoteRecordDetailResp {
  repeated VoteRecordDetail voteRecordDetails = 1; // 投票选项的详细信息切片
}

message GetUserVoteRecordDetailReq {
  int64 LotteryId = 1; //id
  int64 UserId = 5; //用户ID
}

message GetUserVoteRecordDetailResp {
  repeated UserVoteRecordDetail userVoteRecordDetails = 1; // 用户投票详情
}

message SearchVoteRecordReq {
  int64 page = 1; //page
  int64 limit = 2; //limit
  int64 id = 3; //id
  int64 lotteryId = 4; //抽奖ID
  int64 userId = 5; //用户ID
  int64 selectedOption = 6; //用户选择的投票选项
  int64 createdAt = 7; //投票时间
}

message SearchVoteRecordResp {
  repeated VoteRecord voteRecord = 1; //voteRecord
}



// ------------------------------------ 
// Rpc Func
// ------------------------------------ 

service vote{ 

	 //-----------------------投票表----------------------- 
	 rpc AddVoteConfig(AddVoteConfigReq) returns (AddVoteConfigResp); 
	 rpc UpdateVoteConfig(UpdateVoteConfigReq) returns (UpdateVoteConfigResp); 
	 rpc DelVoteConfig(DelVoteConfigReq) returns (DelVoteConfigResp); 
	 rpc GetVoteConfigById(GetVoteConfigByIdReq) returns (GetVoteConfigByIdResp); 
	 rpc SearchVoteConfig(SearchVoteConfigReq) returns (SearchVoteConfigResp); 
	 //-----------------------投票记录表----------------------- 
	 rpc AddVoteRecord(AddVoteRecordReq) returns (AddVoteRecordResp); 
	 rpc UpdateVoteRecord(UpdateVoteRecordReq) returns (UpdateVoteRecordResp); 
	 rpc DelVoteRecord(DelVoteRecordReq) returns (DelVoteRecordResp); 
	 rpc GetVoteRecordById(GetVoteRecordByIdReq) returns (GetVoteRecordByIdResp); 
	 rpc GetVoteRecordDetail(GetVoteRecordDetailReq) returns (GetVoteRecordDetailResp);
	 rpc GetUserVoteRecordDetail(GetUserVoteRecordDetailReq) returns (GetUserVoteRecordDetailResp);
	 rpc SearchVoteRecord(SearchVoteRecordReq) returns (SearchVoteRecordResp);

}
