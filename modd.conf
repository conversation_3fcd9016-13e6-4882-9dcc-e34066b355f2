#usercenter
app/usercenter/cmd/rpc/**/*.go {
    prep: go build -o data/server/usercenter-rpc  -v app/usercenter/cmd/rpc/usercenter.go
    daemon +sigkill: ./data/server/usercenter-rpc -f app/usercenter/cmd/rpc/etc/usercenter.yaml
}
app/usercenter/cmd/api/**/*.go {
    prep: go build -o data/server/usercenter-api  -v app/usercenter/cmd/api/usercenter.go
    daemon +sigkill: ./data/server/usercenter-api -f app/usercenter/cmd/api/etc/usercenter.yaml
}
#shop
 app/shop/cmd/rpc/**/*.go {
     prep: go build -o data/server/shop-rpc  -v app/shop/cmd/rpc/shop.go
     daemon +sigkill: ./data/server/shop-rpc -f app/shop/cmd/rpc/etc/shop.yaml
 }
 app/shop/cmd/api/**/*.go {
     prep: go build -o data/server/shop-api  -v app/shop/cmd/api/shop.go
     daemon +sigkill: ./data/server/shop-api -f app/shop/cmd/api/etc/shop.yaml
 }
#checkin
app/checkin/cmd/rpc/**/*.go {
  prep: go build -o data/server/checkin-rpc  -v app/checkin/cmd/rpc/checkin.go
  daemon +sigkill: ./data/server/checkin-rpc -f app/checkin/cmd/rpc/etc/checkin.yaml
}
app/checkin/cmd/api/**/*.go {
  prep: go build -o data/server/checkin-api  -v app/checkin/cmd/api/checkin.go
  daemon +sigkill: ./data/server/checkin-api -f app/checkin/cmd/api/etc/checkin.yaml
}

#lottery
app/lottery/cmd/rpc/**/*.go {
 prep: go build -o data/server/lottery-rpc  -v app/lottery/cmd/rpc/lottery.go
 daemon +sigkill: ./data/server/lottery-rpc -f app/lottery/cmd/rpc/etc/lottery.yaml
}
app/lottery/cmd/api/**/*.go {
 prep: go build -o data/server/lottery-api  -v app/lottery/cmd/api/lottery.go
 daemon +sigkill: ./data/server/lottery-api -f app/lottery/cmd/api/etc/lottery.yaml
}

#comment
app/comment/cmd/rpc/**/*.go {
  prep: go build -o data/server/comment-rpc  -v app/comment/cmd/rpc/comment.go
  daemon +sigkill: ./data/server/comment-rpc -f app/comment/cmd/rpc/etc/comment.yaml
}
app/comment/cmd/api/**/*.go {
  prep: go build -o data/server/comment-api  -v app/comment/cmd/api/comment.go
  daemon +sigkill: ./data/server/comment-api -f app/comment/cmd/api/etc/comment.yaml
}

#vote
app/vote/cmd/rpc/**/*.go {
   prep: go build -o data/server/vote-rpc  -v app/vote/cmd/rpc/vote.go
   daemon +sigkill: ./data/server/vote-rpc -f app/vote/cmd/rpc/etc/vote.yaml
}
app/vote/cmd/api/**/*.go {
   prep: go build -o data/server/vote-api  -v app/vote/cmd/api/vote.go
   daemon +sigkill: ./data/server/vote-api -f app/vote/cmd/api/etc/vote.yaml
}

#notice
app/notice/cmd/api/**/*.go {
   prep: go build -o data/server/notice-api  -v app/notice/cmd/api/notice.go
   daemon +sigkill: ./data/server/notice-api -f app/notice/cmd/api/etc/notice.yaml
}
app/notice/cmd/rpc/**/*.go {
   prep: go build -o data/server/notice-rpc  -v app/notice/cmd/rpc/notice.go
   daemon +sigkill: ./data/server/notice-rpc -f app/notice/cmd/rpc/etc/notice.yaml
}

#upload
app/upload/cmd/rpc/**/*.go {
  prep: go build -o data/server/upload-rpc  -v app/upload/cmd/rpc/upload.go
  daemon +sigkill: ./data/server/upload-rpc -f app/upload/cmd/rpc/etc/upload.yaml
}
app/upload/cmd/api/**/*.go {
  prep: go build -o data/server/upload-api  -v app/upload/cmd/api/upload.go
  daemon +sigkill: ./data/server/upload-api -f app/upload/cmd/api/etc/upload.yaml
}

#travel
app/travel/cmd/rpc/**/*.go {
   prep: go build -o data/server/travel-rpc  -v app/travel/cmd/rpc/travel.go
   daemon +sigkill: ./data/server/travel-rpc -f app/travel/cmd/rpc/etc/travel.yaml
}
app/travel/cmd/api/**/*.go {
   prep: go build -o data/server/travel-api  -v app/travel/cmd/api/travel.go
   daemon +sigkill: ./data/server/travel-api -f app/travel/cmd/api/etc/travel.yaml
}

#payment
app/payment/cmd/rpc/**/*.go {
   prep: go build -o data/server/payment-rpc  -v app/payment/cmd/rpc/payment.go
   daemon +sigkill: ./data/server/payment-rpc -f app/payment/cmd/rpc/etc/payment.yaml
}
app/payment/cmd/api/**/*.go {
   prep: go build -o data/server/payment-api  -v app/payment/cmd/api/payment.go
   daemon +sigkill: ./data/server/payment-api -f app/payment/cmd/api/etc/payment.yaml
}
#order
app/order/cmd/rpc/**/*.go {
   prep: go build -o data/server/order-rpc  -v app/order/cmd/rpc/order.go
   daemon +sigkill: ./data/server/order-rpc -f app/order/cmd/rpc/etc/order.yaml
}
app/order/cmd/api/**/*.go {
   prep: go build -o data/server/order-api  -v app/order/cmd/api/order.go
   daemon +sigkill: ./data/server/order-api -f app/order/cmd/api/etc/order.yaml
}
app/order/cmd/mq/**/*.go {
   prep: go build -o data/server/order-mq  -v app/order/cmd/mq/order.go
   daemon +sigkill: ./data/server/order-mq -f app/order/cmd/mq/etc/order.yaml
}
#mqueue
app/mqueue/cmd/scheduler/**/*.go {
   prep: go build -o data/server/mqueue-scheduler  -v app/mqueue/cmd/scheduler/mqueue.go
   daemon +sigkill: ./data/server/mqueue-scheduler -f app/mqueue/cmd/scheduler/etc/mqueue.yaml
}
app/mqueue/cmd/job/**/*.go {
   prep: go build -o data/server/mqueue-job  -v app/mqueue/cmd/job/mqueue.go
   daemon +sigkill: ./data/server/mqueue-job -f app/mqueue/cmd/job/etc/mqueue.yaml
}