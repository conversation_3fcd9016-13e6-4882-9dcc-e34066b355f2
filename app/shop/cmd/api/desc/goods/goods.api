syntax = "v1"

info(
    title: "商城服务api文件"
    desc: "type desc here"
    author: "lovevivi121"
    email: "type email here"
    version: "type version here"
)

//商品信息结构体
//目前与数据库的字段一致
//type GoodsInfo {
//    Id int64 `json:"id"`
//    CategoryId int64 `json:"categoryId"`
//    GoodsId string `json:"goodsId"`
//    Precoupon_Price float64 `json:"precouponPrice"`
//    Aftercoupon_Price float64 `json:"aftercouponPrice"`
//    GoodsDesc string `json:"goodsDesc"`
//    WishPoint64s int64 `json:"wishPoint64s"`
//    CouponStartTime int64 `json:"couponStartTime"`
//    CouponEndTime int64 `json:"couponEndTime"`
//    CouponDiscount int64 `json:"couponDiscount"`
//    CouponRemainQuantity int64 `json:"couponRemainQuantity"`
//}
//商品信息的请求
//拿到用户id和商品id
//type GoodsInfoReq {
//    Id int64 `json:"id"` //商品id
//}
//商品信息的响应
//type GoodsInfoResp {
//    Id int64 `json:"id"`
//    CategoryId int64 `json:"categoryId"`
//    GoodsId string `json:"goodsId"`
//    PrecouponPrice float64 `json:"precouponPrice"`
//    AftercouponPrice float64 `json:"aftercouponPrice"`
//    GoodsDesc string `json:"goodsDesc"`
//    WishPoint64s int64 `json:"wishPoint64s"`
//    CouponStartTime int64 `json:"couponStartTime"`
//    CouponEndTime int64 `json:"couponEndTime"`
//    CouponDiscount int64 `json:"couponDiscount"`
//    CouponRemainQuantity int64 `json:"couponRemainQuantity"`
//}

type GoodsInfo {
	CategoryName         string   `json:"category_name"`
	CouponRemainQuantity int64      `json:"coupon_remain_quantity"`
	ExtraCouponAmount    int64      `json:"extra_coupon_amount"`
	PromotionRate        int64      `json:"promotion_rate"`
	SubsidyGoodsType     int64      `json:"subsidy_goods_type"`
	CatIds               []int64    `json:"cat_ids"`
	CouponMinOrderAmount int64      `json:"coupon_min_order_amount"`
	CategoryId           string   `json:"category_id"`
	MallId               int64    `json:"mall_id"`
	SubsidyAmount        int64      `json:"subsidy_amount"`
	CouponPrice          int64      `json:"coupon_price"`
	MallName             string   `json:"mall_name"`
	CouponTotalQuantity  int64      `json:"coupon_total_quantity"`
	MarketFee            int64      `json:"market_fee"`
	MerchantType         string   `json:"merchant_type"`
	LgstTxt              string   `json:"lgst_txt"`
	GoodsName            string   `json:"goods_name"`
	SalesTip             string   `json:"sales_tip"`
	HasMaterial          bool     `json:"has_material"`
	GoodsId              int64    `json:"goods_id"`
	PredictPromotionRate int64      `json:"predict_promotion_rate"`
	DescTxt              string   `json:"desc_txt"`
	GoodsDesc            string   `json:"goods_desc"`
	OptName              string   `json:"opt_name"`
	RealtimeSalesTip     string   `json:"realtime_sales_tip"`
	ShareRate            int64      `json:"share_rate"`
	GoodsThumbnailUrl    string   `json:"goods_thumbnail_url"`
	OptIds               []int64    `json:"opt_ids"`
	OptId                string   `json:"opt_id"`
	SearchId             string   `json:"search_id"`
	GoodsImageUrl        string   `json:"goods_image_url"`
	ActivityTags         []int64    `json:"activity_tags"`
	HasCoupon            bool     `json:"has_coupon"`
	MinNormalPrice       int64      `json:"min_normal_price"`
	ServTxt              string   `json:"serv_txt"`
	UnifiedTags          []string `json:"unified_tags"`
	CouponStartTime      int64    `json:"coupon_start_time"`
	MinGroupPrice        int64      `json:"min_group_price"`
	CouponDiscount       int64      `json:"coupon_discount"`
	GoodsSign            string   `json:"goods_sign"`
	CouponEndTime        int64    `json:"coupon_end_time"`
}

type GoodsDetail {
	CategoryName            string   `json:"category_name"`
	CouponRemainQuantity    int64      `json:"coupon_remain_quantity"`
	PromotionRate           int64      `json:"promotion_rate"`
	SubsidyGoodsType        int64      `json:"subsidy_goods_type"`
	ServiceTags             []int64    `json:"service_tags"`
	MallId                  int64    `json:"mall_id"`
	MallName                string   `json:"mall_name"`
	MallCouponEndTime       int64    `json:"mall_coupon_end_time"`
	LgstTxt                 string   `json:"lgst_txt"`
	GoodsName               string   `json:"goods_name"`
	GoodsGalleryUrls        []string `json:"goods_gallery_urls"`
	GoodsId                 int64    `json:"goods_id"`
	BrandName               string   `json:"brand_name"`
	PredictPromotionRate    int64      `json:"predict_promotion_rate"`
	GoodsDesc               string   `json:"goods_desc"`
	OptName                 string   `json:"opt_name"`
	ShareRate               int64      `json:"share_rate"`
	OptIds                  []int64    `json:"opt_ids"`
	GoodsImageUrl           string   `json:"goods_image_url"`
	MallImgUrl              string   `json:"mall_img_url"`
	HasMallCoupon           bool     `json:"has_mall_coupon"`
	UnifiedTags             []string `json:"unified_tags"`
	VideoUrls               []string `json:"video_urls"`
	MinGroupPrice           int64      `json:"min_group_price"`
	CouponDiscount          int64      `json:"coupon_discount"`
	CouponEndTime           int64    `json:"coupon_end_time"`
	ZsDuoId                 int64    `json:"zs_duo_id"`
	MallCouponRemainQuantity int64     `json:"mall_coupon_remain_quantity"`
	PlanType                int64      `json:"plan_type"`
	ExtraCouponAmount       int64      `json:"extra_coupon_amount"`
	MaterialList            []string `json:"material_list"`
	CatIds                  []int64    `json:"cat_ids"`
	CouponMinOrderAmount    int64      `json:"coupon_min_order_amount"`
	CategoryId              int64      `json:"category_id"`
	MallCouponDiscountPct   int64      `json:"mall_coupon_discount_pct"`
	CatId                   int64      `json:"cat_id"`
	CouponTotalQuantity     int64      `json:"coupon_total_quantity"`
	MallCouponMinOrderAmount int64     `json:"mall_coupon_min_order_amount"`
	MerchantType            int64      `json:"merchant_type"`
	SalesTip                string   `json:"sales_tip"`
	IsMultiGroup            bool     `json:"is_multi_group"`
	OnlySceneAuth           bool     `json:"only_scene_auth"`
	DescTxt                 string   `json:"desc_txt"`
	GoodsThumbnailUrl       string   `json:"goods_thumbnail_url"`
	OptId                   int64      `json:"opt_id"`
	ActivityTags            []int64    `json:"activity_tags"`
	HasCoupon               bool     `json:"has_coupon"`
	MinNormalPrice          int64      `json:"min_normal_price"`
	MallCouponStartTime     int64    `json:"mall_coupon_start_time"`
	ServTxt                 string   `json:"serv_txt"`
	MallCouponTotalQuantity int64      `json:"mall_coupon_total_quantity"`
	MallCouponMaxDiscountAmount int64  `json:"mall_coupon_max_discount_amount"`
	MallCps                 int64      `json:"mall_cps"`
	GoodsSign               string   `json:"goods_sign"`
}

// 心愿兑换记录
type WishRedemptionRecords {
    Id int64 `json:"id"`
    UserId int64 `json:"userId"`
    GoodUrl string `json:"goodUrl"`
    Integral int64 `json:"integral"`
    Extra string `json:"extra"`
}

// 订单表
type Order {
    Id int64 `json:"id"`
    UserId int64 `json:"userId"`
    GoodsSign string `json:"goodsSign"`
    CreateTime int64 `json:"createTime"`
    Extra string `json:"extra"`
}

//商品列表的请求 传入页号
type GoodsListReq {
    LastId int64 `json:"lastId"`
    Size int64 `json:"size"`
}
//商品列表的响应 返回商品列表
type GoodsListResp {
    Total int64 `json:"total"`
    List []GoodsInfo `json:"list"`
}

//商品详情的请求
type GoodsDetailReq {
    GoodsSign string `json:"goodsSign"`
}

//商品详情的响应
type GoodsDetailResp {
    GoodsDetail GoodsDetail `json:"goodsDetail"`
}

// 商品搜索请求
type GoodsSearchReq {
    Keyword string `json:"keyword"`
    Page int64 `json:"page"`
    Size int64 `json:"size"`
}

// 商品搜索响应
type GoodsSearchResp {
    Body string `json:"body"`
}

// 心愿兑换请求
type WishExchangeReq {
    GoodsSign string `json:"goodsSign"`
    Integral int64 `json:"integral"`
    GoodsUrl string `json:"goodsUrl"`
    GoodsName string `json:"goodsName"`
}

// 心愿兑换响应
type WishExchangeResp {
    Success bool `json:"success"`
}

// 心愿兑换列表请求
type WishExchangeListReq {
}

// 心愿兑换列表响应
type WishExchangeListResp {
    Total int64 `json:"total"`
    List []WishRedemptionRecords `json:"list"`
}

// 订单列表查询请求
type OrderListReq {
}

// 订单列表查询响应
type OrderListResp {
    Total int64 `json:"total"`
    List []Order `json:"list"`
}

// 查询备案情况并返回pdd备案url
type PddAuthReq {
}

type PddAuthResp {
    Bind int64 `json:"bind"`
    MobileURL string `json:"mobile_url"`
}

// 拼多多推广链接请求
type GenPddPromotionUrlReq {
    GoodsSign string `json:"goodsSign"`
}

// TODO: 开启一个后台定时任务，更新订单表



