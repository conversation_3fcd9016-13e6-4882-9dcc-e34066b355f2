{"swagger": "2.0", "info": {"title": "抽奖服务", "description": "抽奖服务", "version": "v1"}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/lottery/v1/lottery/CheckIsParticipated": {"post": {"summary": "CheckIsParticipated", "operationId": "CheckIsParticipated", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/CheckIsParticipatedResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CheckIsParticipatedReq"}}], "tags": ["lottery"], "security": [{"apiKey": []}]}}, "/lottery/v1/lottery/TestValidator": {"post": {"summary": "测试Validator", "operationId": "TestValidator", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/TestResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/TestReq"}}], "tags": ["lottery"]}}, "/lottery/v1/lottery/chanceTypeList": {"get": {"summary": "增加概率类型", "operationId": "chanceTypeList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ChanceTypeListResp"}}}, "tags": ["lottery"]}}, "/lottery/v1/lottery/checkIsWin": {"post": {"summary": "判断当前用户当前抽奖是否中奖", "operationId": "checkIsWin", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/CheckIsWinResp"}}}, "parameters": [{"name": "body", "description": " 获取当前用户当前抽奖是否中奖", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CheckIsWinReq"}}], "tags": ["lottery"], "security": [{"apiKey": []}]}}, "/lottery/v1/lottery/clockTaskTypeList": {"get": {"summary": "打卡任务类型", "operationId": "clockTaskTypeList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ClockTaskTypeListResp"}}}, "tags": ["lottery"]}}, "/lottery/v1/lottery/createClockTaskRecord": {"post": {"summary": "完成打卡任务", "operationId": "createClockTaskRecord", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/CreateClockTaskRecordResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CreateClockTaskRecordReq"}}], "tags": ["lottery"], "security": [{"apiKey": []}]}}, "/lottery/v1/lottery/createLottery": {"post": {"summary": "发起抽奖", "operationId": "createLottery", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/CreateLotteryResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CreateLotteryReq"}}], "tags": ["lottery"], "security": [{"apiKey": []}]}}, "/lottery/v1/lottery/getLotteryListByUserId": {"post": {"summary": "获取当前用户全部/发起/中奖的抽奖列表", "operationId": "getLotteryListByUserId", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GetLotteryListByUserIdResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/GetLotteryListByUserIdReq"}}], "tags": ["lottery"], "security": [{"apiKey": []}]}}, "/lottery/v1/lottery/getLotteryWinList": {"post": {"summary": "获取当前用户中奖列表", "operationId": "getLotteryWinList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GetLotteryWinListResp"}}}, "parameters": [{"name": "body", "description": " 获取当前用户中奖列表", "in": "body", "required": true, "schema": {"$ref": "#/definitions/GetLotteryWinListReq"}}], "tags": ["lottery"], "security": [{"apiKey": []}]}}, "/lottery/v1/lottery/getLotteryWinnersList": {"post": {"summary": "获取当前抽奖中奖者名单", "operationId": "getLotteryWinList2", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GetLotteryWinList2Resp"}}}, "parameters": [{"name": "body", "description": " 得到当前抽奖中奖者名单列表", "in": "body", "required": true, "schema": {"$ref": "#/definitions/GetLotteryWinList2Req"}}], "tags": ["lottery"]}}, "/lottery/v1/lottery/lotteryDetail": {"post": {"summary": "lottery detail", "operationId": "lotteryDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/LotteryDetailResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/LotteryDetailReq"}}], "tags": ["lottery"], "security": [{"apiKey": []}]}}, "/lottery/v1/lottery/lotteryList": {"post": {"summary": "lottery list", "operationId": "lotteryList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/LotteryListResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/LotteryListReq"}}], "tags": ["lottery"]}}, "/lottery/v1/lottery/lotteryListAfterLogin": {"post": {"summary": "登录后获取抽奖列表", "operationId": "lotteryListAfterLogin", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/LotteryListResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/LotteryListReq"}}], "tags": ["lottery"], "security": [{"apiKey": []}]}}, "/lottery/v1/lottery/participation": {"post": {"summary": "参与抽奖", "operationId": "addLotteryParticipation", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/AddLotteryParticipationResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/AddLotteryParticipationReq"}}], "tags": ["lottery"], "security": [{"apiKey": []}]}}, "/lottery/v1/lottery/participations": {"post": {"summary": "抽奖人", "operationId": "searchParticipation", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/SearchLotteryParticipationResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SearchLotteryParticipationReq"}}], "tags": ["lottery"]}}, "/lottery/v1/lottery/setLotteryIsSelected": {"post": {"summary": "lottery setIsSelected", "operationId": "setLotteryIsSelected", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/SetLotteryIsSelectedResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SetLotteryIsSelectedReq"}}], "tags": ["lottery"], "security": [{"apiKey": []}]}}, "/lottery/v1/lottery/updateLottery": {"post": {"summary": "发布抽奖", "operationId": "updateLottery", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/UpdateLotteryResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UpdateLotteryReq"}}], "tags": ["lottery"], "security": [{"apiKey": []}]}}}, "definitions": {"AddLotteryParticipationReq": {"type": "object", "properties": {"lotteryId": {"type": "integer", "format": "int64"}}, "title": "AddLotteryParticipationReq", "required": ["lotteryId"]}, "AddLotteryParticipationResp": {"type": "object", "title": "AddLotteryParticipationResp"}, "ChanceType": {"type": "object", "properties": {"type": {"type": "integer", "format": "int64"}, "text": {"type": "string"}}, "title": "ChanceType", "required": ["type", "text"]}, "ChanceTypeListReq": {"type": "object", "title": "ChanceTypeListReq"}, "ChanceTypeListResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/ChanceType"}}}, "title": "ChanceTypeListResp", "required": ["list"]}, "CheckIsParticipatedReq": {"type": "object", "properties": {"lotteryId": {"type": "integer", "format": "int64", "description": " 当前抽奖Id"}}, "title": "CheckIsParticipatedReq", "required": ["lotteryId"]}, "CheckIsParticipatedResp": {"type": "object", "properties": {"isParticipated": {"type": "integer", "format": "int64", "description": " 当前用户是否参与了当前抽奖： 0未参与 1已参与"}}, "title": "CheckIsParticipatedResp", "required": ["isParticipated"]}, "CheckIsWinReq": {"type": "object", "properties": {"lotteryId": {"type": "integer", "format": "int64"}}, "title": "CheckIsWinReq", "required": ["lotteryId"]}, "CheckIsWinResp": {"type": "object", "properties": {"isWon": {"type": "integer", "format": "int64"}}, "title": "CheckIsWinResp", "required": ["isWon"]}, "ClockTaskTypeListReq": {"type": "object", "title": "ClockTaskTypeListReq"}, "ClockTaskTypeListResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/CockTaskType"}}}, "title": "ClockTaskTypeListResp", "required": ["list"]}, "CockTaskType": {"type": "object", "properties": {"type": {"type": "integer", "format": "int64"}, "text": {"type": "string"}, "seconds": {"type": "integer", "format": "int64"}}, "title": "CockTaskType", "required": ["type", "text", "seconds"]}, "CreateClockTask": {"type": "object", "properties": {"type": {"type": "integer", "format": "int64", "description": " 任务类型 1: 体验小程序 2： 浏览指定公众号文章 3: 浏览图片（微信图片二维码等） 4： 浏览视频号视频"}, "seconds": {"type": "integer", "format": "int64", "description": " 任务秒数"}, "appletType": {"type": "integer", "format": "int64", "description": " type=1时该字段才有意义 小程序跳转类型，1小程序链接 2小程序路径"}, "pageLink": {"type": "string", "description": " type=1 并且 applet_type=1时 该字段才有意义 配置要跳转小程序的页面链接 （如 #小程序:抽奖/oM....）"}, "appId": {"type": "string", "description": " type=1 并且 applet_type=2时 该字段才有意义 配置要跳转的小程序AppID"}, "pagePath": {"type": "string", "description": " type=1 并且 applet_type=2时 该字段才有意义 配置要跳转的小程序路径（如：/pages/index）"}, "image": {"type": "string", "description": " type=3时 该字段才有意义 添加要查看的图片"}, "videoAccountId": {"type": "string", "description": " type=4时 该字段才有意义 视频号ID"}, "videoId": {"type": "string", "description": " type=4时 该字段才有意义 视频ID"}, "articleLink": {"type": "string", "description": " type=2时 该字段才有意义 公众号文章链接"}, "copywriting": {"type": "string", "description": " 引导参与者完成打卡任务的文案"}, "chanceType": {"type": "integer", "format": "int64", "description": " 概率类型 1: 随机 2: 指定"}, "increaseMultiple": {"type": "integer", "format": "int64", "description": " ChanceType=2时 该字段才有意义，概率增加倍数"}}, "title": "CreateClockTask", "required": ["type", "seconds", "appletType", "pageLink", "appId", "pagePath", "image", "videoAccountId", "videoId", "articleLink", "increaseMultiple"]}, "CreateClockTaskRecordReq": {"type": "object", "properties": {"lotteryId": {"type": "integer", "format": "int64"}, "clockTaskId": {"type": "integer", "format": "int64"}}, "title": "CreateClockTaskRecordReq", "required": ["lotteryId", "clockTaskId"]}, "CreateClockTaskRecordResp": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "title": "CreateClockTaskRecordResp", "required": ["id"]}, "CreateLotteryReq": {"type": "object", "properties": {"name": {"type": "string", "description": "默认一等奖名称"}, "thumb": {"type": "string", "description": "默认一等奖配图"}, "announceType": {"type": "integer", "format": "int64", "description": "开奖设置：1按时间开奖 2按人数开奖 3即抽即中"}, "announceTime": {"type": "integer", "format": "int64", "description": "开奖时间"}, "joinNumber": {"type": "integer", "format": "int64", "description": "自动开奖人数标准"}, "introduce": {"type": "string", "description": "抽奖说明"}, "awardDeadline": {"type": "integer", "format": "int64", "description": "领奖截止时间"}, "sponsorId": {"type": "integer", "format": "int64", "description": " 赞助商Id"}, "prizes": {"type": "array", "items": {"$ref": "#/definitions/CreatePrize"}, "description": "奖品 支持多个"}, "isClocked": {"type": "integer", "format": "int64", "description": "是否开启打卡任务 0未开启；1已开启"}, "clockTask": {"$ref": "#/definitions/CreateClockTask", "description": "打卡任务 支持一个"}, "publishType": {"type": "integer", "format": "int64", "description": "发布类型 1发布抽奖 2发布测试"}}, "title": "CreateLotteryReq", "required": ["name", "thumb", "announceType", "announceTime", "joinNumber", "introduce", "awardDeadline", "sponsorId", "prizes", "isClocked", "clockTask", "publishType"]}, "CreateLotteryResp": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "title": "CreateLotteryResp", "required": ["id"]}, "CreatePrize": {"type": "object", "properties": {"type": {"type": "integer", "format": "int64", "description": "奖品类型：1奖品 2优惠券 3兑换码 4商城 5微信红包封面 6红包"}, "name": {"type": "string", "description": "奖品名称"}, "count": {"type": "integer", "format": "int64", "description": "奖品份数"}, "thumb": {"type": "string", "description": "默认一等奖配图"}, "level": {"type": "integer", "format": "int64", "description": "奖品等级 1一等奖 2二等奖 3三等奖，依次类推"}, "grantType": {"type": "integer", "format": "int64", "description": "奖品发放方式：1快递邮寄 2让中奖者联系我 3中奖者填写信息 4跳转到其他小程序"}}, "title": "CreatePrize", "required": ["type", "name", "count", "thumb", "level", "grantType"]}, "GetLotteryListByUserIdReq": {"type": "object", "properties": {"type": {"type": "integer", "format": "int64", "description": " 1:全部（发起+参与） 2:发起 3：中奖"}, "size": {"type": "integer", "format": "int64", "description": " 每页数量"}, "lastId": {"type": "integer", "format": "int64", "description": " 最后一条数据的id"}, "isAnnounced": {"type": "integer", "format": "int64", "description": " 是否已开奖 0:未开奖 1:已开奖"}}, "title": "GetLotteryListByUserIdReq", "required": ["type", "size", "lastId", "isAnnounced"]}, "GetLotteryListByUserIdResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/LotteryPrizes"}}}, "title": "GetLotteryListByUserIdResp", "required": ["list"]}, "GetLotteryWinList2Req": {"type": "object", "properties": {"lotteryId": {"type": "integer", "format": "int64"}}, "title": "GetLotteryWinList2Req", "required": ["lotteryId"]}, "GetLotteryWinList2Resp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/WonList2"}}}, "title": "GetLotteryWinList2Resp", "required": ["list"]}, "GetLotteryWinListReq": {"type": "object", "properties": {"lastId": {"type": "integer", "format": "int64"}, "size": {"type": "integer", "format": "int64"}}, "title": "GetLotteryWinListReq", "required": ["lastId", "size"]}, "GetLotteryWinListResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/WonList"}}}, "title": "GetLotteryWinListResp", "required": ["list"]}, "Lottery": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64", "description": "发起抽奖用户ID"}, "name": {"type": "string", "description": "默认一等奖名称"}, "thumb": {"type": "string", "description": "默认一等奖配图"}, "publishTime": {"type": "integer", "format": "int64", "description": "发布抽奖时间"}, "joinNumber": {"type": "integer", "format": "int64", "description": "自动开奖人数标准"}, "introduce": {"type": "string", "description": "抽奖说明"}, "awardDeadline": {"type": "integer", "format": "int64", "description": "领奖截止时间"}, "isSelected": {"type": "integer", "format": "int64", "description": "是否精选 1是 0否"}, "announceType": {"type": "integer", "format": "int64", "description": "开奖设置：1按时间开奖 2按人数开奖 3即抽即中"}, "announceTime": {"type": "integer", "format": "int64", "description": "开奖时间"}, "isAnnounced": {"type": "integer", "format": "int64", "description": " 是否已经开奖：0未开奖 1已开奖"}, "sponsorId": {"type": "integer", "format": "int64", "description": " 赞助商Id"}}, "title": "Lottery", "required": ["id", "userId", "name", "thumb", "publishTime", "joinNumber", "introduce", "awardDeadline", "isSelected", "announceType", "announceTime", "isAnnounced", "sponsorId"]}, "LotteryDetailReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "title": "LotteryDetailReq", "required": ["id"]}, "LotteryDetailResp": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64", "description": "发起抽奖用户ID"}, "name": {"type": "string", "description": "默认一等奖名称"}, "thumb": {"type": "string", "description": "默认一等奖配图"}, "publishTime": {"type": "integer", "format": "int64", "description": "发布抽奖时间"}, "joinNumber": {"type": "integer", "format": "int64", "description": "自动开奖人数标准"}, "introduce": {"type": "string", "description": "抽奖说明"}, "awardDeadline": {"type": "integer", "format": "int64", "description": "领奖截止时间"}, "isSelected": {"type": "integer", "format": "int64", "description": "是否精选 1是 0否"}, "announceType": {"type": "integer", "format": "int64", "description": "开奖设置：1按时间开奖 2按人数开奖 3即抽即中"}, "announceTime": {"type": "integer", "format": "int64", "description": "开奖时间"}, "isAnnounced": {"type": "integer", "format": "int64", "description": " 是否已经开奖：0未开奖 1已开奖"}, "sponsorId": {"type": "integer", "format": "int64", "description": " 赞助商Id"}, "prizes": {"type": "array", "items": {"$ref": "#/definitions/CreatePrize"}, "description": "奖品信息"}, "sponsor": {"$ref": "#/definitions/LotterySponsor", "description": " 抽奖赞助商信息"}, "isParticipated": {"type": "integer", "format": "int64", "description": " 当前用户是否参与了当前抽奖： 0未参与 1已参与"}}, "title": "LotteryDetailResp", "required": ["id", "userId", "name", "thumb", "publishTime", "joinNumber", "introduce", "awardDeadline", "isSelected", "announceType", "announceTime", "isAnnounced", "sponsorId", "prizes", "sponsor", "isParticipated"]}, "LotteryListReq": {"type": "object", "properties": {"lastId": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int64"}, "isSelected": {"type": "integer", "format": "int64"}}, "title": "LotteryListReq", "required": ["lastId", "pageSize", "isSelected"]}, "LotteryListResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/Lottery"}}}, "title": "LotteryListResp", "required": ["list"]}, "LotteryParticipation": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": " 主键"}, "lottery_id": {"type": "integer", "format": "int64", "description": " 参与的抽奖的id"}, "user_id": {"type": "integer", "format": "int64", "description": " 用户id"}, "is_won": {"type": "integer", "format": "int64", "description": " 中奖了吗？"}, "prize_id": {"type": "integer", "format": "int64", "description": " 中奖id"}}, "title": "LotteryParticipation", "required": ["id", "lottery_id", "user_id", "is_won", "prize_id"]}, "LotteryPrizes": {"type": "object", "properties": {"lotteryId": {"type": "integer", "format": "int64", "description": "抽奖ID"}, "prizes": {"type": "array", "items": {"$ref": "#/definitions/Prize"}, "description": "奖品信息"}, "participationId": {"type": "integer", "format": "int64", "description": "参与ID"}, "time": {"type": "integer", "format": "int64", "description": "参与/发起/中奖时间"}}, "title": "LotteryPrizes", "required": ["lotteryId", "prizes", "participationId", "time"]}, "LotterySponsor": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "id"}, "userId": {"type": "integer", "format": "int64", "description": "userId"}, "type": {"type": "integer", "format": "int64", "description": "1微信号 2公众号 3小程序 4微信群 5视频号"}, "appletType": {"type": "integer", "format": "int64", "description": "type=3时该字段才有意义，1小程序链接 2路径跳转 3二维码跳转"}, "name": {"type": "string", "description": "名称"}, "desc": {"type": "string", "description": "描述"}, "avatar": {"type": "string", "description": "avatar"}, "isShow": {"type": "integer", "format": "int64", "description": "1显示 2不显示"}, "qrCode": {"type": "string", "description": "二维码图片地址, type=1 2 3&applet_type=3 4的时候启用"}, "inputA": {"type": "string", "description": "type=5 applet_type=2 or applet_type=1 输入框A"}, "inputB": {"type": "string", "description": "type=5 applet_type=2输入框B"}}, "title": "LotterySponsor", "required": ["id", "userId", "type", "appletType", "name", "desc", "avatar", "isShow"]}, "Prize": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "lotteryId": {"type": "integer", "format": "int64", "description": "抽奖ID"}, "type": {"type": "integer", "format": "int64", "description": "奖品类型：1奖品 2优惠券 3兑换码 4商城 5微信红包封面 6红包"}, "name": {"type": "string", "description": "奖品名称"}, "count": {"type": "integer", "format": "int64", "description": "奖品份数"}, "thumb": {"type": "string", "description": "默认一等奖配图"}, "level": {"type": "integer", "format": "int64", "description": "奖品等级 1一等奖 2二等奖 3三等奖，依次类推"}, "grantType": {"type": "integer", "format": "int64", "description": "奖品发放方式：1快递邮寄 2让中奖者联系我 3中奖者填写信息 4跳转到其他小程序"}}, "title": "Prize", "required": ["id", "lotteryId"]}, "Prizes": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "lottery_id": {"type": "integer", "format": "int64", "description": " 抽奖ID"}, "type": {"type": "integer", "format": "int64", "description": " 奖品类型：1奖品 2优惠券 3兑换码 4商城 5微信红包封面 6红包"}, "name": {"type": "string", "description": " 奖品名称"}, "level": {"type": "integer", "format": "int64", "description": " 几等奖 默认1"}, "thumb": {"type": "string", "description": " 奖品图"}, "count": {"type": "integer", "format": "int64", "description": " 奖品份数"}, "grant_type": {"type": "integer", "format": "int64", "description": " 奖品发放方式：1快递邮寄 2让中奖者联系我 3中奖者填写信息 4跳转到其他小程序"}}, "title": "Prizes", "required": ["id", "lottery_id", "type", "name", "level", "thumb", "count", "grant_type"]}, "SearchLotteryParticipationReq": {"type": "object", "properties": {"lotteryId": {"type": "integer", "format": "int64"}, "pageIndex": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int64"}}, "title": "SearchLotteryParticipationReq", "required": ["lotteryId", "pageIndex", "pageSize"]}, "SearchLotteryParticipationResp": {"type": "object", "properties": {"count": {"type": "integer", "format": "int64"}, "list": {"type": "array", "items": {"$ref": "#/definitions/UserInfo"}}}, "title": "SearchLotteryParticipationResp", "required": ["count", "list"]}, "SetLotteryIsSelectedReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "title": "SetLotteryIsSelectedReq", "required": ["id"]}, "SetLotteryIsSelectedResp": {"type": "object", "properties": {"isSelected": {"type": "integer", "format": "int64"}}, "title": "SetLotteryIsSelectedResp", "required": ["isSelected"]}, "TestReq": {"type": "object", "properties": {"age": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "email": {"type": "string"}, "password": {"type": "string"}, "re_password": {"type": "string"}, "date": {"type": "string"}}, "title": "TestReq", "required": ["age", "name", "email", "password", "re_password", "date"]}, "TestResp": {"type": "object", "title": "TestResp"}, "UpdateLotteryReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "title": "UpdateLotteryReq", "required": ["id"]}, "UpdateLotteryResp": {"type": "object", "title": "UpdateLotteryResp"}, "UserInfo": {"type": "object", "properties": {"mobile": {"type": "string"}, "nickname": {"type": "string"}, "sex": {"type": "integer", "format": "int64"}, "avatar": {"type": "string"}, "info": {"type": "string"}, "signature": {"type": "string"}, "locationName": {"type": "string"}}, "title": "UserInfo", "required": ["mobile", "nickname", "sex", "avatar", "info", "signature", "locationName"]}, "WonList": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": " 主键"}, "lottery_id": {"type": "integer", "format": "int64", "description": " 参与的抽奖的id"}, "user_id": {"type": "integer", "format": "int64", "description": " 用户id"}, "is_won": {"type": "integer", "format": "int64", "description": " 中奖了吗？"}, "prize": {"$ref": "#/definitions/Prizes", "description": " 中奖奖品"}}, "title": "WonList", "required": ["id", "lottery_id", "user_id", "is_won", "prize"]}, "WonList2": {"type": "object", "properties": {"prize": {"$ref": "#/definitions/Prizes"}, "winnerCount": {"type": "integer", "format": "int64"}, "users": {"type": "array", "items": {"$ref": "#/definitions/UserInfo"}}}, "title": "WonList2", "required": ["prize", "winnerCount", "users"]}}, "securityDefinitions": {"apiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Enter JWT Bearer token **_only_**", "name": "Authorization", "in": "header"}}}