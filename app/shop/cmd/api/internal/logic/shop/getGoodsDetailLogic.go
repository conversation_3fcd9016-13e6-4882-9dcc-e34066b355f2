package shop

import (
	"context"
	"errors"
	"fmt"
	"github.com/valyala/fastjson"
	"looklook/common/tool"

	"looklook/app/shop/cmd/api/internal/svc"
	"looklook/app/shop/cmd/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetGoodsDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetGoodsDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetGoodsDetailLogic {
	return &GetGoodsDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetGoodsDetailLogic) GetGoodsDetail(req *types.GoodsDetailReq) (resp string, err error) {
	// todo: add your logic here and delete this line

	if req.GoodsSign == "" {
		return "", errors.New("商品标识不能为空")
	}

	goodsDetail := ""

	// 根据goodsSign 以及pdd接口获取商品信息
	params := map[string]string{
		"type":            "pdd.ddk.goods.search",
		"client_secret":   tool.ClientSecret,
		"goods_sign_list": fmt.Sprintf("[\"%s\"]", req.GoodsSign)}
	body, err := tool.SendRequestToDDJB(params, "POST")
	if err != nil {
		return "", err
	}

	// 打印响应体
	logx.Errorf("Testt-7 Response body: %s", string(body))
	// Parse JSON response
	var p fastjson.Parser
	v, err := p.ParseBytes(body)
	if err != nil {
		return "", err
	}

	// Extract goods_list
	goodsList := v.GetArray("goods_search_response", "goods_list")
	if len(goodsList) == 0 {
		return "", errors.New("no goods found")
	}

	// Get the first item in goods_list
	firstGoods := goodsList[0].String()

	logx.Errorf("Testt-8 firstGoods: %s", firstGoods)

	if firstGoods != "" {
		goodsDetail = firstGoods
	}

	return goodsDetail, nil
}
