# 改正错误
## 修改了app/lottery/model/lotteryModel_gen.go 文件
之前操作失误，导致引用了`genModel.ErrNotFound`

## app/lottery/cmd/api/internal/types/types.go 文件中的字段定义错误
1. `UpdateLotteryReq` 和 `CreateLotteryResp` 中 `Id` 的类型为`int`,修改成`int64`

**修改方法：** 在`lottery.api`文件中修改，然后使用goctl自动生成

2. 不应该设置`PublishTime`字段，前端不需要传递这个字段，直接设置为当前时间即可

## app/lottery/cmd/rpc/internal/logic/updateLotteryLogic.go 文件中不应该使用事务
### 事务是什么？
事务是一组数据库操作的执行单元，它要么完全执行，要么完全回滚。事务可以确保数据库操作的一致性和可靠性。
### 什么时候使用事务？
1. 当需要执行一系列数据库操作时，要么全部成功，要么全部回滚，以确保数据的一致性。
2. 当多个并发操作需要访问和修改相同的数据时，使用事务可以确保数据的隔离性，避免并发操作导致的数据冲突和不一致。
3. 当需要执行复杂的业务逻辑，涉及多个数据库表的操作时，使用事务可以确保操作的原子性，避免部分操作成功而导致数据不一致。
