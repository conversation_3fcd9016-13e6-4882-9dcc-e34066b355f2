syntax = "v1"

info(
    title: "回调消息"
    desc: "回调消息"
    author: "<PERSON>"
    email: "<EMAIL>"
)

type (
    VerifyEventReq {
        Signature string `form:"signature"`
        Timestamp string `form:"timestamp"`
        Nonce string `form:"nonce"`
        Echostr string `form:"echostr"`
    }
    VerifyEventResp {
    }
)

type (
    ReceiveEventReq {
        Signature string `form:"signature"`
        Timestamp string `form:"timestamp"`
        Nonce string `form:"nonce"`
    }
    ReceiveEventResp {
    }
)