Name: shop-api
Host: 0.0.0.0
Port: 1014
Mode: dev

#jwtAuth
JwtAuth:
  AccessSecret: ae0536f9-6450-4606-8e13-5a19ed505da0
  AccessExpire: 31536000

Log:
  ServiceName: shop-api
  Mode: console
  # Mode: file
  # 因为项目在docker中启动，所以logs是打印在docker中
  Path: logs
  # 打印堆栈信息 方便查询错误
  Encoding: plain
  Level: info
  Compress: true
  KeyDays: 7

#监控
Prometheus:
  Host: 0.0.0.0
  Port: 4019
  Path: /metrics

#链路追踪
Telemetry:
  Name: shop-api
  Endpoint: http://jaeger:14268/api/traces
  Sampler: 1.0
  Batcher: jaeger

DB:
  DataSource: root:PXDN93VRKUm8TeE7@tcp(mysql:3306)/lottery?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai
Cache:
  - Host: redis:6379
    Pass: G62m50oigInC30sf

#rpc
UsercenterRpcConf:
  Endpoints:
    - ********:2004
  NonBlock: true

ShopRpcConf:
  Endpoints:
    - 127.0.0.1:2014
  NonBlock: true