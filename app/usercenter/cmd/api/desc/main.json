{"swagger": "2.0", "info": {"title": "用户中心服务", "description": "用户中心服务", "version": "v1"}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/usercenter/v1/user/addAddress": {"post": {"summary": "添加收货地址", "operationId": "addAddress", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/AddAddressResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/AddAddressReq"}}], "tags": ["address"], "security": [{"apiKey": []}]}}, "/usercenter/v1/user/addressList": {"post": {"summary": "收货地址列表", "operationId": "addressList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/AddressListResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/AddressListReq"}}], "tags": ["address"], "security": [{"apiKey": []}]}}, "/usercenter/v1/user/convertAddress": {"post": {"summary": "识别并转换收货地址", "operationId": "convertAddress", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ConvertAddressResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ConvertAddressReq"}}], "tags": ["address"], "security": [{"apiKey": []}]}}, "/usercenter/v1/user/createDynamic": {"post": {"summary": "创建用户动态", "operationId": "createDynamic", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/CreateDynamicResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CreateDynamicReq"}}], "tags": ["userDynamic"], "security": [{"apiKey": []}]}}, "/usercenter/v1/user/deleteDynamic": {"post": {"summary": "删除用户动态", "operationId": "deleteDynamic", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/DeleteDynamicResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/DeleteDynamicReq"}}], "tags": ["userDynamic"], "security": [{"apiKey": []}]}}, "/usercenter/v1/user/detail": {"post": {"summary": "获得用户数据", "operationId": "detail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/UserInfoResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UserInfoReq"}}], "tags": ["user"], "security": [{"apiKey": []}]}}, "/usercenter/v1/user/login": {"post": {"summary": "登录", "operationId": "login", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/LoginResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/LoginReq"}}], "tags": ["user"]}}, "/usercenter/v1/user/register": {"post": {"summary": "注册", "operationId": "register", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/RegisterResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/RegisterReq"}}], "tags": ["user"]}}, "/usercenter/v1/user/setAdmin": {"post": {"summary": "设置user为admin", "operationId": "set<PERSON>d<PERSON>", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/SetAdminResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SetAdminReq"}}], "tags": ["user"], "security": [{"apiKey": []}]}}, "/usercenter/v1/user/update": {"put": {"summary": "修改用户基本信息", "operationId": "update", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/UserUpdateResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UserUpdateReq"}}], "tags": ["user"], "security": [{"apiKey": []}]}}, "/usercenter/v1/user/userWonDynamicCommentList": {"post": {"summary": "累计奖品发布动态用户晒单列表", "operationId": "UserWonDynamicCommentList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/UserWonDynamicCommentResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UserWonDynamicCommentReq"}}], "tags": ["userWonDynamicComment"], "security": [{"apiKey": []}]}}, "/usercenter/v1/user/wxMiniAuth": {"post": {"summary": "小程序注册登录", "operationId": "wxMiniAuth", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/WXMiniAuthResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/WXMiniAuthReq"}}], "tags": ["user"]}}, "/usercenter/v1/userContact/ContactDel": {"post": {"summary": "批量删除联系方式", "operationId": "ContactDel", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ContactDelResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ContactDelReq"}}], "tags": ["userContact"], "security": [{"apiKey": []}]}}, "/usercenter/v1/userContact/addContact": {"post": {"summary": "添加抽奖发起人联系方式", "operationId": "addContact", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/CreateContactResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CreateContactReq"}}], "tags": ["userContact"], "security": [{"apiKey": []}]}}, "/usercenter/v1/userContact/contactList": {"post": {"summary": "我的联系方式列表", "operationId": "contactList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ContactListResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ContactListReq"}}], "tags": ["userContact"], "security": [{"apiKey": []}]}}, "/usercenter/v1/userContact/sponsorDel": {"post": {"summary": "删除（赞助商）", "operationId": "sponsorDel", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sponsorDelResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sponsorDelReq"}}], "tags": ["userSponsor"], "security": [{"apiKey": []}]}}, "/usercenter/v1/userContact/upDateContact": {"post": {"summary": "修改抽奖发起人联系方式", "operationId": "upDateContact", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/UpDateContactResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UpDateContactReq"}}], "tags": ["userContact"], "security": [{"apiKey": []}]}}, "/usercenter/v1/userContact/upDateSponsor": {"post": {"summary": "修改抽奖发起人（赞助商）", "operationId": "upDateSponsor", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/UpdateSponsorResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UpdateSponsorReq"}}], "tags": ["userSponsor"], "security": [{"apiKey": []}]}}, "/usercenter/v1/userSponsor/addSponsor": {"post": {"summary": "添加 抽奖发起人（赞助商）", "operationId": "addSponsor", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/CreateSponsorResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/CreateSponsorReq"}}], "tags": ["userSponsor"], "security": [{"apiKey": []}]}}, "/usercenter/v1/userSponsor/sponsorDetail": {"post": {"summary": "抽奖发起人（赞助商）详情", "operationId": "sponsorDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/SponosorDetailResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SponosorDetailReq"}}], "tags": ["userSponsor"]}}, "/usercenter/v1/userSponsor/sponsorList": {"post": {"summary": "我的赞助商列表（赞助商）", "operationId": "sponsorList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/SponsorListResp"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SponsorListReq"}}], "tags": ["userSponsor"], "security": [{"apiKey": []}]}}}, "definitions": {"AddAddressReq": {"type": "object", "properties": {"contactName": {"type": "string"}, "contactMobile": {"type": "string"}, "district": {"$ref": "#/definitions/DistrictInfo"}, "detail": {"type": "string"}, "postcode": {"type": "string"}, "isDefault": {"type": "integer", "format": "int64"}}, "title": "AddAddressReq"}, "AddAddressResp": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "title": "AddAddressResp", "required": ["id"]}, "AddressInfo": {"type": "object", "properties": {"contactName": {"type": "string"}, "contactMobile": {"type": "string"}, "district": {"$ref": "#/definitions/DistrictInfo"}, "detail": {"type": "string"}, "postcode": {"type": "string"}, "isDefault": {"type": "integer", "format": "int64"}}, "title": "AddressInfo", "required": ["contactName", "contactMobile", "district", "detail", "postcode", "isDefault"]}, "AddressListReq": {"type": "object", "properties": {"page": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int64"}}, "title": "AddressListReq", "required": ["page", "pageSize"]}, "AddressListResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/UserAddress"}}}, "title": "AddressListResp", "required": ["list"]}, "Contact": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}, "content": {"type": "string"}, "remark": {"type": "string"}}, "title": "Contact", "required": ["id", "userId", "content", "remark"]}, "ContactDelReq": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "title": "ContactDelReq", "required": ["id"]}, "ContactDelResp": {"type": "object", "title": "ContactDelResp"}, "ContactDetailReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "title": "ContactDetailReq", "required": ["id"]}, "ContactDetailResp": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "content": {"type": "string"}, "remark": {"type": "string"}}, "title": "ContactDetailResp", "required": ["id", "content", "remark"]}, "ContactListReq": {"type": "object", "properties": {"page": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int64"}}, "title": "ContactListReq", "required": ["page", "pageSize"]}, "ContactListResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/Contact"}}}, "title": "ContactListResp", "required": ["list"]}, "ConvertAddressReq": {"type": "object", "properties": {"originalAddressInfo": {"type": "string"}}, "title": "ConvertAddressReq", "required": ["originalAddressInfo"]}, "ConvertAddressResp": {"type": "object", "properties": {"contactName": {"type": "string"}, "contactMobile": {"type": "string"}, "district": {"$ref": "#/definitions/DistrictInfo"}, "detail": {"type": "string"}, "postcode": {"type": "string"}, "isDefault": {"type": "integer", "format": "int64"}}, "title": "ConvertAddressResp"}, "CreateContactReq": {"type": "object", "properties": {"content": {"type": "string"}, "remark": {"type": "string"}}, "title": "CreateContactReq", "required": ["content", "remark"]}, "CreateContactResp": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "title": "CreateContactResp", "required": ["id"]}, "CreateDynamicInfo": {"type": "object", "properties": {"dynamicUrl": {"type": "string"}, "remark": {"type": "string"}}, "title": "CreateDynamicInfo", "required": ["dynamicUrl", "remark"]}, "CreateDynamicReq": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64"}, "dynamicUrl": {"type": "string"}, "remark": {"type": "string"}}, "title": "CreateDynamicReq", "required": ["userId", "dynamicUrl", "remark"]}, "CreateDynamicResp": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "title": "CreateDynamicResp", "required": ["id"]}, "CreateSponsorReq": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64"}, "type": {"type": "integer", "format": "int64"}, "appletType": {"type": "integer", "format": "int64"}, "isShow": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "desc": {"type": "string"}, "avatar": {"type": "string"}, "qr_code": {"type": "string"}, "inputA": {"type": "string"}, "inputB": {"type": "string"}}, "title": "CreateSponsorReq", "required": ["userId", "type", "appletType", "isShow", "name", "desc", "avatar", "qr_code", "inputA", "inputB"]}, "CreateSponsorResp": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "title": "CreateSponsorResp", "required": ["id"]}, "DeleteDynamicReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}}, "title": "DeleteDynamicReq", "required": ["id", "userId"]}, "DeleteDynamicResp": {"type": "object", "title": "DeleteDynamicResp"}, "DistrictInfo": {"type": "object", "properties": {"province": {"$ref": "#/definitions/DistrictItem"}, "city": {"$ref": "#/definitions/DistrictItem"}, "county": {"$ref": "#/definitions/DistrictItem"}, "town": {"$ref": "#/definitions/DistrictItem"}}, "title": "DistrictInfo", "required": ["province", "city", "county"]}, "DistrictItem": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}, "title": "DistrictItem", "required": ["name"]}, "DynamicInfo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "dynamicUrl": {"type": "string"}, "remark": {"type": "string"}, "updateTime": {"type": "integer", "format": "int64"}}, "title": "DynamicInfo", "required": ["id", "dynamicUrl", "remark", "updateTime"]}, "LoginReq": {"type": "object", "properties": {"mobile": {"type": "string"}, "password": {"type": "string"}}, "title": "LoginReq", "required": ["mobile", "password"]}, "LoginResp": {"type": "object", "properties": {"accessToken": {"type": "string"}, "accessExpire": {"type": "integer", "format": "int64"}, "refreshAfter": {"type": "integer", "format": "int64"}}, "title": "LoginResp", "required": ["accessToken", "accessExpire", "refreshAfter"]}, "RegisterReq": {"type": "object", "properties": {"mobile": {"type": "string"}, "password": {"type": "string"}}, "title": "RegisterReq", "required": ["mobile", "password"]}, "RegisterResp": {"type": "object", "properties": {"accessToken": {"type": "string"}, "accessExpire": {"type": "integer", "format": "int64"}, "refreshAfter": {"type": "integer", "format": "int64"}}, "title": "RegisterResp", "required": ["accessToken", "accessExpire", "refreshAfter"]}, "SetAdminReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "title": "SetAdminReq", "required": ["id"]}, "SetAdminResp": {"type": "object", "title": "SetAdminResp"}, "SponosorDetailReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "title": "SponosorDetailReq", "required": ["id"]}, "SponosorDetailResp": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}, "type": {"type": "integer", "format": "int64"}, "appletType": {"type": "integer", "format": "int64"}, "isShow": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "desc": {"type": "string"}, "avatar": {"type": "string"}, "qr_code": {"type": "string"}, "inputA": {"type": "string"}, "inputB": {"type": "string"}}, "title": "SponosorDetailResp", "required": ["id", "userId", "type", "appletType", "isShow", "name", "desc", "avatar", "qr_code", "inputA", "inputB"]}, "Sponsor": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}, "type": {"type": "integer", "format": "int64"}, "appletType": {"type": "integer", "format": "int64"}, "isShow": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "desc": {"type": "string"}, "avatar": {"type": "string"}, "qr_code": {"type": "string"}, "inputA": {"type": "string"}, "inputB": {"type": "string"}}, "title": "Sponsor", "required": ["id", "userId", "type", "appletType", "isShow", "name", "desc", "avatar", "qr_code", "inputA", "inputB"]}, "SponsorListReq": {"type": "object", "properties": {"page": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int64"}}, "title": "SponsorListReq", "required": ["page", "pageSize"]}, "SponsorListResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/Sponsor"}}}, "title": "SponsorListResp", "required": ["list"]}, "UpDateContactReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "content": {"type": "string"}, "remark": {"type": "string"}}, "title": "UpDateContactReq", "required": ["id", "content", "remark"]}, "UpDateContactResp": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "title": "UpDateContactResp", "required": ["id"]}, "UpdateSponsorReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}, "type": {"type": "integer", "format": "int64"}, "appletType": {"type": "integer", "format": "int64"}, "isShow": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "desc": {"type": "string"}, "avatar": {"type": "string"}, "qr_code": {"type": "string"}, "inputA": {"type": "string"}, "inputB": {"type": "string"}}, "title": "UpdateSponsorReq", "required": ["id", "userId", "type", "appletType", "isShow", "name", "desc", "avatar", "qr_code", "inputA", "inputB"]}, "UpdateSponsorResp": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "title": "UpdateSponsorResp", "required": ["id"]}, "User": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "mobile": {"type": "string"}, "nickname": {"type": "string"}, "sex": {"type": "integer", "format": "int64"}, "avatar": {"type": "string"}, "info": {"type": "string"}, "isAdmin": {"type": "integer", "format": "int64"}, "signature": {"type": "string"}, "longitude": {"type": "number", "format": "double"}, "latitude": {"type": "number", "format": "double"}, "participation_count": {"type": "integer", "format": "int64"}, "created_count": {"type": "integer", "format": "int64"}, "won_count": {"type": "integer", "format": "int64"}, "integral": {"type": "integer", "format": "int64"}}, "title": "User", "required": ["id", "mobile", "nickname", "sex", "avatar", "info", "isAdmin", "signature", "longitude", "latitude", "participation_count", "created_count", "won_count", "integral"]}, "UserAddress": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}, "contactName": {"type": "string"}, "contactMobile": {"type": "string"}, "district": {"$ref": "#/definitions/DistrictInfo"}, "detail": {"type": "string"}, "postcode": {"type": "string"}, "isDefault": {"type": "integer", "format": "int64"}}, "title": "User<PERSON>ddress", "required": ["id", "userId"]}, "UserCommentInfo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}, "lotteryId": {"type": "integer", "format": "int64"}, "prizeName": {"type": "string"}, "content": {"type": "string"}, "pics": {"type": "string"}, "praiseCount": {"type": "integer", "format": "int64"}, "createTime": {"type": "integer", "format": "int64"}, "updateTime": {"type": "integer", "format": "int64"}, "isPraise": {"type": "integer", "format": "int64"}}, "title": "UserCommentInfo"}, "UserInfoReq": {"type": "object", "title": "UserInfoReq"}, "UserInfoResp": {"type": "object", "properties": {"userInfo": {"$ref": "#/definitions/User"}}, "title": "UserInfoResp", "required": ["userInfo"]}, "UserUpdateReq": {"type": "object", "properties": {"nickname": {"type": "string"}, "sex": {"type": "integer", "format": "int64"}, "avatar": {"type": "string"}, "info": {"type": "string"}, "signature": {"type": "string"}, "longitude": {"type": "number", "format": "double"}, "latitude": {"type": "number", "format": "double"}}, "title": "UserUpdateReq", "required": ["nickname", "sex", "avatar", "info", "signature", "longitude", "latitude"]}, "UserUpdateResp": {"type": "object", "title": "UserUpdateResp"}, "UserWonDynamicCommentReq": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int64"}}, "title": "UserWonDynamicCommentReq", "required": ["userId"]}, "UserWonDynamicCommentResp": {"type": "object", "properties": {"count": {"type": "integer", "format": "int64"}, "userDynamicList": {"type": "array", "items": {"$ref": "#/definitions/DynamicInfo"}}, "userCommentList": {"type": "array", "items": {"$ref": "#/definitions/UserCommentInfo"}}}, "title": "UserWonDynamicCommentResp", "required": ["count", "userDynamicList", "userCommentList"]}, "WXMiniAuthReq": {"type": "object", "properties": {"code": {"type": "string"}, "iv": {"type": "string"}, "encryptedData": {"type": "string"}, "nickname": {"type": "string"}, "avatar": {"type": "string"}}, "title": "WXMiniAuthReq", "required": ["code", "iv", "encryptedData", "nickname", "avatar"]}, "WXMiniAuthResp": {"type": "object", "properties": {"accessToken": {"type": "string"}, "accessExpire": {"type": "integer", "format": "int64"}, "refreshAfter": {"type": "integer", "format": "int64"}}, "title": "WXMiniAuthResp", "required": ["accessToken", "accessExpire", "refreshAfter"]}, "sponsorDelReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "title": "sponsorDelReq", "required": ["id"]}, "sponsorDelResp": {"type": "object", "title": "sponsorDelResp"}}, "securityDefinitions": {"apiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Enter JWT Bearer token **_only_**", "name": "Authorization", "in": "header"}}}