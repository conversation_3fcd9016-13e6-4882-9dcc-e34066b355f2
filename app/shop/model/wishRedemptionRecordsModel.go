package model

import (
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ WishRedemptionRecordsModel = (*customWishRedemptionRecordsModel)(nil)

type (
	// WishRedemptionRecordsModel is an interface to be customized, add more methods here,
	// and implement the added methods in customWishRedemptionRecordsModel.
	WishRedemptionRecordsModel interface {
		wishRedemptionRecordsModel
		GetWishExchangeListByUId(userId, page, pageSize int64) ([]*WishRedemptionRecords, error)
		WishExchange(userId int64, goodsSign string, integral int64, extra string) error
	}

	customWishRedemptionRecordsModel struct {
		*defaultWishRedemptionRecordsModel
	}
)

// NewWishRedemptionRecordsModel returns a model for the database table.
func NewWishRedemptionRecordsModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) WishRedemptionRecordsModel {
	return &customWishRedemptionRecordsModel{
		defaultWishRedemptionRecordsModel: newWishRedemptionRecordsModel(conn, c, opts...),
	}
}

func (m *customWishRedemptionRecordsModel) GetWishExchangeListByUId(userId, page, pageSize int64) ([]*WishRedemptionRecords, error) {
	var resp []*WishRedemptionRecords
	err := m.QueryRowsNoCache(&resp, "select * from wish_redemption_records where user_id = ? limit ?, ?", userId, (page-1)*pageSize, pageSize)
	return resp, err
}

func (m *customWishRedemptionRecordsModel) WishExchange(userId int64, goodsSign string, integral int64, extra string) error {
	_, err := m.ExecNoCache("insert into wish_redemption_records (user_id, goods_sign, integral, extra) values (?, ?, ?, ?)", userId, goodsSign, integral, extra)
	return err
}
