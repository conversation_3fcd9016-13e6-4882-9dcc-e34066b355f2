Name: comment-api
Host: 0.0.0.0
Port: 1009
Mode: dev

#jwtAuth
JwtAuth:
  AccessSecret: ae0536f9-6450-4606-8e13-5a19ed505da0

Log:
  ServiceName: comment-api
  Mode: console
  Level: error
  Encoding: plain
  # Mode: file
  # 因为项目在docker中启动，所以logs是打印在docker中
  #  Path: logs
  # 打印堆栈信息 方便查询错误
#  Encoding: plain
#  Compress: true
#  KeyDays: 7

#监控
Prometheus:
  Host: 0.0.0.0
  Port: 4031
  Path: /metrics

#链路追踪
Telemetry:
  Name: comment-api
  Endpoint: http://jaeger:14268/api/traces
  Sampler: 1.0
  Batcher: jaeger

DB:
  DataSource: root:PXDN93VRKUm8TeE7@tcp(mysql:3306)/lottery?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai
Cache:
  - Host: redis:6379
    Pass: G62m50oigInC30sf

#rpc
UsercenterRpcConf:
  Endpoints:
    - 127.0.0.1:2004
  NonBlock: true

CommentRpcConf:
  Endpoints:
    - 127.0.0.1:2009
  NonBlock: true

LotteryRpcConf:
  Endpoints:
    - 127.0.0.1:2005
  NonBlock: true