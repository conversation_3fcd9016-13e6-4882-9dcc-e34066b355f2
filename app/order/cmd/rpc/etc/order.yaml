Name: order-rpc
ListenOn: 0.0.0.0:2001
Mode: dev

Log:
  ServiceName: order-rpc
  Level: error

#监控
Prometheus:
  Host: 0.0.0.0
  Port: 4002
  Path: /metrics

#链路追踪
Telemetry:
  Name: order-rpc
  Endpoint:  http://jaeger:14268/api/traces
  Sampler: 1.0
  Batcher: jaeger

Redis:
  Host: redis:6379
  Type: node
  Pass: G62m50oigInC30sf
  Key : order-rpc
DB:
  DataSource: root:PXDN93VRKUm8TeE7@tcp(mysql:3306)/lottery?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai
Cache:
  - Host: redis:6379
    Pass: G62m50oigInC30sf

#rpc
TravelRpcConf:
  Endpoints:
    - 127.0.0.1:2003
  NonBlock: true

