// Code generated by goctl. DO NOT EDIT.
package handler

import (
	"net/http"

	vote "looklook/app/vote/cmd/api/internal/handler/vote"
	"looklook/app/vote/cmd/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/vote/createVote",
				Handler: vote.CreateVoteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/vote/enableVoteDetail",
				Handler: vote.EnableVoteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/vote/updateVote",
				Handler: vote.UpdateVoteHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/vote/viewVoteDetail",
				Handler: vote.ViewVoteDetailHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.JwtAuth.AccessSecret),
		rest.WithPrefix("/vote/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/vote/createVoteRecord",
				Handler: vote.CreateVoteRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/vote/viewUserVoteRecordDetail",
				Handler: vote.ViewUserVoteRecordDetailHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/vote/viewVoteRecordDetail",
				Handler: vote.ViewVoteRecordDetailHandler(serverCtx),
			},
		},
		rest.WithJwt(serverCtx.Config.JwtAuth.AccessSecret),
		rest.WithPrefix("/vote/v1"),
	)
}
