// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.19.4
// source: checkin.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Checkin_AddCheckinRecord_FullMethodName          = "/pb.checkin/AddCheckinRecord"
	Checkin_UpdateCheckinRecord_FullMethodName       = "/pb.checkin/UpdateCheckinRecord"
	Checkin_GetCheckinRecordByUserId_FullMethodName  = "/pb.checkin/GetCheckinRecordByUserId"
	Checkin_AddIntegral_FullMethodName               = "/pb.checkin/AddIntegral"
	Checkin_AddIntegralRecord_FullMethodName         = "/pb.checkin/AddIntegralRecord"
	Checkin_UpdateIntegralRecord_FullMethodName      = "/pb.checkin/UpdateIntegralRecord"
	Checkin_DelIntegralRecord_FullMethodName         = "/pb.checkin/DelIntegralRecord"
	Checkin_GetIntegralRecordById_FullMethodName     = "/pb.checkin/GetIntegralRecordById"
	Checkin_GetIntegralRecordByUserId_FullMethodName = "/pb.checkin/GetIntegralRecordByUserId"
	Checkin_SearchIntegralRecord_FullMethodName      = "/pb.checkin/SearchIntegralRecord"
	Checkin_DeductIntegral_FullMethodName            = "/pb.checkin/DeductIntegral"
	Checkin_AddTaskRecord_FullMethodName             = "/pb.checkin/AddTaskRecord"
	Checkin_UpdateTaskRecord_FullMethodName          = "/pb.checkin/UpdateTaskRecord"
	Checkin_DelTaskRecord_FullMethodName             = "/pb.checkin/DelTaskRecord"
	Checkin_GetTaskRecordById_FullMethodName         = "/pb.checkin/GetTaskRecordById"
	Checkin_GetTaskRecordByUserId_FullMethodName     = "/pb.checkin/GetTaskRecordByUserId"
	Checkin_SearchTaskRecord_FullMethodName          = "/pb.checkin/SearchTaskRecord"
	Checkin_AddTasks_FullMethodName                  = "/pb.checkin/AddTasks"
	Checkin_UpdateTasks_FullMethodName               = "/pb.checkin/UpdateTasks"
	Checkin_DelTasks_FullMethodName                  = "/pb.checkin/DelTasks"
	Checkin_GetTasksById_FullMethodName              = "/pb.checkin/GetTasksById"
	Checkin_SearchTasks_FullMethodName               = "/pb.checkin/SearchTasks"
	Checkin_GetTaskProgress_FullMethodName           = "/pb.checkin/GetTaskProgress"
	Checkin_UpdateSub_FullMethodName                 = "/pb.checkin/UpdateSub"
	Checkin_NoticeWishCheckin_FullMethodName         = "/pb.checkin/NoticeWishCheckin"
	Checkin_GetIntegralByUserId_FullMethodName       = "/pb.checkin/GetIntegralByUserId"
)

// CheckinClient is the client API for Checkin service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CheckinClient interface {
	// -----------------------checkinRecord--------------------
	AddCheckinRecord(ctx context.Context, in *AddCheckinRecordReq, opts ...grpc.CallOption) (*AddCheckinRecordResp, error)
	UpdateCheckinRecord(ctx context.Context, in *UpdateCheckinRecordReq, opts ...grpc.CallOption) (*UpdateCheckinRecordResp, error)
	GetCheckinRecordByUserId(ctx context.Context, in *GetCheckinRecordByUserIdReq, opts ...grpc.CallOption) (*GetCheckinRecordByUserIdResp, error)
	// -----------------------integralRecord-------------------
	AddIntegral(ctx context.Context, in *AddIntegralReq, opts ...grpc.CallOption) (*AddIntegralResp, error)
	AddIntegralRecord(ctx context.Context, in *AddIntegralRecordReq, opts ...grpc.CallOption) (*AddIntegralRecordResp, error)
	UpdateIntegralRecord(ctx context.Context, in *UpdateIntegralRecordReq, opts ...grpc.CallOption) (*UpdateIntegralRecordResp, error)
	DelIntegralRecord(ctx context.Context, in *DelIntegralRecordReq, opts ...grpc.CallOption) (*DelIntegralRecordResp, error)
	GetIntegralRecordById(ctx context.Context, in *GetIntegralRecordByIdReq, opts ...grpc.CallOption) (*GetIntegralRecordByIdResp, error)
	GetIntegralRecordByUserId(ctx context.Context, in *GetIntegralRecordByUserIdReq, opts ...grpc.CallOption) (*GetIntegralRecordByUserIdResp, error)
	SearchIntegralRecord(ctx context.Context, in *SearchIntegralRecordReq, opts ...grpc.CallOption) (*SearchIntegralRecordResp, error)
	DeductIntegral(ctx context.Context, in *DeductIntegralReq, opts ...grpc.CallOption) (*DeductIntegralResp, error)
	// -----------------------taskRecord-----------------------
	AddTaskRecord(ctx context.Context, in *AddTaskRecordReq, opts ...grpc.CallOption) (*AddTaskRecordResp, error)
	UpdateTaskRecord(ctx context.Context, in *UpdateTaskRecordReq, opts ...grpc.CallOption) (*UpdateTaskRecordResp, error)
	DelTaskRecord(ctx context.Context, in *DelTaskRecordReq, opts ...grpc.CallOption) (*DelTaskRecordResp, error)
	GetTaskRecordById(ctx context.Context, in *GetTaskRecordByIdReq, opts ...grpc.CallOption) (*GetTaskRecordByIdResp, error)
	GetTaskRecordByUserId(ctx context.Context, in *GetTaskRecordByUserIdReq, opts ...grpc.CallOption) (*GetTaskRecordByUserIdResp, error)
	SearchTaskRecord(ctx context.Context, in *SearchTaskRecordReq, opts ...grpc.CallOption) (*SearchTaskRecordResp, error)
	// -----------------------tasks-----------------------------
	AddTasks(ctx context.Context, in *AddTasksReq, opts ...grpc.CallOption) (*AddTasksResp, error)
	UpdateTasks(ctx context.Context, in *UpdateTasksReq, opts ...grpc.CallOption) (*UpdateTasksResp, error)
	DelTasks(ctx context.Context, in *DelTasksReq, opts ...grpc.CallOption) (*DelTasksResp, error)
	GetTasksById(ctx context.Context, in *GetTasksByIdReq, opts ...grpc.CallOption) (*GetTasksByIdResp, error)
	SearchTasks(ctx context.Context, in *SearchTasksReq, opts ...grpc.CallOption) (*SearchTasksResp, error)
	// -----------------------taskProgress----------------------
	GetTaskProgress(ctx context.Context, in *GetTaskProgressReq, opts ...grpc.CallOption) (*GetTaskProgressResp, error)
	UpdateSub(ctx context.Context, in *UpdateSubReq, opts ...grpc.CallOption) (*UpdateSubResp, error)
	// -----------------------others----------------------
	NoticeWishCheckin(ctx context.Context, in *NoticeWishCheckinReq, opts ...grpc.CallOption) (*NoticeWishCheckinResp, error)
	GetIntegralByUserId(ctx context.Context, in *GetIntegralByUserIdReq, opts ...grpc.CallOption) (*GetIntegralByUserIdResp, error)
}

type checkinClient struct {
	cc grpc.ClientConnInterface
}

func NewCheckinClient(cc grpc.ClientConnInterface) CheckinClient {
	return &checkinClient{cc}
}

func (c *checkinClient) AddCheckinRecord(ctx context.Context, in *AddCheckinRecordReq, opts ...grpc.CallOption) (*AddCheckinRecordResp, error) {
	out := new(AddCheckinRecordResp)
	err := c.cc.Invoke(ctx, Checkin_AddCheckinRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) UpdateCheckinRecord(ctx context.Context, in *UpdateCheckinRecordReq, opts ...grpc.CallOption) (*UpdateCheckinRecordResp, error) {
	out := new(UpdateCheckinRecordResp)
	err := c.cc.Invoke(ctx, Checkin_UpdateCheckinRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) GetCheckinRecordByUserId(ctx context.Context, in *GetCheckinRecordByUserIdReq, opts ...grpc.CallOption) (*GetCheckinRecordByUserIdResp, error) {
	out := new(GetCheckinRecordByUserIdResp)
	err := c.cc.Invoke(ctx, Checkin_GetCheckinRecordByUserId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) AddIntegral(ctx context.Context, in *AddIntegralReq, opts ...grpc.CallOption) (*AddIntegralResp, error) {
	out := new(AddIntegralResp)
	err := c.cc.Invoke(ctx, Checkin_AddIntegral_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) AddIntegralRecord(ctx context.Context, in *AddIntegralRecordReq, opts ...grpc.CallOption) (*AddIntegralRecordResp, error) {
	out := new(AddIntegralRecordResp)
	err := c.cc.Invoke(ctx, Checkin_AddIntegralRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) UpdateIntegralRecord(ctx context.Context, in *UpdateIntegralRecordReq, opts ...grpc.CallOption) (*UpdateIntegralRecordResp, error) {
	out := new(UpdateIntegralRecordResp)
	err := c.cc.Invoke(ctx, Checkin_UpdateIntegralRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) DelIntegralRecord(ctx context.Context, in *DelIntegralRecordReq, opts ...grpc.CallOption) (*DelIntegralRecordResp, error) {
	out := new(DelIntegralRecordResp)
	err := c.cc.Invoke(ctx, Checkin_DelIntegralRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) GetIntegralRecordById(ctx context.Context, in *GetIntegralRecordByIdReq, opts ...grpc.CallOption) (*GetIntegralRecordByIdResp, error) {
	out := new(GetIntegralRecordByIdResp)
	err := c.cc.Invoke(ctx, Checkin_GetIntegralRecordById_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) GetIntegralRecordByUserId(ctx context.Context, in *GetIntegralRecordByUserIdReq, opts ...grpc.CallOption) (*GetIntegralRecordByUserIdResp, error) {
	out := new(GetIntegralRecordByUserIdResp)
	err := c.cc.Invoke(ctx, Checkin_GetIntegralRecordByUserId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) SearchIntegralRecord(ctx context.Context, in *SearchIntegralRecordReq, opts ...grpc.CallOption) (*SearchIntegralRecordResp, error) {
	out := new(SearchIntegralRecordResp)
	err := c.cc.Invoke(ctx, Checkin_SearchIntegralRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) DeductIntegral(ctx context.Context, in *DeductIntegralReq, opts ...grpc.CallOption) (*DeductIntegralResp, error) {
	out := new(DeductIntegralResp)
	err := c.cc.Invoke(ctx, Checkin_DeductIntegral_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) AddTaskRecord(ctx context.Context, in *AddTaskRecordReq, opts ...grpc.CallOption) (*AddTaskRecordResp, error) {
	out := new(AddTaskRecordResp)
	err := c.cc.Invoke(ctx, Checkin_AddTaskRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) UpdateTaskRecord(ctx context.Context, in *UpdateTaskRecordReq, opts ...grpc.CallOption) (*UpdateTaskRecordResp, error) {
	out := new(UpdateTaskRecordResp)
	err := c.cc.Invoke(ctx, Checkin_UpdateTaskRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) DelTaskRecord(ctx context.Context, in *DelTaskRecordReq, opts ...grpc.CallOption) (*DelTaskRecordResp, error) {
	out := new(DelTaskRecordResp)
	err := c.cc.Invoke(ctx, Checkin_DelTaskRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) GetTaskRecordById(ctx context.Context, in *GetTaskRecordByIdReq, opts ...grpc.CallOption) (*GetTaskRecordByIdResp, error) {
	out := new(GetTaskRecordByIdResp)
	err := c.cc.Invoke(ctx, Checkin_GetTaskRecordById_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) GetTaskRecordByUserId(ctx context.Context, in *GetTaskRecordByUserIdReq, opts ...grpc.CallOption) (*GetTaskRecordByUserIdResp, error) {
	out := new(GetTaskRecordByUserIdResp)
	err := c.cc.Invoke(ctx, Checkin_GetTaskRecordByUserId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) SearchTaskRecord(ctx context.Context, in *SearchTaskRecordReq, opts ...grpc.CallOption) (*SearchTaskRecordResp, error) {
	out := new(SearchTaskRecordResp)
	err := c.cc.Invoke(ctx, Checkin_SearchTaskRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) AddTasks(ctx context.Context, in *AddTasksReq, opts ...grpc.CallOption) (*AddTasksResp, error) {
	out := new(AddTasksResp)
	err := c.cc.Invoke(ctx, Checkin_AddTasks_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) UpdateTasks(ctx context.Context, in *UpdateTasksReq, opts ...grpc.CallOption) (*UpdateTasksResp, error) {
	out := new(UpdateTasksResp)
	err := c.cc.Invoke(ctx, Checkin_UpdateTasks_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) DelTasks(ctx context.Context, in *DelTasksReq, opts ...grpc.CallOption) (*DelTasksResp, error) {
	out := new(DelTasksResp)
	err := c.cc.Invoke(ctx, Checkin_DelTasks_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) GetTasksById(ctx context.Context, in *GetTasksByIdReq, opts ...grpc.CallOption) (*GetTasksByIdResp, error) {
	out := new(GetTasksByIdResp)
	err := c.cc.Invoke(ctx, Checkin_GetTasksById_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) SearchTasks(ctx context.Context, in *SearchTasksReq, opts ...grpc.CallOption) (*SearchTasksResp, error) {
	out := new(SearchTasksResp)
	err := c.cc.Invoke(ctx, Checkin_SearchTasks_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) GetTaskProgress(ctx context.Context, in *GetTaskProgressReq, opts ...grpc.CallOption) (*GetTaskProgressResp, error) {
	out := new(GetTaskProgressResp)
	err := c.cc.Invoke(ctx, Checkin_GetTaskProgress_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) UpdateSub(ctx context.Context, in *UpdateSubReq, opts ...grpc.CallOption) (*UpdateSubResp, error) {
	out := new(UpdateSubResp)
	err := c.cc.Invoke(ctx, Checkin_UpdateSub_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) NoticeWishCheckin(ctx context.Context, in *NoticeWishCheckinReq, opts ...grpc.CallOption) (*NoticeWishCheckinResp, error) {
	out := new(NoticeWishCheckinResp)
	err := c.cc.Invoke(ctx, Checkin_NoticeWishCheckin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) GetIntegralByUserId(ctx context.Context, in *GetIntegralByUserIdReq, opts ...grpc.CallOption) (*GetIntegralByUserIdResp, error) {
	out := new(GetIntegralByUserIdResp)
	err := c.cc.Invoke(ctx, Checkin_GetIntegralByUserId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CheckinServer is the server API for Checkin service.
// All implementations must embed UnimplementedCheckinServer
// for forward compatibility.
type CheckinServer interface {
	// -----------------------checkinRecord--------------------
	AddCheckinRecord(context.Context, *AddCheckinRecordReq) (*AddCheckinRecordResp, error)
	UpdateCheckinRecord(context.Context, *UpdateCheckinRecordReq) (*UpdateCheckinRecordResp, error)
	GetCheckinRecordByUserId(context.Context, *GetCheckinRecordByUserIdReq) (*GetCheckinRecordByUserIdResp, error)
	// -----------------------integralRecord-------------------
	AddIntegral(context.Context, *AddIntegralReq) (*AddIntegralResp, error)
	AddIntegralRecord(context.Context, *AddIntegralRecordReq) (*AddIntegralRecordResp, error)
	UpdateIntegralRecord(context.Context, *UpdateIntegralRecordReq) (*UpdateIntegralRecordResp, error)
	DelIntegralRecord(context.Context, *DelIntegralRecordReq) (*DelIntegralRecordResp, error)
	GetIntegralRecordById(context.Context, *GetIntegralRecordByIdReq) (*GetIntegralRecordByIdResp, error)
	GetIntegralRecordByUserId(context.Context, *GetIntegralRecordByUserIdReq) (*GetIntegralRecordByUserIdResp, error)
	SearchIntegralRecord(context.Context, *SearchIntegralRecordReq) (*SearchIntegralRecordResp, error)
	DeductIntegral(context.Context, *DeductIntegralReq) (*DeductIntegralResp, error)
	// -----------------------taskRecord-----------------------
	AddTaskRecord(context.Context, *AddTaskRecordReq) (*AddTaskRecordResp, error)
	UpdateTaskRecord(context.Context, *UpdateTaskRecordReq) (*UpdateTaskRecordResp, error)
	DelTaskRecord(context.Context, *DelTaskRecordReq) (*DelTaskRecordResp, error)
	GetTaskRecordById(context.Context, *GetTaskRecordByIdReq) (*GetTaskRecordByIdResp, error)
	GetTaskRecordByUserId(context.Context, *GetTaskRecordByUserIdReq) (*GetTaskRecordByUserIdResp, error)
	SearchTaskRecord(context.Context, *SearchTaskRecordReq) (*SearchTaskRecordResp, error)
	// -----------------------tasks-----------------------------
	AddTasks(context.Context, *AddTasksReq) (*AddTasksResp, error)
	UpdateTasks(context.Context, *UpdateTasksReq) (*UpdateTasksResp, error)
	DelTasks(context.Context, *DelTasksReq) (*DelTasksResp, error)
	GetTasksById(context.Context, *GetTasksByIdReq) (*GetTasksByIdResp, error)
	SearchTasks(context.Context, *SearchTasksReq) (*SearchTasksResp, error)
	// -----------------------taskProgress----------------------
	GetTaskProgress(context.Context, *GetTaskProgressReq) (*GetTaskProgressResp, error)
	UpdateSub(context.Context, *UpdateSubReq) (*UpdateSubResp, error)
	// -----------------------others----------------------
	NoticeWishCheckin(context.Context, *NoticeWishCheckinReq) (*NoticeWishCheckinResp, error)
	GetIntegralByUserId(context.Context, *GetIntegralByUserIdReq) (*GetIntegralByUserIdResp, error)
	mustEmbedUnimplementedCheckinServer()
}

// UnimplementedCheckinServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCheckinServer struct{}

func (UnimplementedCheckinServer) AddCheckinRecord(context.Context, *AddCheckinRecordReq) (*AddCheckinRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddCheckinRecord not implemented")
}
func (UnimplementedCheckinServer) UpdateCheckinRecord(context.Context, *UpdateCheckinRecordReq) (*UpdateCheckinRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCheckinRecord not implemented")
}
func (UnimplementedCheckinServer) GetCheckinRecordByUserId(context.Context, *GetCheckinRecordByUserIdReq) (*GetCheckinRecordByUserIdResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCheckinRecordByUserId not implemented")
}
func (UnimplementedCheckinServer) AddIntegral(context.Context, *AddIntegralReq) (*AddIntegralResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddIntegral not implemented")
}
func (UnimplementedCheckinServer) AddIntegralRecord(context.Context, *AddIntegralRecordReq) (*AddIntegralRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddIntegralRecord not implemented")
}
func (UnimplementedCheckinServer) UpdateIntegralRecord(context.Context, *UpdateIntegralRecordReq) (*UpdateIntegralRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateIntegralRecord not implemented")
}
func (UnimplementedCheckinServer) DelIntegralRecord(context.Context, *DelIntegralRecordReq) (*DelIntegralRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelIntegralRecord not implemented")
}
func (UnimplementedCheckinServer) GetIntegralRecordById(context.Context, *GetIntegralRecordByIdReq) (*GetIntegralRecordByIdResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIntegralRecordById not implemented")
}
func (UnimplementedCheckinServer) GetIntegralRecordByUserId(context.Context, *GetIntegralRecordByUserIdReq) (*GetIntegralRecordByUserIdResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIntegralRecordByUserId not implemented")
}
func (UnimplementedCheckinServer) SearchIntegralRecord(context.Context, *SearchIntegralRecordReq) (*SearchIntegralRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchIntegralRecord not implemented")
}
func (UnimplementedCheckinServer) DeductIntegral(context.Context, *DeductIntegralReq) (*DeductIntegralResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeductIntegral not implemented")
}
func (UnimplementedCheckinServer) AddTaskRecord(context.Context, *AddTaskRecordReq) (*AddTaskRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddTaskRecord not implemented")
}
func (UnimplementedCheckinServer) UpdateTaskRecord(context.Context, *UpdateTaskRecordReq) (*UpdateTaskRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTaskRecord not implemented")
}
func (UnimplementedCheckinServer) DelTaskRecord(context.Context, *DelTaskRecordReq) (*DelTaskRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelTaskRecord not implemented")
}
func (UnimplementedCheckinServer) GetTaskRecordById(context.Context, *GetTaskRecordByIdReq) (*GetTaskRecordByIdResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTaskRecordById not implemented")
}
func (UnimplementedCheckinServer) GetTaskRecordByUserId(context.Context, *GetTaskRecordByUserIdReq) (*GetTaskRecordByUserIdResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTaskRecordByUserId not implemented")
}
func (UnimplementedCheckinServer) SearchTaskRecord(context.Context, *SearchTaskRecordReq) (*SearchTaskRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchTaskRecord not implemented")
}
func (UnimplementedCheckinServer) AddTasks(context.Context, *AddTasksReq) (*AddTasksResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddTasks not implemented")
}
func (UnimplementedCheckinServer) UpdateTasks(context.Context, *UpdateTasksReq) (*UpdateTasksResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTasks not implemented")
}
func (UnimplementedCheckinServer) DelTasks(context.Context, *DelTasksReq) (*DelTasksResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelTasks not implemented")
}
func (UnimplementedCheckinServer) GetTasksById(context.Context, *GetTasksByIdReq) (*GetTasksByIdResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTasksById not implemented")
}
func (UnimplementedCheckinServer) SearchTasks(context.Context, *SearchTasksReq) (*SearchTasksResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchTasks not implemented")
}
func (UnimplementedCheckinServer) GetTaskProgress(context.Context, *GetTaskProgressReq) (*GetTaskProgressResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTaskProgress not implemented")
}
func (UnimplementedCheckinServer) UpdateSub(context.Context, *UpdateSubReq) (*UpdateSubResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSub not implemented")
}
func (UnimplementedCheckinServer) NoticeWishCheckin(context.Context, *NoticeWishCheckinReq) (*NoticeWishCheckinResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NoticeWishCheckin not implemented")
}
func (UnimplementedCheckinServer) GetIntegralByUserId(context.Context, *GetIntegralByUserIdReq) (*GetIntegralByUserIdResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIntegralByUserId not implemented")
}
func (UnimplementedCheckinServer) mustEmbedUnimplementedCheckinServer() {}
func (UnimplementedCheckinServer) testEmbeddedByValue()                 {}

// UnsafeCheckinServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CheckinServer will
// result in compilation errors.
type UnsafeCheckinServer interface {
	mustEmbedUnimplementedCheckinServer()
}

func RegisterCheckinServer(s grpc.ServiceRegistrar, srv CheckinServer) {
	// If the following call pancis, it indicates UnimplementedCheckinServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Checkin_ServiceDesc, srv)
}

func _Checkin_AddCheckinRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCheckinRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).AddCheckinRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_AddCheckinRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).AddCheckinRecord(ctx, req.(*AddCheckinRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_UpdateCheckinRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCheckinRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).UpdateCheckinRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_UpdateCheckinRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).UpdateCheckinRecord(ctx, req.(*UpdateCheckinRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_GetCheckinRecordByUserId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCheckinRecordByUserIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).GetCheckinRecordByUserId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_GetCheckinRecordByUserId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).GetCheckinRecordByUserId(ctx, req.(*GetCheckinRecordByUserIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_AddIntegral_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddIntegralReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).AddIntegral(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_AddIntegral_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).AddIntegral(ctx, req.(*AddIntegralReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_AddIntegralRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddIntegralRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).AddIntegralRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_AddIntegralRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).AddIntegralRecord(ctx, req.(*AddIntegralRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_UpdateIntegralRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateIntegralRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).UpdateIntegralRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_UpdateIntegralRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).UpdateIntegralRecord(ctx, req.(*UpdateIntegralRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_DelIntegralRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelIntegralRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).DelIntegralRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_DelIntegralRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).DelIntegralRecord(ctx, req.(*DelIntegralRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_GetIntegralRecordById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIntegralRecordByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).GetIntegralRecordById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_GetIntegralRecordById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).GetIntegralRecordById(ctx, req.(*GetIntegralRecordByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_GetIntegralRecordByUserId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIntegralRecordByUserIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).GetIntegralRecordByUserId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_GetIntegralRecordByUserId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).GetIntegralRecordByUserId(ctx, req.(*GetIntegralRecordByUserIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_SearchIntegralRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchIntegralRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).SearchIntegralRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_SearchIntegralRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).SearchIntegralRecord(ctx, req.(*SearchIntegralRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_DeductIntegral_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeductIntegralReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).DeductIntegral(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_DeductIntegral_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).DeductIntegral(ctx, req.(*DeductIntegralReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_AddTaskRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTaskRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).AddTaskRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_AddTaskRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).AddTaskRecord(ctx, req.(*AddTaskRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_UpdateTaskRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTaskRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).UpdateTaskRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_UpdateTaskRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).UpdateTaskRecord(ctx, req.(*UpdateTaskRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_DelTaskRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelTaskRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).DelTaskRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_DelTaskRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).DelTaskRecord(ctx, req.(*DelTaskRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_GetTaskRecordById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTaskRecordByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).GetTaskRecordById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_GetTaskRecordById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).GetTaskRecordById(ctx, req.(*GetTaskRecordByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_GetTaskRecordByUserId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTaskRecordByUserIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).GetTaskRecordByUserId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_GetTaskRecordByUserId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).GetTaskRecordByUserId(ctx, req.(*GetTaskRecordByUserIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_SearchTaskRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchTaskRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).SearchTaskRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_SearchTaskRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).SearchTaskRecord(ctx, req.(*SearchTaskRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_AddTasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTasksReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).AddTasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_AddTasks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).AddTasks(ctx, req.(*AddTasksReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_UpdateTasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTasksReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).UpdateTasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_UpdateTasks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).UpdateTasks(ctx, req.(*UpdateTasksReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_DelTasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelTasksReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).DelTasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_DelTasks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).DelTasks(ctx, req.(*DelTasksReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_GetTasksById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTasksByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).GetTasksById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_GetTasksById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).GetTasksById(ctx, req.(*GetTasksByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_SearchTasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchTasksReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).SearchTasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_SearchTasks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).SearchTasks(ctx, req.(*SearchTasksReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_GetTaskProgress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTaskProgressReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).GetTaskProgress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_GetTaskProgress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).GetTaskProgress(ctx, req.(*GetTaskProgressReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_UpdateSub_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSubReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).UpdateSub(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_UpdateSub_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).UpdateSub(ctx, req.(*UpdateSubReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_NoticeWishCheckin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NoticeWishCheckinReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).NoticeWishCheckin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_NoticeWishCheckin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).NoticeWishCheckin(ctx, req.(*NoticeWishCheckinReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_GetIntegralByUserId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIntegralByUserIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).GetIntegralByUserId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Checkin_GetIntegralByUserId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).GetIntegralByUserId(ctx, req.(*GetIntegralByUserIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Checkin_ServiceDesc is the grpc.ServiceDesc for Checkin service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Checkin_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.checkin",
	HandlerType: (*CheckinServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddCheckinRecord",
			Handler:    _Checkin_AddCheckinRecord_Handler,
		},
		{
			MethodName: "UpdateCheckinRecord",
			Handler:    _Checkin_UpdateCheckinRecord_Handler,
		},
		{
			MethodName: "GetCheckinRecordByUserId",
			Handler:    _Checkin_GetCheckinRecordByUserId_Handler,
		},
		{
			MethodName: "AddIntegral",
			Handler:    _Checkin_AddIntegral_Handler,
		},
		{
			MethodName: "AddIntegralRecord",
			Handler:    _Checkin_AddIntegralRecord_Handler,
		},
		{
			MethodName: "UpdateIntegralRecord",
			Handler:    _Checkin_UpdateIntegralRecord_Handler,
		},
		{
			MethodName: "DelIntegralRecord",
			Handler:    _Checkin_DelIntegralRecord_Handler,
		},
		{
			MethodName: "GetIntegralRecordById",
			Handler:    _Checkin_GetIntegralRecordById_Handler,
		},
		{
			MethodName: "GetIntegralRecordByUserId",
			Handler:    _Checkin_GetIntegralRecordByUserId_Handler,
		},
		{
			MethodName: "SearchIntegralRecord",
			Handler:    _Checkin_SearchIntegralRecord_Handler,
		},
		{
			MethodName: "DeductIntegral",
			Handler:    _Checkin_DeductIntegral_Handler,
		},
		{
			MethodName: "AddTaskRecord",
			Handler:    _Checkin_AddTaskRecord_Handler,
		},
		{
			MethodName: "UpdateTaskRecord",
			Handler:    _Checkin_UpdateTaskRecord_Handler,
		},
		{
			MethodName: "DelTaskRecord",
			Handler:    _Checkin_DelTaskRecord_Handler,
		},
		{
			MethodName: "GetTaskRecordById",
			Handler:    _Checkin_GetTaskRecordById_Handler,
		},
		{
			MethodName: "GetTaskRecordByUserId",
			Handler:    _Checkin_GetTaskRecordByUserId_Handler,
		},
		{
			MethodName: "SearchTaskRecord",
			Handler:    _Checkin_SearchTaskRecord_Handler,
		},
		{
			MethodName: "AddTasks",
			Handler:    _Checkin_AddTasks_Handler,
		},
		{
			MethodName: "UpdateTasks",
			Handler:    _Checkin_UpdateTasks_Handler,
		},
		{
			MethodName: "DelTasks",
			Handler:    _Checkin_DelTasks_Handler,
		},
		{
			MethodName: "GetTasksById",
			Handler:    _Checkin_GetTasksById_Handler,
		},
		{
			MethodName: "SearchTasks",
			Handler:    _Checkin_SearchTasks_Handler,
		},
		{
			MethodName: "GetTaskProgress",
			Handler:    _Checkin_GetTaskProgress_Handler,
		},
		{
			MethodName: "UpdateSub",
			Handler:    _Checkin_UpdateSub_Handler,
		},
		{
			MethodName: "NoticeWishCheckin",
			Handler:    _Checkin_NoticeWishCheckin_Handler,
		},
		{
			MethodName: "GetIntegralByUserId",
			Handler:    _Checkin_GetIntegralByUserId_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "checkin.proto",
}
