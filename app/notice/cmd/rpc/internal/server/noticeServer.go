// Code generated by goctl. DO NOT EDIT.
// Source: notice.proto

package server

import (
	"context"

	"looklook/app/notice/cmd/rpc/internal/logic"
	"looklook/app/notice/cmd/rpc/internal/svc"
	"looklook/app/notice/cmd/rpc/pb"
)

type NoticeServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedNoticeServer
}

func NewNoticeServer(svcCtx *svc.ServiceContext) *NoticeServer {
	return &NoticeServer{
		svcCtx: svcCtx,
	}
}

func (s *NoticeServer) NoticeLotteryDraw(ctx context.Context, in *pb.NoticeLotteryDrawReq) (*pb.NoticeLotteryDrawResp, error) {
	l := logic.NewNoticeLotteryDrawLogic(ctx, s.svcCtx)
	return l.NoticeLotteryDraw(in)
}

func (s *NoticeServer) GetNoticeSubscribePreference(ctx context.Context, in *pb.GetNoticeSubscribePreferenceReq) (*pb.GetNoticeSubscribePreferenceResp, error) {
	l := logic.NewGetNoticeSubscribePreferenceLogic(ctx, s.svcCtx)
	return l.GetNoticeSubscribePreference(in)
}

func (s *NoticeServer) SaveNoticeSubscribePreference(ctx context.Context, in *pb.SaveNoticeSubscribePreferenceReq) (*pb.SaveNoticeSubscribePreferenceResp, error) {
	l := logic.NewSaveNoticeSubscribePreferenceLogic(ctx, s.svcCtx)
	return l.SaveNoticeSubscribePreference(in)
}

func (s *NoticeServer) NoticeLotteryStart(ctx context.Context, in *pb.NoticeLotteryStartReq) (*pb.NoticeLotteryStartResp, error) {
	l := logic.NewNoticeLotteryStartLogic(ctx, s.svcCtx)
	return l.NoticeLotteryStart(in)
}

func (s *NoticeServer) NoticeWishSign(ctx context.Context, in *pb.NoticeWishSignInReq) (*pb.NoticeWishSignInResp, error) {
	l := logic.NewNoticeWishSignLogic(ctx, s.svcCtx)
	return l.NoticeWishSign(in)
}
