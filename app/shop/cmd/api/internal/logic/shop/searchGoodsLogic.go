package shop

import (
	"context"
	"errors"
	"fmt"
	"looklook/common/ctxdata"
	"looklook/common/tool"

	"looklook/app/shop/cmd/api/internal/svc"
	"looklook/app/shop/cmd/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SearchGoodsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSearchGoodsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchGoodsLogic {
	return &SearchGoodsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SearchGoodsLogic) SearchGoods(req *types.GoodsSearchReq) (resp string, err error) {
	// todo: add your logic here and delete this line
	// 1、参数校验
	if req.Page == 0 {
		req.Page = 1
	}
	if req.Size == 0 {
		req.Size = 10
	}
	if req.Keyword == "" {
		return "", errors.New("请输入关键字")
	}

	// 2、 获取uid
	userId := ctxdata.GetUidFromCtx(l.ctx)

	// 3、调用多多进宝的搜索商品接口，根据关键字搜索商品 http调用 pdd.ddk.goods.search
	params := map[string]string{
		"custom_parameters": fmt.Sprintf("{\"uid\":%d}", userId),
		"type":              "pdd.ddk.goods.search",
		"keyword":           req.Keyword,
		"page":              fmt.Sprintf("%d", req.Page),
		"page_size":         fmt.Sprintf("%d", req.Size),
		"pid":               "42316735_301157319",
	}

	body, err := tool.SendRequestToDDJB(params, "POST")
	if err != nil {
		return "", err
	}

	// 打印响应体
	logx.Errorf("Testt-7 Response body: %s", string(body))

	return string(body), nil
}
