package shop

import (
	"context"
	"encoding/json"
	"looklook/app/shop/cmd/rpc/shop"
	"looklook/common/ctxdata"
	"time"

	"looklook/app/shop/cmd/api/internal/svc"
	"looklook/app/shop/cmd/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type WishExchangeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewWishExchangeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WishExchangeLogic {
	return &WishExchangeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WishExchangeLogic) WishExchange(req *types.WishExchangeReq) (resp *types.WishExchangeResp, err error) {
	// todo: add your logic here and delete this line
	// 1、uid获取
	uid := ctxdata.GetUidFromCtx(l.ctx)
	// 2、调用rpc接口

	extra := map[string]interface{}{
		"goods_name": req.GoodsName,
		"create_at":  time.Now().Unix(),
		"goods_sign": req.GoodsSign,
	}

	extraStr, err := json.Marshal(extra)

	_, err = l.svcCtx.ShopRpc.WishExchange(l.ctx, &shop.GoodsWishReq{
		UserId:    uid,
		GoodsSign: req.GoodsSign,
		Integral:  req.Integral,
		Extra:     string(extraStr),
	})
	if err != nil {
		return nil, err
	}
	return &types.WishExchangeResp{
		Success: true,
	}, nil
}
