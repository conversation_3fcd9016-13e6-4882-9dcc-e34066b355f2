package model

import (
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

var _ GoodsModel = (*customGoodsModel)(nil)

type (
	// GoodsModel is an interface to be customized, add more methods here,
	// and implement the added methods in customGoodsModel.
	GoodsModel interface {
		goodsModel
		GetGoodsListByLastId(lastId int64, size int) ([]*Goods, error)
	}

	customGoodsModel struct {
		*defaultGoodsModel
	}
)

// NewGoodsModel returns a model for the database table.
func NewGoodsModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) GoodsModel {
	return &customGoodsModel{
		defaultGoodsModel: newGoodsModel(conn, c, opts...),
	}
}

func (m *customGoodsModel) GetGoodsListByLastId(lastId int64, size int) ([]*Goods, error) {
	var resp []*Goods
	err := m.QueryRowsNoCache(&resp, "select * from goods where id > ? order by id limit ?", lastId, size)
	return resp, err
}
