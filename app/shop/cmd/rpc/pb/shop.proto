syntax = "proto3";

package stream;

option go_package = "./pb";

//message Goods {
//  int64 id = 1;
//  string goods_id = 2;
//  int64 category_id = 3;
//  double precoupon_price = 4;
//  double aftercoupon_price = 5;
//  string goods_desc = 6;
//  int64 wish_points = 7;
//  int64 coupon_start_time = 8;
//  int64 coupon_end_time = 9;
//  int64 coupon_discount = 10;
//  int64 coupon_remain_quantity = 11;
//}
//
//message GoodsReq {
//  int64 id = 1;
//}
//message GoodsListReq{
//  int64 pageSize = 1;//分页查询
//}
//message GoodsResp {
//  int64 id = 1;
//  string goods_id = 2;
//  int64 category_id = 3;
//  double precoupon_price = 4;
//  double aftercoupon_price = 5;
//  string goods_desc = 6;
//  int64 wish_points = 7;
//  int64 coupon_start_time = 8;
//  int64 coupon_end_time = 9;
//  int64 coupon_discount = 10;
//  int64 coupon_remain_quantity = 11;
//}
//message GoodsListResp{
//  repeated Goods goods = 1; // 商品信息列表
//}
//service Shop {
//  rpc getGoodsById(GoodsReq) returns (GoodsResp);
//  rpc getGoodsList(GoodsListReq) returns(GoodsListResp); //传入页号
//}

// 商品基本信息
message GoodsInfo {
  string category_name = 1;
  int64 coupon_remain_quantity = 2;
  int64 extra_coupon_amount = 3;
  int64 promotion_rate = 4;
  int64 subsidy_goods_type = 5;
  repeated int64 cat_ids = 6;
  int64 coupon_min_order_amount = 7;
  string category_id = 8;
  int64 mall_id = 9;
  int64 subsidy_amount = 10;
  int64 coupon_price = 11;
  string mall_name = 12;
  int64 coupon_total_quantity = 13;
  int64 market_fee = 14;
  string merchant_type = 15;
  string lgst_txt = 16;
  string goods_name = 17;
  string sales_tip = 18;
  bool has_material = 19;
  int64 goods_id = 20;
  int64 predict_promotion_rate = 21;
  string desc_txt = 22;
  string goods_desc = 23;
  string opt_name = 24;
  string realtime_sales_tip = 25;
  int64 share_rate = 26;
  string goods_thumbnail_url = 27;
  repeated int64 opt_ids = 28;
  string opt_id = 29;
  string search_id = 30;
  string goods_image_url = 31;
  repeated int64 activity_tags = 32;
  bool has_coupon = 33;
  int64 min_normal_price = 34;
  string serv_txt = 35;
  repeated string unified_tags = 36;
  int64 coupon_start_time = 37;
  int64 min_group_price = 38;
  int64 coupon_discount = 39;
  string goods_sign = 40;
  int64 coupon_end_time = 41;
}

// 商品详情
message GoodsDetail {
  int64 id = 1;
  string category_name = 2;
  int64 coupon_remain_quantity = 3;
  int64 promotion_rate = 4;
  int64 subsidy_goods_type = 5;
  repeated int64 service_tags = 6;
  int64 mall_id = 7;
  string mall_name = 8;
  int64 mall_coupon_end_time = 9;
  string lgst_txt = 10;
  string goods_name = 11;
  repeated string goods_gallery_urls = 12;
  int64 goods_id = 13;
  string brand_name = 14;
  int64 predict_promotion_rate = 15;
  string goods_desc = 16;
  string opt_name = 17;
  int64 share_rate = 18;
  repeated int64 opt_ids = 19;
  string goods_image_url = 20;
  string mall_img_url = 21;
  bool has_mall_coupon = 22;
  repeated string unified_tags = 23;
  repeated string video_urls = 24;
  int64 coupon_start_time = 25;
  int64 min_group_price = 26;
  int64 coupon_discount = 27;
  int64 coupon_end_time = 28;
  int64 zs_duo_id = 29;
  int64 mall_coupon_remain_quantity = 30;
  int64 plan_type = 31;
  int64 extra_coupon_amount = 32;
  repeated string material_list = 33;
  repeated int64 cat_ids = 34;
  int64 coupon_min_order_amount = 35;
  int64 category_id = 36;
  int64 mall_coupon_discount_pct = 37;
  int64 cat_id = 38;
  int64 coupon_total_quantity = 39;
  int64 mall_coupon_min_order_amount = 40;
  int64 merchant_type = 41;
  string sales_tip = 42;
  bool is_multi_group = 43;
  bool only_scene_auth = 44;
  string desc_txt = 45;
  string goods_thumbnail_url = 46;
  int64 opt_id = 47;
  repeated int64 activity_tags = 48;
  bool has_coupon = 49;
  int64 min_normal_price = 50;
  int64 mall_coupon_start_time = 51;
  string serv_txt = 52;
  int64 mall_coupon_total_quantity = 53;
  int64 mall_coupon_max_discount_amount = 54;
  int64 mall_cps = 55;
  string goods_sign = 56;
}

// 心愿兑换记录表
message WishExchange {
  int64 id = 1;
  int64 user_id = 2;
  int64 goods_url = 3;
  int64 integral = 4;
  string extra = 5;
  string goods_sign = 6;
}

// 订单表
message Order {
  int64 id = 1;
  int64 user_id = 2;
  int64 goods_sign = 3;
  int64 create_time = 4;
  string extra = 5;
}


// 商品列表
message GoodsListReq {
  int64 lastId = 1;
  int64 size = 2;
}

message GoodsListResp {
  int64 total = 1;
  repeated GoodsInfo list = 2;
}

// 搜索商品
message GoodsSearchReq {
  string keyword = 1;
  int64 pageSize = 2;
  int64 page = 3;
  int64 userId = 4;
}

message GoodsSearchResp {
  int64 total = 1;
  repeated GoodsInfo list = 2;
}

// 心愿兑换接口
message GoodsWishReq {
  string goodsSign = 1;
  int64 userId = 2;
  int64 integral = 3;
  string extra = 4;
}

message GoodsWishResp {
  bool success = 1;
}

// 心愿兑换列表
message GoodsWishListReq {
  int64 userId = 1;
  int64 pageSize = 2;
  int64 page = 3;
}

message GoodsWishListResp {
  int64 total = 1;
  repeated WishExchange list = 2;
}

// 订单列表
message OrderListReq {
  int64 userId = 1;
  int64 pageSize = 2;
  int64 page = 3;
}

message OrderListResp {
  int64 total = 1;
  repeated Order list = 2;
}

// 从多多进宝平台拉取订单信息
message OrderPullReq {
}

message OrderPullResp {
}







service Shop {
  rpc getGoodsList(GoodsListReq) returns(GoodsListResp);
  rpc searchGoods(GoodsSearchReq) returns(GoodsSearchResp);
  rpc wishExchange(GoodsWishReq) returns(GoodsWishResp);
  rpc getWishExchangeList(GoodsWishListReq) returns(GoodsWishListResp);
  rpc getOrderList(OrderListReq) returns(OrderListResp);
  rpc getOrderPull(OrderPullReq) returns(OrderPullResp);
}

