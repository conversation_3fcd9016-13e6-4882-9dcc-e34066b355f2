// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.1
// source: vote.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Vote_AddVoteConfig_FullMethodName           = "/pb.vote/AddVoteConfig"
	Vote_UpdateVoteConfig_FullMethodName        = "/pb.vote/UpdateVoteConfig"
	Vote_DelVoteConfig_FullMethodName           = "/pb.vote/DelVoteConfig"
	Vote_GetVoteConfigById_FullMethodName       = "/pb.vote/GetVoteConfigById"
	Vote_SearchVoteConfig_FullMethodName        = "/pb.vote/SearchVoteConfig"
	Vote_AddVoteRecord_FullMethodName           = "/pb.vote/AddVoteRecord"
	Vote_UpdateVoteRecord_FullMethodName        = "/pb.vote/UpdateVoteRecord"
	Vote_DelVoteRecord_FullMethodName           = "/pb.vote/DelVoteRecord"
	Vote_GetVoteRecordById_FullMethodName       = "/pb.vote/GetVoteRecordById"
	Vote_GetVoteRecordDetail_FullMethodName     = "/pb.vote/GetVoteRecordDetail"
	Vote_GetUserVoteRecordDetail_FullMethodName = "/pb.vote/GetUserVoteRecordDetail"
	Vote_SearchVoteRecord_FullMethodName        = "/pb.vote/SearchVoteRecord"
)

// VoteClient is the client API for Vote service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type VoteClient interface {
	// -----------------------投票表-----------------------
	AddVoteConfig(ctx context.Context, in *AddVoteConfigReq, opts ...grpc.CallOption) (*AddVoteConfigResp, error)
	UpdateVoteConfig(ctx context.Context, in *UpdateVoteConfigReq, opts ...grpc.CallOption) (*UpdateVoteConfigResp, error)
	DelVoteConfig(ctx context.Context, in *DelVoteConfigReq, opts ...grpc.CallOption) (*DelVoteConfigResp, error)
	GetVoteConfigById(ctx context.Context, in *GetVoteConfigByIdReq, opts ...grpc.CallOption) (*GetVoteConfigByIdResp, error)
	SearchVoteConfig(ctx context.Context, in *SearchVoteConfigReq, opts ...grpc.CallOption) (*SearchVoteConfigResp, error)
	// -----------------------投票记录表-----------------------
	AddVoteRecord(ctx context.Context, in *AddVoteRecordReq, opts ...grpc.CallOption) (*AddVoteRecordResp, error)
	UpdateVoteRecord(ctx context.Context, in *UpdateVoteRecordReq, opts ...grpc.CallOption) (*UpdateVoteRecordResp, error)
	DelVoteRecord(ctx context.Context, in *DelVoteRecordReq, opts ...grpc.CallOption) (*DelVoteRecordResp, error)
	GetVoteRecordById(ctx context.Context, in *GetVoteRecordByIdReq, opts ...grpc.CallOption) (*GetVoteRecordByIdResp, error)
	GetVoteRecordDetail(ctx context.Context, in *GetVoteRecordDetailReq, opts ...grpc.CallOption) (*GetVoteRecordDetailResp, error)
	GetUserVoteRecordDetail(ctx context.Context, in *GetUserVoteRecordDetailReq, opts ...grpc.CallOption) (*GetUserVoteRecordDetailResp, error)
	SearchVoteRecord(ctx context.Context, in *SearchVoteRecordReq, opts ...grpc.CallOption) (*SearchVoteRecordResp, error)
}

type voteClient struct {
	cc grpc.ClientConnInterface
}

func NewVoteClient(cc grpc.ClientConnInterface) VoteClient {
	return &voteClient{cc}
}

func (c *voteClient) AddVoteConfig(ctx context.Context, in *AddVoteConfigReq, opts ...grpc.CallOption) (*AddVoteConfigResp, error) {
	out := new(AddVoteConfigResp)
	err := c.cc.Invoke(ctx, Vote_AddVoteConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voteClient) UpdateVoteConfig(ctx context.Context, in *UpdateVoteConfigReq, opts ...grpc.CallOption) (*UpdateVoteConfigResp, error) {
	out := new(UpdateVoteConfigResp)
	err := c.cc.Invoke(ctx, Vote_UpdateVoteConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voteClient) DelVoteConfig(ctx context.Context, in *DelVoteConfigReq, opts ...grpc.CallOption) (*DelVoteConfigResp, error) {
	out := new(DelVoteConfigResp)
	err := c.cc.Invoke(ctx, Vote_DelVoteConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voteClient) GetVoteConfigById(ctx context.Context, in *GetVoteConfigByIdReq, opts ...grpc.CallOption) (*GetVoteConfigByIdResp, error) {
	out := new(GetVoteConfigByIdResp)
	err := c.cc.Invoke(ctx, Vote_GetVoteConfigById_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voteClient) SearchVoteConfig(ctx context.Context, in *SearchVoteConfigReq, opts ...grpc.CallOption) (*SearchVoteConfigResp, error) {
	out := new(SearchVoteConfigResp)
	err := c.cc.Invoke(ctx, Vote_SearchVoteConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voteClient) AddVoteRecord(ctx context.Context, in *AddVoteRecordReq, opts ...grpc.CallOption) (*AddVoteRecordResp, error) {
	out := new(AddVoteRecordResp)
	err := c.cc.Invoke(ctx, Vote_AddVoteRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voteClient) UpdateVoteRecord(ctx context.Context, in *UpdateVoteRecordReq, opts ...grpc.CallOption) (*UpdateVoteRecordResp, error) {
	out := new(UpdateVoteRecordResp)
	err := c.cc.Invoke(ctx, Vote_UpdateVoteRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voteClient) DelVoteRecord(ctx context.Context, in *DelVoteRecordReq, opts ...grpc.CallOption) (*DelVoteRecordResp, error) {
	out := new(DelVoteRecordResp)
	err := c.cc.Invoke(ctx, Vote_DelVoteRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voteClient) GetVoteRecordById(ctx context.Context, in *GetVoteRecordByIdReq, opts ...grpc.CallOption) (*GetVoteRecordByIdResp, error) {
	out := new(GetVoteRecordByIdResp)
	err := c.cc.Invoke(ctx, Vote_GetVoteRecordById_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voteClient) GetVoteRecordDetail(ctx context.Context, in *GetVoteRecordDetailReq, opts ...grpc.CallOption) (*GetVoteRecordDetailResp, error) {
	out := new(GetVoteRecordDetailResp)
	err := c.cc.Invoke(ctx, Vote_GetVoteRecordDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voteClient) GetUserVoteRecordDetail(ctx context.Context, in *GetUserVoteRecordDetailReq, opts ...grpc.CallOption) (*GetUserVoteRecordDetailResp, error) {
	out := new(GetUserVoteRecordDetailResp)
	err := c.cc.Invoke(ctx, Vote_GetUserVoteRecordDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *voteClient) SearchVoteRecord(ctx context.Context, in *SearchVoteRecordReq, opts ...grpc.CallOption) (*SearchVoteRecordResp, error) {
	out := new(SearchVoteRecordResp)
	err := c.cc.Invoke(ctx, Vote_SearchVoteRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VoteServer is the server API for Vote service.
// All implementations must embed UnimplementedVoteServer
// for forward compatibility
type VoteServer interface {
	// -----------------------投票表-----------------------
	AddVoteConfig(context.Context, *AddVoteConfigReq) (*AddVoteConfigResp, error)
	UpdateVoteConfig(context.Context, *UpdateVoteConfigReq) (*UpdateVoteConfigResp, error)
	DelVoteConfig(context.Context, *DelVoteConfigReq) (*DelVoteConfigResp, error)
	GetVoteConfigById(context.Context, *GetVoteConfigByIdReq) (*GetVoteConfigByIdResp, error)
	SearchVoteConfig(context.Context, *SearchVoteConfigReq) (*SearchVoteConfigResp, error)
	// -----------------------投票记录表-----------------------
	AddVoteRecord(context.Context, *AddVoteRecordReq) (*AddVoteRecordResp, error)
	UpdateVoteRecord(context.Context, *UpdateVoteRecordReq) (*UpdateVoteRecordResp, error)
	DelVoteRecord(context.Context, *DelVoteRecordReq) (*DelVoteRecordResp, error)
	GetVoteRecordById(context.Context, *GetVoteRecordByIdReq) (*GetVoteRecordByIdResp, error)
	GetVoteRecordDetail(context.Context, *GetVoteRecordDetailReq) (*GetVoteRecordDetailResp, error)
	GetUserVoteRecordDetail(context.Context, *GetUserVoteRecordDetailReq) (*GetUserVoteRecordDetailResp, error)
	SearchVoteRecord(context.Context, *SearchVoteRecordReq) (*SearchVoteRecordResp, error)
	mustEmbedUnimplementedVoteServer()
}

// UnimplementedVoteServer must be embedded to have forward compatible implementations.
type UnimplementedVoteServer struct {
}

func (UnimplementedVoteServer) AddVoteConfig(context.Context, *AddVoteConfigReq) (*AddVoteConfigResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddVoteConfig not implemented")
}
func (UnimplementedVoteServer) UpdateVoteConfig(context.Context, *UpdateVoteConfigReq) (*UpdateVoteConfigResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateVoteConfig not implemented")
}
func (UnimplementedVoteServer) DelVoteConfig(context.Context, *DelVoteConfigReq) (*DelVoteConfigResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelVoteConfig not implemented")
}
func (UnimplementedVoteServer) GetVoteConfigById(context.Context, *GetVoteConfigByIdReq) (*GetVoteConfigByIdResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVoteConfigById not implemented")
}
func (UnimplementedVoteServer) SearchVoteConfig(context.Context, *SearchVoteConfigReq) (*SearchVoteConfigResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchVoteConfig not implemented")
}
func (UnimplementedVoteServer) AddVoteRecord(context.Context, *AddVoteRecordReq) (*AddVoteRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddVoteRecord not implemented")
}
func (UnimplementedVoteServer) UpdateVoteRecord(context.Context, *UpdateVoteRecordReq) (*UpdateVoteRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateVoteRecord not implemented")
}
func (UnimplementedVoteServer) DelVoteRecord(context.Context, *DelVoteRecordReq) (*DelVoteRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelVoteRecord not implemented")
}
func (UnimplementedVoteServer) GetVoteRecordById(context.Context, *GetVoteRecordByIdReq) (*GetVoteRecordByIdResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVoteRecordById not implemented")
}
func (UnimplementedVoteServer) GetVoteRecordDetail(context.Context, *GetVoteRecordDetailReq) (*GetVoteRecordDetailResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVoteRecordDetail not implemented")
}
func (UnimplementedVoteServer) GetUserVoteRecordDetail(context.Context, *GetUserVoteRecordDetailReq) (*GetUserVoteRecordDetailResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserVoteRecordDetail not implemented")
}
func (UnimplementedVoteServer) SearchVoteRecord(context.Context, *SearchVoteRecordReq) (*SearchVoteRecordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchVoteRecord not implemented")
}
func (UnimplementedVoteServer) mustEmbedUnimplementedVoteServer() {}

// UnsafeVoteServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to VoteServer will
// result in compilation errors.
type UnsafeVoteServer interface {
	mustEmbedUnimplementedVoteServer()
}

func RegisterVoteServer(s grpc.ServiceRegistrar, srv VoteServer) {
	s.RegisterService(&Vote_ServiceDesc, srv)
}

func _Vote_AddVoteConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddVoteConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoteServer).AddVoteConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Vote_AddVoteConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoteServer).AddVoteConfig(ctx, req.(*AddVoteConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Vote_UpdateVoteConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateVoteConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoteServer).UpdateVoteConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Vote_UpdateVoteConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoteServer).UpdateVoteConfig(ctx, req.(*UpdateVoteConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Vote_DelVoteConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelVoteConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoteServer).DelVoteConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Vote_DelVoteConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoteServer).DelVoteConfig(ctx, req.(*DelVoteConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Vote_GetVoteConfigById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVoteConfigByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoteServer).GetVoteConfigById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Vote_GetVoteConfigById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoteServer).GetVoteConfigById(ctx, req.(*GetVoteConfigByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Vote_SearchVoteConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchVoteConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoteServer).SearchVoteConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Vote_SearchVoteConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoteServer).SearchVoteConfig(ctx, req.(*SearchVoteConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Vote_AddVoteRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddVoteRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoteServer).AddVoteRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Vote_AddVoteRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoteServer).AddVoteRecord(ctx, req.(*AddVoteRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Vote_UpdateVoteRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateVoteRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoteServer).UpdateVoteRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Vote_UpdateVoteRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoteServer).UpdateVoteRecord(ctx, req.(*UpdateVoteRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Vote_DelVoteRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelVoteRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoteServer).DelVoteRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Vote_DelVoteRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoteServer).DelVoteRecord(ctx, req.(*DelVoteRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Vote_GetVoteRecordById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVoteRecordByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoteServer).GetVoteRecordById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Vote_GetVoteRecordById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoteServer).GetVoteRecordById(ctx, req.(*GetVoteRecordByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Vote_GetVoteRecordDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVoteRecordDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoteServer).GetVoteRecordDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Vote_GetVoteRecordDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoteServer).GetVoteRecordDetail(ctx, req.(*GetVoteRecordDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Vote_GetUserVoteRecordDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserVoteRecordDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoteServer).GetUserVoteRecordDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Vote_GetUserVoteRecordDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoteServer).GetUserVoteRecordDetail(ctx, req.(*GetUserVoteRecordDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Vote_SearchVoteRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchVoteRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VoteServer).SearchVoteRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Vote_SearchVoteRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VoteServer).SearchVoteRecord(ctx, req.(*SearchVoteRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Vote_ServiceDesc is the grpc.ServiceDesc for Vote service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Vote_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.vote",
	HandlerType: (*VoteServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddVoteConfig",
			Handler:    _Vote_AddVoteConfig_Handler,
		},
		{
			MethodName: "UpdateVoteConfig",
			Handler:    _Vote_UpdateVoteConfig_Handler,
		},
		{
			MethodName: "DelVoteConfig",
			Handler:    _Vote_DelVoteConfig_Handler,
		},
		{
			MethodName: "GetVoteConfigById",
			Handler:    _Vote_GetVoteConfigById_Handler,
		},
		{
			MethodName: "SearchVoteConfig",
			Handler:    _Vote_SearchVoteConfig_Handler,
		},
		{
			MethodName: "AddVoteRecord",
			Handler:    _Vote_AddVoteRecord_Handler,
		},
		{
			MethodName: "UpdateVoteRecord",
			Handler:    _Vote_UpdateVoteRecord_Handler,
		},
		{
			MethodName: "DelVoteRecord",
			Handler:    _Vote_DelVoteRecord_Handler,
		},
		{
			MethodName: "GetVoteRecordById",
			Handler:    _Vote_GetVoteRecordById_Handler,
		},
		{
			MethodName: "GetVoteRecordDetail",
			Handler:    _Vote_GetVoteRecordDetail_Handler,
		},
		{
			MethodName: "GetUserVoteRecordDetail",
			Handler:    _Vote_GetUserVoteRecordDetail_Handler,
		},
		{
			MethodName: "SearchVoteRecord",
			Handler:    _Vote_SearchVoteRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "vote.proto",
}
